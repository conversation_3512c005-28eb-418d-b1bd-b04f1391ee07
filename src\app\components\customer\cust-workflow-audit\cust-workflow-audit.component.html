<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }} {{ custData.lastname }} Status
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="custStatus"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#custStatus"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="custStatus">
        <div class="panel-body table-responsive">
          <div class="progressbarWrap">
            <div
              *ngFor="let data of customerStatusDetail; last as isLast"
              [ngClass]="{
                complete: data.status == 'Approved',
                progressdv: data.status == 'Pending'
              }"
              class="circleWrap"
            >
              <div class="circledv completedWorkFlowClass">
                <i *ngIf="data.status == 'Approved'" class="fa fa-check"></i>
              </div>
              <p>{{ data.teamName }}</p>
              <div *ngIf="!isLast" class="lines">
                <div class="line"></div>
              </div>
            </div>
          </div>
          <div style="margin: 20px">
            <h3>Workflow Audit</h3>
            <div class="table-responsive">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Customer Name</th>
                        <th>Action</th>
                        <th>Staff name</th>
                        <th>Remark</th>
                        <th>Action Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let data of workflowAuditData
                            | paginate
                              : {
                                  id: 'searchMasterPageData',
                                  itemsPerPage: MasteritemsPerPage,
                                  currentPage: currentPageMasterSlab,
                                  totalItems: MastertotalRecords
                                };
                          index as i
                        "
                      >
                        <td>
                          <div *ngIf="data.entityName">
                            {{ data.entityName }}
                          </div>
                          <div *ngIf="data.planName">
                            {{ data.planName }}
                          </div>
                        </td>
                        <td>
                          <div>
                            {{ data.action }}
                          </div>
                        </td>
                        <td>
                          <div>
                            {{ data.actionByName }}
                          </div>
                        </td>
                        <td>
                          <div>
                            {{ data.remark }}
                          </div>
                        </td>
                        <td>
                          <div>
                            {{ data.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <br />
                  <div class="pagination_Dropdown">
                    <pagination-controls
                      (pageChange)="pageChangedMasterList($event)"
                      directionLinks="true"
                      id="searchMasterPageData"
                      maxSize="10"
                      nextLabel=""
                      previousLabel=""
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        (onChange)="TotalItemPerPageWorkFlow($event)"
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
