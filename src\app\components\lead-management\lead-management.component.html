<div class="row">
  <div class="col-md-12">
    <div class="panel top mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">Lead Management</h3>
      </div>
      <div id="searchLeadM" class="panel-collapse collapse in">
        <div class="panel-body" *ngIf="listView">
          <div class="row">
            <div class="col-lg-2 col-md-2 m-b-3">
              <p-dropdown
                [options]="searchOptionSelect"
                optionValue="value"
                optionLabel="label"
                filter="true"
                filterBy="label"
                [(ngModel)]="searchOption"
                (ngModelChange)="selSearchOption($event)"
                placeholder="Select a search option"
              >
              </p-dropdown>
            </div>

            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'name'">
              <input
                id="namesearch"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                (keydown.enter)="searchLead()"
                placeholder="Enter Customer Name"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'mobile'">
              <input
                id="mobilesearch"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Mobile"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'leadSourceName'">
              <input
                id="leadSourcesearch"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Lead Source"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'serviceArea '">
              <input
                id="serviceArea"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Servicearea"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'Lead Assigne Name '">
              <input
                id="Lead Assigne Name"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter assigneeName"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'Branch '">
              <input
                id="Branch"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Branch"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'Partner '">
              <input
                id="Partner"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Partner"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'plangroupname '">
              <input
                id="plangroupname"
                (keydown.enter)="searchLead()"
                type="text"
                class="form-control"
                [(ngModel)]="searchDeatil"
                placeholder="Enter plangroupname"
              />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'createdBy'">
              <p-dropdown
                id="createdbysearch"
                [options]="getStaffUsers"
                optionValue="id"
                optionLabel="firstname"
                type="text"
                [(ngModel)]="searchDeatil"
                placeholder="Select Staff User"
              ></p-dropdown>
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'lastUpdateOn'">
              <!-- <input id="lastmodifiedonsearch" type="text" class="form-control" [(ngModel)]="searchDeatil"
                                placeholder="Select the date" /> -->
              <p-calendar
                inputId="date"
                [numberOfMonths]="3"
                [(ngModel)]="searchDeatil"
                placeholder="Select the date"
              >
              </p-calendar>
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchOption === 'status'">
              <p-dropdown
                id="leadstatussearch"
                [options]="leadStatusOptions"
                type="text"
                [(ngModel)]="searchDeatil"
                placeholder="Select Any Status"
              ></p-dropdown>
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchDeatil === 'Converted'">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="dd/mm/yy"
                [(ngModel)]="fromConvertedDate"
                placeholder="Select From Converted Date"
              ></p-calendar>
              <br />
            </div>
            <div class="col-lg-2 col-md-2 m-b-3" *ngIf="searchDeatil === 'Converted'">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="dd/mm/yy"
                [(ngModel)]="toConvertedDate"
                placeholder="Enter Credit To Date"
              ></p-calendar>
              <br />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchLead()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="button"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchLead()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
        <div class="panel-heading">
          <div class="row panel-udata">
            <div
              class="col-md-6 pcolumn pcol"
              *ngIf="!isLeadDetailOpen && (createView || listView) && createAccess"
            >
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: createView
                }"
              >
                <a class="curson_pointer" (click)="checkExit('create')">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Create Lead</h5>
                </a>
              </div>
            </div>
            <div [ngClass]="isLeadDetailOpen ? 'col-md-3' : 'col-md-6'" class="pcolumn pcol">
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: listView
                }"
              >
                <a (click)="checkExit('list')" class="curson_pointer">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5 *ngIf="!isLeadDetailOpen && (createView || listView)">Search Lead</h5>
                  <h5 *ngIf="isLeadDetailOpen">Home</h5>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcolumn pcol" *ngIf="isLeadDetailOpen && leadAuditAccess">
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: openAuditTrailScreen
                }"
              >
                <a (click)="auditTrailScreenOpen(leadId)" class="curson_pointer">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Audit Trail</h5>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcolumn pcol" *ngIf="isLeadDetailOpen && statusAccess">
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: openLeadStatusScreen
                }"
              >
                <a (click)="viewLeadStatusPopupOpen(leadId)" class="curson_pointer">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Lead Status</h5>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcolumn pcol" *ngIf="isLeadDetailOpen && notesAccess">
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: openLeadNotesScreen
                }"
              >
                <a (click)="leadNotesScreenOpen('', leadId)" class="curson_pointer">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Lead Notes</h5>
                </a>
              </div>
            </div>
            <!-- <div class="col-md-3 pcolumn pcol" *ngIf="isLeadDetailOpen">
            <div
              class="dbox"
              [ngClass]="{
                activeSubMenu: isServiceManagementOpen
              }"
            >
              <a (click)="serviceManagementScreenOpen(leadId)" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Service Management</h5>
              </a>
            </div>
          </div> -->
            <!-- <div class="col-md-3 pcolumn pcol" *ngIf="isLeadDetailOpen">
            <div
              class="dbox"
              [ngClass]="{
                activeSubMenu: isQuotationDetailOpen
              }"
            >
              <a (click)="quotationManagementScreenOpen(leadId)" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Quotation Management</h5>
              </a>
            </div>
          </div> -->
            <div
              class="col-md-3 pcolumn pcol"
              *ngIf="
                myLead &&
                myLead?.nextApproveStaffId === this.staffid &&
                myLead?.leadStatus !== 'Converted' &&
                isLeadDetailOpen &&
                myFinalCheck
              "
            >
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: isLeadEdit
                }"
              >
                <a class="curson_pointer" (click)="editLead(leadId, true)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>LApprove & Convertead To CAF</h5>
                </a>
              </div>
            </div>
            <div
              class="col-md-3 pcolumn pcol"
              *ngIf="
                followUpAccess &&
                myLead &&
                myLead?.nextApproveStaffId === this.staffid &&
                myLead?.leadStatus !== 'Converted' &&
                myLead?.leadStatus !== 'Rejected' &&
                isLeadDetailOpen
              "
            >
              <div
                class="dbox"
                [ngClass]="{
                  activeSubMenu: openFollowUpSchedulling
                }"
              >
                <a (click)="followUpScheduleScreenOpen(leadId)" class="curson_pointer">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Follow Up</h5>
                </a>
              </div>
            </div>

            <!-- <div
                class="col-md-3 pcolumn pcol"
                *ngIf="
                  myLead &&
                  myLead?.nextApproveStaffId === this.staffid &&
                  myLead?.leadStatus !== 'Converted' &&
                  isLeadDetailOpen &&
                  !myFinalCheck
                "
              >
                <div
                  class="dbox"
                  [ngClass]="{
                    activeSubMenu: isLeadEdit,
                    inactiveSubMenu: !isLeadEdit
                  }"
                >
                  <a class="curson_pointer" (click)="editLead(leadId, false)">
                    <img src="../../../assets/img/i01.png" style="width: 32px" />
                    <h5>Manage Lead</h5>
                  </a>
                </div>
              </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12 col-sm-12" *ngIf="createView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title" *ngIf="!isLeadEdit && !myFinalCheck">Create a lead</h3>
        <h3 class="panel-title" *ngIf="isLeadEdit && !myFinalCheck">Update a lead</h3>
        <h3 class="panel-title" *ngIf="myFinalCheck">Approve & Convert Lead To CAF</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#leadDetails"
            aria-expanded="false"
            aria-controls="leadDetails"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="leadDetails" class="panel-collapse collapse in">
        <div class="panel-body">
          <form [formGroup]="customerGroupForm" (keydown.enter)="$event.preventDefault()">
            <!-- Lead Basic Details -->
            <fieldset style="margin-top: 1.5rem">
              <legend>Basic Lead Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <!--        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label style="margin-bottom: 2px">LeadNo *</label>
                    <input
                      disabled
                      class="form-control"
                      name="leadNo"
                      id="leadNo"
                      formControlName="leadNo"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.leadNo.errors
                      }"
                      placeholder="Enter the leadNo"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.leadNo.errors"
                    >
                      <div class="error text-danger">LeadNo is required.</div>
                    </div>
                  </div>-->
                  <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Customer Type*</label>
                    <p-dropdown
                      id="custtype"
                      [options]="CustomerTypeValue"
                      filter="true"
                      filterBy="label"
                      formControlName="custlabel"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Customer Type"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.custlabel.errors"
                    >
                      <div class="error text-danger">Customer Type is required.</div>
                    </div>
                  </div> -->
                  <div *ngIf="mvnoId === 1" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>{{ mvnoTitle }} List*</label>
                    <p-dropdown
                      id="mvnoId"
                      [disabled]="isLeadEdit"
                      [options]="commondropdownService.mvnoList"
                      filter="true"
                      filterBy="name"
                      formControlName="mvnoId"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a mvno"
                      (onChange)="mvnoChange($event)"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.mvnoId.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && customerGroupForm.controls.mvnoId.errors.required"
                        class="error text-danger"
                      >
                        Mvno is required.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Customer Category *</label>
                    <p-dropdown
                      id="custcategory"
                      [options]="commondropdownService.dunningRules"
                      formControlName="dunningCategory"
                      optionLabel="value"
                      optionValue="value"
                      placeholder="Select customer category"
                    >
                    </p-dropdown>
                    <div
                      *ngIf="submitted && this.customerGroupForm.controls.dunningCategory.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="
                          submitted &&
                          this.customerGroupForm.controls.dunningCategory.errors.required
                        "
                        class="error text-danger"
                      >
                        Customer category required.
                      </div>
                    </div>
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Lead Customer Type *</label>
                    <p-dropdown
                      #ddlCustomerType
                      (onChange)="onCustomerTypeChange(ddlCustomerType)"
                      [filter]="true"
                      [options]="leadcustTypeList"
                      optionValue="value"
                      optionLabel="label"
                      formControlName="custtype"
                      placeholder="Select Customer Type"
                      id="value"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.custtype.errors
                      }"
                      [disabled]="ifReadonlyExtingInput || isLeadEdit"
                    >
                    </p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.custtype.errors"
                    >
                      <div class="error text-danger">Lead Customer Type is required.</div>
                    </div>
                  </div>
                  <!-- </div> -->
                  <!-- <div class="row"> -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Lead Customer Sector*</label>

                    <p-dropdown
                      (onChange)="getSelectCustomerSector($event)"
                      [options]="CustomerSector"
                      filter="true"
                      filterBy="text"
                      formControlName="leadCustomerSector"
                      optionLabel="text"
                      optionValue="text"
                      placeholder="Select lead customer sector"
                      [ngClass]="{
                        invalid: submitted && customerGroupForm.controls.leadCustomerSector.errors
                      }"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.leadCustomerSector.errors"
                    >
                      <div class="error text-danger">Lead Customer Sector Is required</div>
                    </div>
                  </div>

                  <!-- </div> -->
                  <!-- <div class="row"> -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>Require Service Type</label>
                    <p-dropdown
                      [filter]="true"
                      id="requireServiceType"
                      [options]="requireServiceTypes"
                      formControlName="requireServiceType"
                      placeholder="Select Required Service Type"
                    >
                    </p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>Lead Type</label>
                    <p-dropdown
                      [filter]="true"
                      name="leadType"
                      id="leadType"
                      formControlName="leadType"
                      [options]="leadTypes"
                      placeholder="Select a Lead Type"
                    >
                    </p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Lead Category *</label>
                    <p-dropdown
                      [options]="leadCategoryist"
                      formControlName="leadCategory"
                      placeholder="Select Lead Category"
                      optionLabel="label"
                      optionValue="label"
                      [filter]="true"
                      id="leadCategory"
                      filterBy="label"
                      (onChange)="
                        selectLeadCategory(
                          customerGroupForm.controls.leadCategory.value,
                          customerGroupForm.controls.mobile.value
                        )
                      "
                      [disabled]="ifReadonlyExtingInput && isLeadEdit"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.leadCategory.errors
                      }"
                    >
                    </p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.leadCategory.errors"
                    >
                      <div class="error text-danger">Lead Category is required.</div>
                    </div>
                  </div>
                  <!-- </div> -->
                  <!-- <div class="row"> -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>Lead Origin Type</label>
                    <p-dropdown
                      [filter]="true"
                      id="leadOriginType"
                      [options]="leadOriginTypes"
                      formControlName="leadOriginType"
                      placeholder="Select Lead Origin Type"
                    >
                    </p-dropdown>
                  </div>
                  <!-- </div> -->
                  <!-- <div class="row"> -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Lead Source *</label>
                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.leadSourceId.errors
                      }"
                      [options]="leadSourceArr"
                      filter="true"
                      filterBy="leadSourceName"
                      formControlName="leadSourceId"
                      optionLabel="leadSourceName"
                      optionValue="id"
                      placeholder="Select a lead source"
                      (onChange)="selectLeadSource($event.value)"
                      [disabled]="isLeadEdit"
                    ></p-dropdown>
                    <!-- <p-dropdown
                      [options]="leadSourceArr"
                      optionLabel="leadSourceName"
                      optionValue="id"
                      [filter]="true"
                      name="leadSourceId"
                      id="leadSourceId"
                      formControlName="leadSourceId"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.leadSourceId.errors
                      }"
                      placeholder="Select a lead source"
                      (ngModelChange)="
                        selectLeadSource(customerGroupForm.controls.leadSourceId.value)
                      "
                    >
                    </p-dropdown> -->
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.leadSourceId.errors"
                    >
                      <div class="error text-danger">Lead Source is required.</div>
                    </div>
                  </div>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    style="margin-right: -1px"
                  >
                    <label>Feasibility *</label>
                    <p-dropdown
                      name="feasibility"
                      id="feasibility"
                      [options]="feasibilityOptions"
                      (ngModelChange)="
                        selectFeasibility(customerGroupForm.controls.feasibility.value)
                      "
                      formControlName="feasibility"
                      placeholder="Select the feasibility check..."
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.feasibility.errors
                      }"
                    >
                    </p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.feasibility.errors"
                    >
                      <div class="error text-danger">Lead Feasibility is required.</div>
                    </div>
                  </div>
                  <!-- </div>

                <div class="row"> -->
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    style="margin-right: 1px"
                    *ngIf="isMandatory"
                  >
                    <label>Department </label>
                    <p-dropdown
                      [options]="departmentListData"
                      formControlName="leadDepartment"
                      placeholder="Select Lead Category"
                      optionLabel="displayName"
                      optionValue="name"
                      [filter]="true"
                      id="department"
                      [disabled]="ifReadonlyExtingInput && isLeadEdit"
                    >
                    </p-dropdown>
                  </div>

                  <div
                    *ngIf="this.customerGroupForm.controls.leadCustomerSubSector.enabled"
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                  ></div>

                  <div
                    *ngIf="this.customerGroupForm.controls.leadCustomerSubSector.enabled"
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                  ></div>

                  <!-- <div *ngIf="mvnoId === 1" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>{{ mvnoTitle }} List*</label>
                    <p-dropdown
                      id="mvnoId"
                      [disabled]="isLeadEdit"
                      [options]="commondropdownService.mvnoList"
                      filter="true"
                      filterBy="name"
                      formControlName="mvnoId"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a mvno"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.mvnoId.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && customerGroupForm.controls.mvnoId.errors.required"
                        class="error text-danger"
                      >
                        Mvno is required.
                      </div>
                    </div>
                  </div> -->
                  <ng-container *ngIf="isMandatory">
                    <div
                      class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                      [ngClass]="{
                        invalid: submitted && customerGroupForm.controls.heardAboutSubisuFrom.errors
                      }"
                    >
                      <label>Heard about Organization From</label>
                      <textarea
                        class="form-control"
                        name="heardAboutSubisuFrom"
                        id="heardAboutSubisuFrom"
                        formControlName="heardAboutSubisuFrom"
                        placeholder="Write something on organization from wherever you heard about..."
                      >
                      </textarea>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls.heardAboutSubisuFrom.errors"
                      >
                        <div class="error text-danger">Text limit is 500 characters only</div>
                      </div>
                    </div>

                    <div
                      class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                      *ngIf="this.customerGroupForm.controls.leadCustomerSubSector.enabled"
                    >
                      <label style="margin-bottom: 0px">Lead Customer sector type</label>
                      <input
                        class="form-control"
                        formControlName="leadCustomerSubSector"
                        id="leadCustomerSubSector"
                        placeholder="Enter lead customer sector type"
                        type="text"
                        [ngClass]="{
                          invalid:
                            submitted && customerGroupForm.controls.leadCustomerSubSector.errors
                        }"
                      />
                    </div>
                  </ng-container>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    *ngIf="customerGroupForm.value.leadSourceId"
                  >
                    <label>Lead Sub Source</label>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Customer'"
                      [filter]="true"
                      name="leadCustomerId"
                      id="leadCustomerId"
                      optionValue="id"
                      optionLabel="firstname"
                      formControlName="leadCustomerId"
                      [options]="commondropdownService.customersFromSalesCRMS"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadCustomerId"
                    >
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Branch'"
                      [filter]="true"
                      name="leadBranchId"
                      id="leadBranchId"
                      optionValue="id"
                      optionLabel="name"
                      formControlName="leadBranchId"
                      [options]="commondropdownService.branchesFromSalesCRMS"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadBranchId"
                    >
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Partner'"
                      [filter]="true"
                      name="leadPartnerId"
                      id="leadPartnerId"
                      optionValue="id"
                      optionLabel="name"
                      formControlName="leadPartnerId"
                      [options]="commondropdownService.partnersFromSalesCRMS"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadPartnerId"
                    >
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Staff'"
                      [filter]="true"
                      filterBy="firstname"
                      name="leadStaffId"
                      id="leadStaffId"
                      optionValue="id"
                      formControlName="leadStaffId"
                      [options]="commondropdownService.staffsFromSalesCRMS"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadStaffId"
                    >
                      <ng-template let-item pTemplate="optionLabel">
                        {{ item.firstname + " " + item.lastname }}
                      </ng-template>
                      <ng-template let-item pTemplate="selectedItem">
                        {{ item.firstname + " " + item.lastname }}
                      </ng-template>
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Outlet/ SA'"
                      [filter]="true"
                      name="leadServiceAreaId"
                      id="leadServiceAreaId"
                      optionValue="id"
                      optionLabel="name"
                      formControlName="leadServiceAreaId"
                      [options]="commondropdownService.serviceAreasFromSalesCRMS"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadServiceAreaId"
                    >
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="!myViewFlag && leadSourceTitle === 'Agent'"
                      [filter]="true"
                      name="leadAgentId"
                      id="leadAgentId"
                      optionValue="id"
                      optionLabel="name"
                      formControlName="leadAgentId"
                      [options]="agentArr"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadAgentId"
                    >
                    </p-dropdown>
                    <p-dropdown
                      *ngIf="myViewFlag"
                      [filter]="true"
                      name="leadSubSourceId"
                      id="leadSubSourceName"
                      optionValue="id"
                      optionLabel="name"
                      formControlName="leadSubSourceId"
                      [options]="leadSubSourceArr"
                      placeholder="Select a lead sub source"
                      [disabled]="isLeadEdit && customerGroupForm.value.leadSubSourceId"
                    >
                    </p-dropdown>
                  </div>

                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    style="margin-right: 1px"
                    *ngIf="customerGroupForm.controls.feasibility.value === 'N/A'"
                  >
                    <label>Feasibility Remark *</label>
                    <input
                      class="form-control"
                      name="feasibilityRemark"
                      id="feasibilityRemark"
                      formControlName="feasibilityRemark"
                      [ngClass]="{
                        'is-invalid':
                          submitted && customerGroupForm.controls.feasibilityRemark.errors
                      }"
                      placeholder="Enter the feasibility remark..."
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.feasibilityRemark.errors"
                    >
                      <div class="error text-danger">Feasibility Remark is required.</div>
                    </div>
                  </div>
                </div>
              </div>

              <!--                  <div-->
              <!--                          class="errorWrap text-danger"-->
              <!--                          *ngIf="submitted && customerGroupForm.controls.leadCategory.errors"-->
              <!--                  >-->
              <!--                    <div class="error text-danger">Lead Category is required.</div>-->
              <!--                  &lt;!&ndash; -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;<div></div>&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; &ndash;&gt;-->
              <!--                </div>-->
              <!-- <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                    <label>Plan Type</label>
                    <p-dropdown
                      name="planType"
                      id="planType"
                      [options]="planTypeOptions"
                      (ngModelChange)="selectPlanType(customerGroupForm.controls.planType.value)"
                      formControlName="planType"
                      placeholder="Select the plan type..."
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.planType"
                    >
                    </p-dropdown>
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Previous Vendor</label>
                    <p-dropdown
                      [filter]="true"
                      id="previousVendor"
                      [options]="previousVendorList"
                      formControlName="previousVendor"
                      placeholder="Select Previous Vendor"
                    >
                    </p-dropdown>
                  </div>
                </div> -->
            </fieldset>

            <fieldset>
              <legend>Basic Customer Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>First Name *</label>
                    <input
                      id="firstname"
                      type="text"
                      class="form-control"
                      placeholder="Enter First Name"
                      formControlName="firstname"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.firstname.errors
                      }"
                      [readonly]="ifReadonlyExtingInput"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.firstname.errors"
                    >
                      <div class="error text-danger">First Name is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Last Name {{ myFinalCheck ? "*" : "" }}</label>
                    <input
                      id="lastname"
                      type="text"
                      class="form-control"
                      placeholder="Enter Last Name"
                      formControlName="lastname"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.lastname"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.lastname.errors"
                    >
                      <div class="error text-danger">Last Name is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Mobile *</label>
                    <div style="display: flex">
                      <div style="width: 30%">
                        <p-dropdown
                          [filter]="true"
                          id="countryCode"
                          [filter]="true"
                          [options]="countries"
                          optionLabel="dial_code"
                          optionValue="dial_code"
                          formControlName="countryCode"
                          placeholder="+977"
                          [disabled]="ifReadonlyExtingInput"
                        ></p-dropdown>
                      </div>
                      <div style="width: 70%">
                        <input
                          id="mobile"
                          type="text"
                          class="form-control"
                          placeholder="Enter Mobile"
                          formControlName="mobile"
                          [attr.maxlength]="commondropdownService.maxMobileLength"
                          [readonly]="ifReadonlyExtingInput"
                          [ngClass]="{
                            'is-invalid': submitted && customerGroupForm.controls.mobile.errors
                          }"
                        />

                        <div
                          *ngIf="submitted && customerGroupForm.controls.mobile.errors"
                          class="errorWrap text-danger"
                        >
                          <div *ngIf="customerGroupForm.controls.mobile.errors.required">
                            Mobile Number is required.
                          </div>

                          <div
                            *ngIf="
                              customerGroupForm.controls.mobile.errors.minlength ||
                              customerGroupForm.controls.mobile.errors.maxlength
                            "
                          >
                            <ng-container
                              *ngIf="
                                commondropdownService.minMobileLength ===
                                  commondropdownService.maxMobileLength;
                                else mobileRangeError
                              "
                            >
                              Mobile Number must be exactly
                              {{ commondropdownService.minMobileLength }} digits.
                            </ng-container>

                            <ng-template #mobileRangeError>
                              Mobile Number must be between
                              {{ commondropdownService.minMobileLength }} and
                              {{ commondropdownService.maxMobileLength }} digits.
                            </ng-template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Primary Email {{ myFinalCheck ? "*" : "" }}</label>
                    <input
                      id="email"
                      type="text"
                      class="form-control"
                      placeholder="Enter Email"
                      formControlName="email"
                      [readonly]="ifReadonlyExtingInput"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.email.errors"
                    >
                      <div class="error text-danger">Email is required.</div>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        (customerGroupForm.controls.email.touched ||
                          customerGroupForm.controls.email.dirty ||
                          submitted) &&
                        customerGroupForm.controls.email.errors
                      "
                    >
                      <div class="error text-danger">Email is not valid.</div>
                    </div>
                    <br />
                  </div>

                  <div class="left">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                      <label>Parent Customer</label>
                      <p-dropdown
                        [options]="commondropdownService.customerAllList"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        (onSelect)="getParentCust($event)"
                        [showClear]="true"
                        placeholder="Select a Parent Customer"
                        formControlName="parentCustomerId"
                        styleClass="disableDropdown"
                        [disabled]="true"
                      ></p-dropdown>
                      <button
                        type="button"
                        class="btn btn-primary"
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 10px;
                        "
                        type="button"
                        (click)="modalOpenParentCustomer('parent')"
                      >
                        <i class="fa fa-plus-square"></i>
                      </button>
                      <button
                        class="btn btn-danger"
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 10px;
                        "
                        (click)="removeSelParentCust('parent')"
                      >
                        <i class="fa fa-trash"></i>
                      </button>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Service Area * {{ myFinalCheck ? "" : "" }}</label>

                      <p-dropdown
                        (onChange)="selServiceArea($event)"
                        [ngClass]="{
                          'is-invalid': submitted && customerGroupForm.controls.serviceareaid.errors
                        }"
                        [options]="commondropdownService.serviceAreaList"
                        filter="true"
                        filterBy="name"
                        formControlName="serviceareaid"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Servicearea"
                        [disabled]="ifReadonlyExtingInput"
                      ></p-dropdown>

                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls.serviceareaid.errors"
                      >
                        <div class="error text-danger">Service area is required.</div>
                      </div>
                    </div>
                    <div
                      class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                      *ngIf="isBranchAvailable"
                    >
                      <label for="branchId">Branch/Partner *</label>
                      <p-dropdown
                        [options]="branchData"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Branch"
                        formControlName="branchId"
                        [ngClass]="{
                          'is-invalid': submitted && customerGroupForm.controls.branchId.errors
                        }"
                        [disabled]="customerGroupForm.value.branchId && ifReadonlyExtingInput"
                      >
                      </p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls.branchId.errors"
                      >
                        <div class="error text-danger">Branch Is required</div>
                      </div>
                    </div>
                    <div
                      class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                      *ngIf="!isBranchAvailable"
                    >
                      <label>Branch/Partner *</label>

                      <p-dropdown
                        [disabled]="partnerId !== 1"
                        [options]="partnerList"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Partner"
                        formControlName="partnerid"
                        [ngClass]="{
                          'is-invalid': submitted && customerGroupForm.controls.partnerid.errors
                        }"
                        [disabled]="ifReadonlyExtingInput"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls.partnerid.errors"
                      >
                        <div class="error text-danger">Partner is required.</div>
                      </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>Customer Gender *</label>
                      <p-dropdown
                        [filter]="true"
                        name="gender"
                        id="gender"
                        [options]="leadCustomerGenderTypes"
                        formControlName="gender"
                        placeholder="Select the Gender..."
                        [ngClass]="{
                          'is-invalid': submitted && customerGroupForm.controls.gender.errors
                        }"
                      >
                      </p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls.gender.errors"
                      >
                        <div class="error text-danger">Gender is required.</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    *ngIf="serviceAreaData?.serviceAreaType == 'private'"
                  >
                    <label>Unit No. *</label>
                    <p-dropdown
                      [options]="blockNoOptions"
                      formControlName="blockNo"
                      placeholder="Select a blockNo"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.blockNo.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">blockNo is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>TIN/PAN No.</label>
                    <input
                      id="pan"
                      class="form-control"
                      placeholder="Enter TIN/PAN No"
                      formControlName="pan"
                      [attr.maxlength]="commondropdownService.commonPanNumberLength"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.pan"
                      step=".01"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.pan.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && customerGroupForm.controls.pan.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            customerGroupForm.controls.pan.errors.minlength ||
                            customerGroupForm.controls.pan.errors.maxlength
                          "
                        >
                          TIN/PAN Number must be exactly
                          {{ commondropdownService.commonPanNumberLength }} characters.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>VAT</label>
                    <input
                      id="tinno"
                      type="text"
                      min="0"
                      class="lela"
                      class="form-control"
                      placeholder="Enter VAT"
                      formControlName="tinNo"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.tinNo"
                      maxlength="9"
                      step=".01"
                      (keypress)="keypressId($event)"
                    />
                  </div>

                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    *ngIf="this.customerGroupForm.value.parentCustomerId"
                  >
                    <label>Invoice Type *</label>
                    <p-dropdown
                      [options]="invoiceType"
                      optionValue="value"
                      optionLabel="label"
                      placeholder="Select a Invoice Type"
                      formControlName="invoiceType"
                    >
                    </p-dropdown>
                  </div>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    *ngIf="this.customerGroupForm.value.parentCustomerId"
                  >
                    <label>Parent Experience*</label>
                    <p-dropdown
                      [options]="parentExperience"
                      optionValue="value"
                      optionLabel="label"
                      placeholder="Select a Parent Expirence"
                      formControlName="parentExperience"
                    >
                    </p-dropdown>
                  </div>
                </div>
              </div>
            </fieldset>
            <!-- Present Address Details -->

            <fieldset>
              <legend>Present Address Details</legend>
              <div class="boxWhite">
                <div [formGroup]="presentGroupForm">
                  <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>Landmark {{ myFinalCheck ? "*" : "" }}</label>
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Enter Landmark"
                        name="landmark"
                        id="landmark"
                        formControlName="landmark"
                        [readonly]="ifReadonlyExtingInput && presentGroupForm.value.landmark"
                      />

                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.landmark.errors"
                      >
                        <div class="error text-danger">Landmark is required.</div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>{{ pincodeTitle }} {{ myFinalCheck ? "*" : "" }}</label>
                      <p-dropdown
                        *ngIf="ifReadonlyExtingInput && presentGroupForm.value.pincodeId"
                        [options]="pincodeDD"
                        optionValue="pincodeid"
                        optionLabel="pincode"
                        filter="true"
                        filterBy="pincode"
                        placeholder="Select a {{ pincodeTitle }}"
                        formControlName="pincodeId"
                        (onChange)="selectPINCODEChange($event, 'present')"
                        [disabled]="true"
                        [virtualScroll]="true"
                        [itemSize]="30"
                        [scrollHeight]="'200px'"
                      ></p-dropdown>
                      <p-dropdown
                        *ngIf="!ifReadonlyExtingInput"
                        [options]="pincodeDD"
                        optionValue="pincodeid"
                        optionLabel="pincode"
                        filter="true"
                        filterBy="pincode"
                        placeholder="Select a {{ pincodeTitle }}"
                        formControlName="pincodeId"
                        (onChange)="selectPINCODEChange($event, 'present')"
                        [disabled]="serviceareaCheck"
                        [virtualScroll]="true"
                        [itemSize]="30"
                        [scrollHeight]="'200px'"
                      ></p-dropdown>

                      <div *ngIf="serviceareaCheck" class="errorWrap text-danger">
                        <div class="error text-danger">Please select Service Area first!</div>
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.pincodeId.errors"
                      >
                        <div class="error text-danger">{{ pincodeTitle }} is required.</div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>{{ areaTitle }} {{ myFinalCheck ? "*" : "" }}</label>
                      <!-- <select class="form-control" style="width: 100%" name="areaId" id="areaId"
                                              formControlName="areaId" (change)="selectAreaChange($event, 'present')">
                                              <option value="">Select {{ areaTitle }}</option>
                                              <option *ngFor="let item of "
                                                  value="{{ item.id }}">
                                                  {{ item.name }}
                                              </option>
                                          </select> -->
                      <p-dropdown
                        *ngIf="ifReadonlyExtingInput && presentGroupForm.value.pincodeId"
                        [options]="AreaListDD"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a {{ areaTitle }}"
                        formControlName="areaId"
                        [virtualScroll]="true"
                        [itemSize]="30"
                        [scrollHeight]="'200px'"
                        (onChange)="selectAreaChange($event, 'present')"
                        [disabled]="!AreaListDD || AreaListDD.length === 0"
                      ></p-dropdown>
                      <p-dropdown
                        *ngIf="!ifReadonlyExtingInput"
                        [options]="AreaListDD"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a {{ areaTitle }}"
                        formControlName="areaId"
                        [virtualScroll]="true"
                        [itemSize]="30"
                        [scrollHeight]="'200px'"
                        (onChange)="selectAreaChange($event, 'present')"
                        [disabled]="!AreaListDD || AreaListDD.length === 0"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.areaId.errors"
                      >
                        <div class="error text-danger">{{ areaTitle }} is required.</div>
                      </div>
                    </div>
                    <ng-container *ngIf="isMandatory">
                      <div
                        class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        *ngIf="myFinalCheck && !ifReadonlyExtingInput"
                      >
                        <label>{{ subareaTitle }} </label>
                        <p-dropdown
                          *ngIf="!ifReadonlyExtingInput"
                          id="areatitl"
                          (onChange)="onChangeSubArea($event, 'present')"
                          [options]="subAreaListDD"
                          filter="true"
                          filterBy="name"
                          formControlName="subareaId"
                          id="subareaId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select a {{ subareaTitle }}"
                          [virtualScroll]="true"
                          [itemSize]="30"
                          [scrollHeight]="'200px'"
                        ></p-dropdown>
                      </div>
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                        <label>{{ buildingTitle }}</label>
                        <p-dropdown
                          id="areatitl"
                          (onChange)="onChangeBuildingArea($event, 'present')"
                          [options]="buildingListDD"
                          filter="true"
                          filterBy="buildingName"
                          formControlName="building_mgmt_id"
                          id="building_mgmt_id"
                          optionLabel="buildingName"
                          optionValue="buildingMgmtId"
                          placeholder="Select a {{ buildingTitle }}"
                          [virtualScroll]="true"
                          [itemSize]="30"
                          [scrollHeight]="'200px'"
                          [disabled]="iscustomerEdit"
                        ></p-dropdown>
                      </div>
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                        <label>Building Number</label>
                        <p-dropdown
                          id="areatitl"
                          [options]="buildingNoDD"
                          filter="true"
                          filterBy="buildingNumber"
                          formControlName="buildingNumber"
                          id="buildingNumber"
                          optionLabel="buildingNumber"
                          optionValue="buildingNumber"
                          placeholder="Select a Building Number"
                          [virtualScroll]="true"
                          [itemSize]="30"
                          [scrollHeight]="'200px'"
                          [disabled]="iscustomerEdit"
                        ></p-dropdown>
                      </div>
                    </ng-container>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>{{ cityTitle }} {{ myFinalCheck ? "*" : "" }}</label>
                      <select
                        class="form-control"
                        style="width: 100%"
                        name="cityId"
                        id="cityId"
                        formControlName="cityId"
                        disabled
                      >
                        <option value="">Select {{ cityTitle }}</option>
                        <option
                          *ngFor="let item of commondropdownService.cityListData"
                          value="{{ item.id }}"
                        >
                          {{ item.name }}
                        </option>
                      </select>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.cityId.errors"
                      >
                        <div class="error text-danger">{{ cityTitle }} is required.</div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>{{ stateTitle }} {{ myFinalCheck ? "*" : "" }}</label>
                      <select
                        class="form-control"
                        style="width: 100%"
                        name="stateId"
                        id="stateId"
                        formControlName="stateId"
                        disabled
                      >
                        <option value="">Select {{ stateTitle }}</option>
                        <option
                          *ngFor="let item of commondropdownService.stateListData"
                          value="{{ item.id }}"
                        >
                          {{ item.name }}
                        </option>
                      </select>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.stateId.errors"
                      >
                        <div class="error text-danger">{{ stateTitle }} is required.</div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                      <label>{{ countryTitle }} {{ myFinalCheck ? "*" : "" }}</label>
                      <select
                        class="form-control"
                        style="width: 100%"
                        name="countryId"
                        id="countryId"
                        formControlName="countryId"
                        disabled
                      >
                        <option value="">Select {{ countryTitle }}</option>
                        <option
                          *ngFor="let item of commondropdownService.countryListData"
                          value="{{ item.id }}"
                        >
                          {{ item.name }}
                        </option>
                      </select>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && presentGroupForm.controls.countryId.errors"
                      >
                        <div class="error text-danger">{{ countryTitle }} is required.</div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top" *ngIf="isMandatory">
                      <label>Street Name</label>
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Enter Street Name"
                        name="streetName"
                        id="streetName"
                        formControlName="streetName"
                        [readonly]="ifReadonlyExtingInput && presentGroupForm.value.streetName"
                      />
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top" *ngIf="isMandatory">
                      <label>House No</label>
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Enter House No"
                        name="houseNo"
                        id="houseNo"
                        formControlName="houseNo"
                        [readonly]="ifReadonlyExtingInput && presentGroupForm.value.houseNo"
                      />
                      <br />
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>Valley Type</label>
                    <p-dropdown
                      [options]="commondropdownService.valleyType"
                      filter="true"
                      filterBy="text"
                      formControlName="valleyType"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Valley Type"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.valleyType"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="
                      customerGroupForm.controls.valleyType.value == 'Outside Valley' && isMandatory
                    "
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                  >
                    <label>Outside Valley</label>
                    <p-dropdown
                      [options]="commondropdownService.outsideValley"
                      filter="true"
                      filterBy="text"
                      formControlName="outsideValley"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Outside Valley"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="
                      customerGroupForm.controls.valleyType.value == 'Inside Valley' && isMandatory
                    "
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                  >
                    <label>Inside Valley</label>
                    <p-dropdown
                      [options]="commondropdownService.insideValley"
                      filter="true"
                      filterBy="text"
                      formControlName="insideValley"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Inside Valley"
                    ></p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Customer Latitude</label>
                    <input
                      id="latitude"
                      type="text"
                      class="form-control"
                      placeholder="Enter latitude"
                      formControlName="latitude"
                      [readonly]="
                        iflocationFill ||
                        (ifReadonlyExtingInput && customerGroupForm.value.latitude)
                      "
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Customer Longitude</label>
                    <input
                      id="longitude"
                      type="text"
                      class="form-control"
                      placeholder="Enter longitude"
                      formControlName="longitude"
                      [readonly]="
                        iflocationFill ||
                        (ifReadonlyExtingInput && customerGroupForm.value.longitude)
                      "
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
                    <div style="margin-bottom: 1rem">
                      <span
                        class="HoverEffect"
                        (click)="mylocation()"
                        title="Get Current Location"
                        style="border-bottom: 1px solid #f7b206"
                      >
                        <img
                          class="LocationIcon LocationIconMargin"
                          src="assets/img/B_Find-My-current-location_Y.png"
                        />
                      </span>
                      <span
                        class="HoverEffect"
                        title="Search Location"
                        data-target="#searchLocationModal"
                        data-toggle="modal"
                        data-backdrop="static"
                        data-keyboard="false"
                        style="margin-left: 8px; border-bottom: 1px solid #f7b206"
                        (click)="openSearchModel()"
                      >
                        <img
                          class="LocationIcon LocationIconMargin"
                          src="assets/img/C_Search-location_Y.png"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
            <!-- plan Details -->

            <fieldset>
              <legend>Plan Details</legend>
              <div class="boxWhite">
                <div class="row" [formGroup]="planDataForm">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Plan Offer Price</label>
                    <input
                      class="form-control"
                      placeholder="Enter Plan Offer Price "
                      type="number"
                      formControlName="offerPrice"
                      readonly
                    />
                    <br />
                  </div>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top"
                    *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                  >
                    <label>New Price (with Discount)</label>
                    <input
                      class="form-control"
                      placeholder="Enter a Price "
                      type="number"
                      formControlName="discountPrice"
                      (keypress)="discountvaluesetPercentage($event)"
                      [readonly]="!ifcustomerDiscountField"
                    />
                    <br />
                  </div>
                </div>

                <div class="row">
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    [formGroup]="planCategoryForm"
                  >
                    <label>Plan Category {{ myFinalCheck ? "*" : "" }}</label>
                    <p-dropdown
                      [options]="planDetailsCategory"
                      optionValue="value"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      formControlName="planCategory"
                      placeholder="Select a Plan Group"
                      (onChange)="planSelectType($event)"
                      [disabled]="serviceareaCheck"
                    >
                    </p-dropdown>
                    <div class="errorWrap text-danger" *ngIf="serviceareaCheck">
                      <div class="error text-danger">Please select Service Area first!</div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Bill To {{ myFinalCheck ? "*" : "" }}</label>
                    <p-dropdown
                      (onChange)="billtoSelectValue($event)"
                      [options]="billToData"
                      placeholder="Select Bill To"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      formControlName="billTo"
                    ></p-dropdown>

                    <div
                      class="errorWrap text-danger"
                      *ngIf="customerGroupForm.controls.discount.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="customerGroupForm.controls.discount.errors.max"
                      >
                        Bill To required!
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Billable To</label>
                    <br />
                    <p-dropdown
                      [disabled]="true"
                      [options]="billableCusList"
                      [showClear]="true"
                      filter="true"
                      filterBy="name"
                      formControlName="billableCustomerId"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a Billable"
                      styleClass="disableDropdown"
                    ></p-dropdown>
                    <button
                      type="button"
                      [disabled]="isLeadEdit"
                      (click)="modalOpenParentCustomer('billable')"
                      class="btn btn-primary"
                      style="
                        border-radius: 5px;
                        padding: 5px 10px;
                        line-height: 1.5;
                        margin-left: 10px;
                      "
                    >
                      <i class="fa fa-plus-square"></i>
                    </button>
                    <button
                      class="btn btn-danger"
                      [disabled]="isLeadEdit"
                      style="
                        border-radius: 5px;
                        padding: 5px 10px;
                        line-height: 1.5;
                        margin-left: 10px;
                      "
                      (click)="removeSelParentCust('billable')"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                  <div
                    *ngIf="
                      !serviceareaCheck &&
                      customerGroupForm.value.billTo == 'ORGANIZATION' &&
                      !ifPlanGroup
                    "
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12"
                  >
                    <label>Invoice To Org: {{ myFinalCheck ? "*" : "" }}</label>
                    <p-dropdown
                      [options]="isInvoiceData"
                      placeholder="Select Invoice to org or not"
                      optionValue="value"
                      optionLabel="label"
                      formControlName="isInvoiceToOrg"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="
                      !serviceareaCheck &&
                      customerGroupForm.value.billTo == 'ORGANIZATION' &&
                      ifPlanGroup
                    "
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top"
                  >
                    <label>Invoice To Org:</label>
                    <p-dropdown
                      (onChange)="valueChange($event)"
                      [disabled]="!customerGroupForm.value.plangroupid"
                      [options]="isInvoiceData"
                      formControlName="isInvoiceToOrg"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select Invoice to org or not"
                    ></p-dropdown>
                  </div>
                </div>
                <br />

                <div *ngIf="ifPlanGroup" class="row">
                  <div
                    *ngIf="!serviceareaCheck && isLeadEdit"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                  >
                    <p-dropdown
                      [options]="commondropdownService.PlanGroupDetails"
                      filter="true"
                      filterBy="planGroupName"
                      formControlName="plangroupid"
                      optionLabel="planGroupName"
                      optionValue="planGroupId"
                      placeholder="Select a Plan Group"
                      (onChange)="planGroupSelectSubisu($event)"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="!serviceareaCheck && !leadExistingCustomerFlag && !isLeadEdit"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                  >
                    <p-dropdown
                      (onChange)="planGroupSelectSubisu($event)"
                      [options]="filterNormalPlanGroup"
                      [showClear]="true"
                      filter="true"
                      filterBy="planGroupName"
                      placeholder="Select a Plan Group"
                      formControlName="plangroupid"
                      optionLabel="planGroupName"
                      optionValue="planGroupId"
                      placeholder="Select a Plan Group"
                    >
                      <ng-template let-data pTemplate="item">
                        <div class="item-drop1">
                          <span class="item-value1">
                            {{ data.planGroupName }}
                            <span *ngIf="data.category == 'Business Promotion'">
                              ( Business Promotion )</span
                            >
                          </span>
                        </div>
                      </ng-template>
                    </p-dropdown>
                  </div>
                  <div
                    *ngIf="!serviceareaCheck && leadExistingCustomerFlag"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                  >
                    <p-dropdown
                      [options]="commondropdownService.PlanGroupDetails"
                      filter="true"
                      filterBy="planGroupName"
                      formControlName="plangroupid"
                      optionLabel="planGroupName"
                      optionValue="planGroupId"
                      placeholder="Select a Plan Group"
                      (onChange)="planGroupSelectSubisu($event)"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                  >
                    <p-dropdown
                      [options]="chargeType"
                      filter="true"
                      filterBy="label"
                      formControlName="discountType"
                      optionLabel="label"
                      optionValue="label"
                      placeholder="Select a Discount Type"
                      [(ngModel)]="discountType"
                      [disabled]="checkIfDiscountPlanGroup(customerGroupForm.value.plangroupid)"
                    ></p-dropdown>

                    <br />
                  </div>
                  <div
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                    *ngIf="!serviceareaCheck && customerGroupForm.value.billTo !== 'ORGANIZATION'"
                  >
                    <input
                      type="number"
                      class="form-control"
                      name="discount"
                      id="discount"
                      placeholder="Enter a Discount "
                      formControlName="discount"
                      (keyup)="discountPercentage($event)"
                      [value]="customerGroupForm.value.discount | number"
                      [readonly]="
                        !customerGroupForm.value.plangroupid || ifcustomerDiscountField == 'false'
                      "
                    />

                    <div
                      class="errorWrap text-danger"
                      *ngIf="customerGroupForm.controls.discount.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="customerGroupForm.controls.discount.errors.max"
                      >
                        Maximum 99 Percentage allowed.
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="
                      customerGroupForm.value.billTo !== 'ORGANIZATION' &&
                      customerGroupForm.value.discountType === 'Recurring'
                    "
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                  >
                    <p-calendar
                      [hideOnDateTimeSelect]="true"
                      [showButtonBar]="true"
                      [showIcon]="true"
                      [style]="{ width: '100%' }"
                      dateFormat="dd/mm/yy"
                      [minDate]="dateTime"
                      formControlName="discountExpiryDate"
                      placeholder="Enter Discount Expiry Date"
                    ></p-calendar>
                  </div>
                  <div
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12 top"
                    *ngIf="
                      !serviceareaCheck &&
                      customerGroupForm.value.billTo !== 'ORGANIZATION' &&
                      ifPlanGroup
                    "
                  >
                    <div class="form-group form-check inputcheckboxCenter">
                      <input formControlName="istrialplan" type="checkbox" class="inputcheckbox" />
                      <label
                        class="form-check-label"
                        for="acceptTerms"
                        style="margin-left: 1rem; margin-bottom: 0"
                        >Trial Plan
                      </label>
                    </div>
                  </div>
                </div>

                <fieldset
                  style="margin-top: 2rem"
                  *ngIf="this.customerGroupForm.value.plangroupid && ifPlanGroup"
                >
                  <legend>Plan Mapping List</legend>
                  <div class="boxWhite">
                    <div class="row table-responsive">
                      <div class="col-lg-12 col-md-12 scrollbarPlangroupMappingList">
                        <table class="table">
                          <thead>
                            <tr>
                              <th style="text-align: left; padding-left: 8px">Service</th>
                              <th style="text-align: left; padding-left: 8px">Plan Name</th>
                              <th style="text-align: left; padding-left: 8px">Validity</th>
                              <th style="text-align: left; padding-left: 8px">Currency</th>
                              <th style="text-align: left; padding-left: 8px">Price</th>
                              <th style="text-align: left; padding-left: 8px">
                                <!-- *ngIf="planGroupData.category == 'Business Promotion'" -->

                                New Price
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              *ngFor="
                                let data of planGroupMapingList
                                  | paginate
                                    : {
                                        id: 'PlanMappingData',
                                        itemsPerPage: PlanMappingitemsPerPage,
                                        currentPage: currentPagePlanMapping,
                                        totalItems: PlanMappingtotalRecords
                                      };
                                let index = index
                              "
                            >
                              <td style="padding-left: 8px">
                                {{ data.service }}
                              </td>
                              <td style="padding-left: 8px">
                                {{ data.plan.name }}
                              </td>
                              <td style="padding-left: 8px">
                                {{ data.plan.validity }}
                                {{ data.plan.unitsOfValidity }}
                              </td>
                              <td>
                                {{ data.plan?.currency }}
                              </td>
                              <td>
                                {{ data.plan.offerprice }}
                              </td>
                              <!-- <td *ngIf="planGroupData.category == 'Business Promotion'"> -->
                              <td>
                                {{ data.newofferprice }}
                              </td>
                            </tr>
                          </tbody>
                        </table>

                        <!-- <div class="row">
                          <div class="col-md-12">
                            <pagination-controls
                              id="PlanMappingData"
                              maxSize="10"
                              directionLinks="true"
                              previousLabel=""
                              nextLabel=""
                              (pageChange)="pageChangedPlanMapping($event)"
                            ></pagination-controls>
                          </div>
                        </div> -->
                      </div>
                    </div>
                    <br />
                  </div>
                </fieldset>

                <div *ngIf="!serviceareaCheck && ifIndividualPlan">
                  <div
                    class="row"
                    [formGroup]="planGroupForm"
                    [ngClass]="{
                      leadplanShowCSS: true
                    }"
                    class="row"
                  >
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12">
                      <div class="row">
                        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                          <p-dropdown
                            [options]="serviceData"
                            optionValue="name"
                            optionLabel="name"
                            filter="true"
                            filterBy="name"
                            #ddlService
                            placeholder="Select a Service *"
                            formControlName="service"
                            (onChange)="serviceBasePlanDATA(ddlService)"
                            [ngClass]="{
                              'is-invalid': plansubmitted && planGroupForm.controls.service.errors
                            }"
                          ></p-dropdown>
                          <div></div>

                          <div
                            class="errorWrap text-danger"
                            *ngIf="plansubmitted && planGroupForm.controls.service.errors"
                          >
                            <div class="error text-danger">Service is required.</div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                          <p-dropdown
                            [options]="plantypaSelectData"
                            optionValue="id"
                            optionLabel="name"
                            filter="true"
                            filterBy="name"
                            placeholder="Select a Plan *"
                            formControlName="planId"
                            (onChange)="getPlanValidity($event)"
                            [ngClass]="{
                              'is-invalid': plansubmitted && planGroupForm.controls.planId.errors
                            }"
                          >
                            <ng-template let-data pTemplate="item">
                              <div class="item-drop1">
                                <span class="item-value1">
                                  {{ data.name }}
                                  <span *ngIf="data.category == 'Business Promotion'">
                                    ( Business Promotion )</span
                                  >
                                </span>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <div></div>

                          <div
                            class="errorWrap text-danger"
                            *ngIf="plansubmitted && planGroupForm.controls.planId.errors"
                          >
                            <div class="error text-danger">Plan is required.</div>
                          </div>
                        </div>
                        <div
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                          *ngIf="this.custType == 'Prepaid'"
                        >
                          <div style="display: flex">
                            <div style="width: 40%">
                              <input
                                id="validity"
                                type="number"
                                min="1"
                                class="form-control"
                                placeholder="Enter Validity"
                                formControlName="validity"
                                readonly
                              />
                            </div>
                            <div
                              style="width: 60%; height: 34px"
                              [formGroup]="validityUnitFormGroup"
                            >
                              <select
                                class="form-control"
                                style="width: 100%"
                                formControlName="validityUnit"
                                disabled
                              >
                                <option value="">Select Unit</option>
                                <option
                                  *ngFor="let label of commondropdownService.validityUnitData"
                                  value="{{ label.label }}"
                                >
                                  {{ label.label }}
                                </option>
                              </select>
                            </div>
                          </div>
                        </div>
                        <div
                          *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <div class="form-group form-check inputcheckboxCenter">
                            <input
                              formControlName="istrialplan"
                              type="checkbox"
                              class="inputcheckbox"
                              [readonly]="isTrialCheckDisable"
                            />
                            <label
                              class="form-check-label"
                              for="acceptTerms"
                              style="margin-left: 1rem; margin-bottom: 0"
                              >Trial Plan
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div
                          *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <div style="display: flex">
                            <input
                              id="offerprice"
                              type="number"
                              class="form-control"
                              placeholder="Old Offerprice"
                              formControlName="offerprice"
                              readonly
                            />
                          </div>
                        </div>
                        <div
                          *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <div style="display: flex">
                            <input
                              [readonly]="ifplanisSubisuSelect"
                              id="newAmount"
                              type="number"
                              class="form-control"
                              placeholder="New Offerprice"
                              formControlName="newAmount"
                            />
                          </div>
                        </div>
                        <div
                          *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <p-dropdown
                            [options]="chargeType"
                            filter="true"
                            filterBy="label"
                            formControlName="discountType"
                            optionLabel="label"
                            optionValue="label"
                            placeholder="Select a Discount Type"
                            [(ngModel)]="discountType"
                            [disabled]="ifdiscounAllow"
                          ></p-dropdown>
                        </div>
                        <div
                          *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <input
                            type="number"
                            class="form-control"
                            name="discount"
                            id="discount"
                            placeholder="Discount %"
                            formControlName="discount"
                            (keyup)="discountPercentage($event)"
                            [readonly]="!planGroupForm.value.planId || !ifcustomerDiscountField"
                            [ngClass]="{
                              'is-invalid': plansubmitted && planGroupForm.controls.discount.errors
                            }"
                          />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="planGroupForm.controls.discount.errors"
                          >
                            <div
                              class="error text-danger"
                              *ngIf="planGroupForm.controls.discount.errors.max"
                            >
                              Maximum 99 Percentage allowed.
                            </div>
                            <div
                              class="error text-danger"
                              *ngIf="planGroupForm.controls.discount.errors.min"
                            >
                              Maximum -99 Percentage allowed.
                            </div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="plansubmitted && planGroupForm.controls.discount.errors"
                          >
                            <div class="error text-danger">Discount is required.</div>
                          </div>
                        </div>
                        <div
                          *ngIf="
                            customerGroupForm.value.billTo !== 'ORGANIZATION' &&
                            planGroupForm.value.discountType === 'Recurring'
                          "
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <p-calendar
                            [hideOnDateTimeSelect]="true"
                            [showButtonBar]="true"
                            [showIcon]="true"
                            [style]="{ width: '100%' }"
                            dateFormat="dd/mm/yy"
                            [minDate]="dateTime"
                            formControlName="discountExpiryDate"
                            placeholder="Enter Discount Expiry Date"
                          ></p-calendar>
                        </div>
                        <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                          <input
                            id="quantity"
                            type="number"
                            class="form-control"
                            placeholder="Enter Quantity"
                            formControlName="quantity"
                          />
                        </div> -->
                      </div>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12" style="text-align: center">
                      <button
                        (click)="onplanMappingQuantityList()"
                        class="btn btn-primary"
                        style="object-fit: cover; padding: 5px 8px"
                      >
                        <i aria-hidden="true" class="fa fa-plus-square"></i>
                        Add
                      </button>
                    </div>
                  </div>
                  <table class="table coa-table" style="margin-top: 3rem">
                    <thead>
                      <tr>
                        <th>Service*</th>
                        <th>Plan*</th>
                        <th>Validity*</th>
                        <th>Currency*</th>
                        <th *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                          offerPrice*
                        </th>
                        <th *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                          New offerPrice
                        </th>
                        <th *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          Discount Type
                        </th>
                        <th *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          Discount (%)*
                        </th>
                        <th *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          Discount Expiry Date
                        </th>
                        <th
                          *ngIf="
                            customerGroupForm.value.parentCustomerId != null &&
                            customerGroupForm.value.parentCustomerId != ''
                          "
                        >
                          Invoice Type
                        </th>
                        <th *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          Trial plan
                        </th>
                        <th
                          [ngClass]="{
                            leadplanShowCSS: isLeadEdit,
                            leadplanShowCSS: !isLeadEdit
                          }"
                        >
                          Delete
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let row of payMappingListFromArray.controls
                            | paginate
                              : {
                                  id: 'payMappingListFromArrayData',
                                  itemsPerPage: paymappingItemPerPage,
                                  currentPage: currentPagePayMapping,
                                  totalItems: payMappinftotalRecords
                                };
                          let index = index
                        "
                      >
                        <td style="padding-left: 8px">
                          <!-- {{row.value.service}} -->
                          <select
                            class="form-control"
                            style="width: 100%"
                            name="service"
                            id="service"
                            [formControl]="row.get('service')"
                            disabled
                          >
                            <option value="">Select Service</option>
                            <option *ngFor="let item of serviceData" value="{{ item.name }}">
                              {{ item.name }}
                            </option>
                          </select>
                        </td>
                        <td>
                          <!-- {{row.value.planId}} -->
                          <select
                            class="form-control"
                            style="width: 100%"
                            name="planId"
                            id="planId"
                            [formControl]="row.get('planId')"
                            disabled
                          >
                            <option value="">Select Plan</option>
                            <option *ngFor="let item of postpaidplanData" value="{{ item.id }}">
                              {{ item.name }}
                            </option>
                          </select>
                        </td>
                        <td *ngIf="this.customerGroupForm.value.custtype == 'Prepaid'">
                          <div style="display: flex">
                            <div style="width: 40%">
                              <input
                                id="validity"
                                type="number"
                                min="1"
                                class="form-control"
                                placeholder="Enter Validity"
                                [formControl]="row.get('validity')"
                                readonly
                              />
                            </div>
                            <div style="width: 60%; height: 34px">
                              <span *ngFor="let data of validityUnitFormArray.controls; index as j">
                                <span *ngIf="index == j">
                                  <select
                                    class="form-control"
                                    style="width: 100%"
                                    [formControl]="data.get('validityUnit')"
                                    disabled
                                  >
                                    <option value="">Select Unit</option>
                                    <option
                                      *ngFor="let label of commondropdownService.validityUnitData"
                                      value="{{ label.label }}"
                                    >
                                      {{ label.label }}
                                    </option>
                                  </select>
                                </span>
                              </span>
                            </div>
                          </div>
                        </td>
                        <td *ngIf="this.customerGroupForm.value.custtype != 'Prepaid'">N/A</td>
                        <td>
                          <input
                            [formControl]="row.get('currency')"
                            class="form-control"
                            id="currency"
                            min="0"
                            name="currency"
                            placeholder="Enter Currency"
                            [readonly]="true"
                          />
                        </td>
                        <td *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                          <input
                            type="number"
                            class="form-control"
                            name="offerPrice"
                            id="offerPrice"
                            placeholder="Enter a OfferPrice *"
                            [formControl]="row.get('offerPrice')"
                            [ngClass]="{
                              'is-invalid':
                                plansubmitted && planGroupForm.controls.offerprice.errors
                            }"
                            readonly
                          />
                        </td>

                        <td *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                          <input
                            type="number"
                            class="form-control"
                            name="newAmount"
                            id="newAmount"
                            placeholder="Enter a ewAmount *"
                            [formControl]="row.get('newAmount')"
                            [ngClass]="{
                              'is-invalid': plansubmitted && planGroupForm.controls.newAmount.errors
                            }"
                            readonly
                          />
                        </td>
                        <td *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          <p-dropdown
                            [options]="chargeType"
                            filter="true"
                            filterBy="label"
                            [formControl]="row.get('discountType')"
                            optionLabel="label"
                            optionValue="label"
                            placeholder="Select a Discount Type"
                            [disabled]="ifdiscounAllow"
                          ></p-dropdown>
                        </td>
                        <td *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          <input
                            type="number"
                            class="form-control"
                            name="discount"
                            id="discount"
                            placeholder="Enter a Discount *"
                            [formControl]="row.get('discount')"
                            [readonly]="isLeadEdit || !ifcustomerDiscountField"
                            (keyup)="discountChange($event, index)"
                          />
                        </td>
                        <td
                          *ngIf="
                            customerGroupForm.value.billTo !== 'ORGANIZATION' &&
                            row.value.discountType === 'Recurring'
                          "
                        >
                          <p-calendar
                            [showButtonBar]="true"
                            [showIcon]="true"
                            [style]="{ width: '100%' }"
                            dateFormat="dd/mm/yy"
                            [minDate]="dateTime"
                            name="discountExpiryDate"
                            id="discountExpiryDate"
                            [formControl]="row.get('discountExpiryDate')"
                            placeholder="Enter Discount Expiry Date"
                          ></p-calendar>
                        </td>
                        <td
                          *ngIf="
                            customerGroupForm.value.billTo !== 'ORGANIZATION' &&
                            row.value.discountType !== 'Recurring'
                          "
                        >
                          -
                        </td>
                        <td
                          *ngIf="
                            customerGroupForm.value.parentCustomerId != null &&
                            customerGroupForm.value.parentCustomerId != ''
                          "
                        >
                          <select
                            [formControl]="row.get('invoiceType')"
                            class="form-control"
                            disabled
                            id="invoiceType"
                            name="invoiceType"
                            style="width: 100%"
                          >
                            <option value="">Select Service</option>
                            <option *ngFor="let item of invoiceType" value="{{ item.value }}">
                              {{ item.label }}
                            </option>
                          </select>
                        </td>
                        <td *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                          <input
                            type="checkbox"
                            class="inputcheckbox"
                            [readonly]="isLeadEdit"
                            [formControl]="row.get('istrialplan')"
                            [value]="row.value.discount | number"
                            [readonly]="true"
                            [ngClass]="{
                              'is-invalid': plansubmitted && planGroupForm.controls.discount.errors
                            }"
                            readonly
                          />
                        </td>
                        <td
                          [ngClass]="{
                            leadplanShowCSS: isLeadEdit,
                            leadplanShowCSS: !isLeadEdit
                          }"
                        >
                          <button
                            *ngIf="chargeFieldIndex !== undefined"
                            class="approve-btn"
                            id="deleteAtt"
                            href="javascript:void(0)"
                            (click)="deleteConfirmonChargeField(index, 'Plan')"
                          >
                            <img src="assets/img/ioc02.jpg" />
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>

                  <div class="row">
                    <div class="col-md-12">
                      <pagination-controls
                        id="payMappingListFromArrayData"
                        maxSize="5"
                        directionLinks="true"
                        previousLabel=""
                        nextLabel=""
                        (pageChange)="pageChangedpayMapping($event)"
                      ></pagination-controls>
                    </div>
                  </div>
                  <br />
                </div>
              </div>
            </fieldset>

            <!-- Service Pack -->
            <fieldset *ngIf="myFinalCheck">
              <legend>Service Pack Details</legend>
              <!-- <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Plan Offer Price</label>
                    <input
                      class="form-control"
                      [(ngModel)]="offerPrice"
                      id="planofferprice"
                      placeholder="Enter Plan Offer Price "
                      readonly
                      type="number"
                      [ngModelOptions]="{ standalone: true }"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 cold-xs-12" style="padding: 27px">
                    <div class="form-group form-check inputcheckboxCenter">
                      <input
                        [(ngModel)]="isInstallemnt"
                        type="checkbox"
                        class="inputcheckbox"
                        (change)="onChangeInstallmentType()"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      <label style="margin-left: 1rem; margin-bottom: 0"
                        >Do you want Installment?
                      </label>
                    </div>
                  </div>
                </div>
                <br />
                <table [formGroup]="servicePackForm" style="width: 100%">
                  <tr>
                    <td style="padding: 0 10px 0 0">
                      <p-dropdown
                        [ngClass]="{
                          'is-invalid':
                            servicePackSubmitted && servicePackForm.controls.vasId.errors
                        }"
                        [options]="planAllData"
                        filter="true"
                        filterBy="name"
                        formControlName="vasId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Vas *"
                        (onChange)="getPlanFromVasId($event)"
                      >
                        <ng-template let-data pTemplate="item">
                          <div class="item-drop1">
                            <span class="item-value1">
                              {{ data.name }}
                              <span *ngIf="data.category == 'Business Promotion'">
                                ( Business Promotion )</span
                              >
                            </span>
                          </div>
                        </ng-template>
                      </p-dropdown>
                    </td>
                    <td style="padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="commondropdownService.installmentTypeData"
                        optionValue="value"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a Installment Type"
                        formControlName="installmentFrequency"
                        [showClear]="true"
                        [disabled]="!isInstallemnt"
                        [ngClass]="{
                          'is-invalid':
                            servicePackSubmitted &&
                            servicePackForm.controls.installmentFrequency.errors
                        }"
                      ></p-dropdown>
                    </td>
                    <td style="padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="totalInstallments"
                        optionValue="value"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Installments"
                        formControlName="totalInstallments"
                        [showClear]="true"
                        [disabled]="!isInstallemnt"
                        [ngClass]="{
                          'is-invalid':
                            servicePackSubmitted &&
                            servicePackForm.controls.totalInstallments.errors
                        }"
                      ></p-dropdown>
                    </td>
                    <td style="text-align: center; width: 5%">
                      <button
                        style="object-fit: cover; padding: 5px 8px"
                        class="btn btn-primary"
                        (click)="onAddoverServicePackListField()"
                        [disabled]="overServicePackListFormArray?.controls?.length >= 1"
                      >
                        <i class="fa fa-plus-square" aria-hidden="true"></i>
                        Add
                      </button>
                    </td>
                  </tr>

                  <tr>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div
                        *ngIf="servicePackSubmitted && servicePackForm.controls.vasId.errors"
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">Vas is required.</div>
                      </div>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          servicePackSubmitted &&
                          servicePackForm.controls.installmentFrequency.errors
                        "
                      >
                        <div class="error text-danger" *ngIf="isInstallemnt">
                          Install Type is required.
                        </div>
                      </div>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          servicePackSubmitted && servicePackForm.controls.totalInstallments.errors
                        "
                      >
                        <div class="error text-danger" *ngIf="isInstallemnt">
                          Installment No is required.
                        </div>
                      </div>
                    </td>
                    <td style="text-align: center; width: 5%"></td>
                  </tr>
                </table>
                <table class="table coa-table" style="margin-top: 3rem">
                  <thead>
                    <tr>
                      <th style="text-align: center">Vas Name</th>
                      <th style="text-align: center">Installment Type</th>
                      <th style="text-align: center">Total Installment</th>
                      <th style="text-align: right; width: 5%; padding: 8px">Delete</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let row of overServicePackListFormArray.controls
                          | paginate
                            : {
                                id: 'overServicePackListFromArrayData',
                                itemsPerPage: overServicePackListItemPerPage,
                                currentPage: currentPageoverServicePackList,
                                totalItems: overServicePackListtotalRecords
                              };
                        let index = index
                      "
                    >
                      <td style="padding-left: 8px">
                        <select
                          [formControl]="row.get('vasId')"
                          class="form-control"
                          disabled
                          id="vasId"
                          name="vasId"
                          style="width: 100%"
                        >
                          <option value="">Select Vas</option>
                          <option *ngFor="let item of planAllData" value="{{ item.id }}">
                            {{ item.name }}
                          </option>
                        </select>
                        <div></div>
                      </td>
                      <td>
                        <p-dropdown
                          [options]="commondropdownService.installmentTypeData"
                          optionValue="value"
                          optionLabel="text"
                          filter="true"
                          filterBy="text"
                          placeholder="Select a Installment Type"
                          [formControl]="row.get('installmentFrequency')"
                          [showClear]="true"
                          [disabled]="true"
                        ></p-dropdown>
                      </td>
                      <td style="text-align: right">
                        <a
                          id="deleteAtt"
                          href="javascript:void(0)"
                          (click)="deleteConfirmServicePackField(index, 'Service Pack')"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                      <td style="text-align: right">
                        <a
                          id="deleteAtt"
                          href="javascript:void(0)"
                          (click)="deleteConfirmonChargeField(index, 'Charge')"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="row">
                  <div class="col-md-12">
                    <pagination-controls
                      id="overServicePackListFromArrayData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedOverServicePackList($event)"
                    ></pagination-controls>
                  </div>
                </div>
                <br />
              </div> -->
              <div class="boxWhite">
                <div class="row" [formGroup]="servicePackForm">
                  <div class="col-lg-3 col-md-3 col-sm-6 cold-xs-12">
                    <div class="form-group">
                      <p-dropdown
                        [ngClass]="{
                          'is-invalid':
                            servicePackSubmitted && servicePackForm.controls.vasId.errors
                        }"
                        [options]="planAllData"
                        filter="true"
                        filterBy="name"
                        formControlName="vasId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Vas *"
                        (onChange)="getPlanFromVasId($event)"
                      >
                        <ng-template let-data pTemplate="item">
                          <div class="item-drop1">
                            <span class="item-value1">
                              {{ data.name }}
                              <span *ngIf="data.category == 'Business Promotion'">
                                ( Business Promotion )</span
                              >
                            </span>
                          </div>
                        </ng-template>
                      </p-dropdown>
                      <div
                        *ngIf="servicePackSubmitted && servicePackForm.controls.vasId.errors"
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">Vas is required.</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 cold-xs-12">
                    <div class="form-group form-check inputcheckboxCenter">
                      <input
                        [(ngModel)]="isInstallemnt"
                        type="checkbox"
                        class="inputcheckbox"
                        (change)="onChangeInstallmentType()"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      <label style="margin-left: 1rem; margin-bottom: 0"
                        >Do you want Installment?
                      </label>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 cold-xs-12">
                    <p-dropdown
                      [options]="commondropdownService.installmentTypeData"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      placeholder="Select a Installment Type"
                      formControlName="installmentFrequency"
                      [showClear]="true"
                      [disabled]="!isInstallemnt"
                      [ngClass]="{
                        'is-invalid':
                          servicePackSubmitted &&
                          servicePackForm.controls.installmentFrequency.errors
                      }"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        servicePackSubmitted && servicePackForm.controls.installmentFrequency.errors
                      "
                    >
                      <div class="error text-danger" *ngIf="isInstallemnt">
                        Install Type is required.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 cold-xs-12">
                    <p-dropdown
                      [options]="totalInstallments"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      placeholder="Installments"
                      formControlName="totalInstallments"
                      [showClear]="true"
                      [disabled]="!isInstallemnt"
                      [ngClass]="{
                        'is-invalid':
                          servicePackSubmitted && servicePackForm.controls.totalInstallments.errors
                      }"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        servicePackSubmitted && servicePackForm.controls.totalInstallments.errors
                      "
                    >
                      <div class="error text-danger" *ngIf="isInstallemnt">
                        Installment No is required.
                      </div>
                    </div>
                  </div>
                </div>
                <br />
                <!-- VAS Details Card -->
                <div class="vas-card" *ngIf="vasData">
                  <div class="vas-header"><strong>VAS Details</strong></div>
                  <div class="vas-body">
                    <div class="vas-row">
                      <span class="label">VAS Name</span>
                      <span class="value">{{ vasData.name }}</span>
                    </div>
                    <div class="vas-row">
                      <span class="label">Offer Price</span>
                      <span class="value">{{ vasData.vasAmount | currency: planCurrency }}</span>
                    </div>
                    <div class="vas-row">
                      <span class="label">Validity</span>
                      <span class="value"
                        >{{ vasData.validity }} {{ vasData?.unitsOfValidity }}</span
                      >
                    </div>

                    <!-- Installment Details -->
                    <ng-container *ngIf="isInstallemnt">
                      <div class="vas-row">
                        <span class="label">Installment Frequency</span>
                        <span class="value">{{ servicePackForm.value.installmentFrequency }}</span>
                      </div>
                      <div class="vas-row">
                        <span class="label">Total Installments</span>
                        <span class="value">{{ servicePackForm.value.totalInstallments }}</span>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </fieldset>

            <!-- Competitor Pack Details -->

            <fieldset *ngIf="isMandatory">
              <legend>Competitor Pack Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Previous Service Type</label>
                    <p-dropdown
                      [filter]="true"
                      id="servicerType"
                      [options]="servicerTypeList"
                      formControlName="servicerType"
                      placeholder="Select Previous Service Type"
                    >
                    </p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Previous Amount</label>
                    <input
                      class="form-control"
                      type="number"
                      id="previousAmount"
                      formControlName="previousAmount"
                      placeholder="Select Previous Amount"
                    />
                    <div></div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Previous Month</label>
                    <p-dropdown
                      [filter]="true"
                      id="previousMonth"
                      [options]="months"
                      formControlName="previousMonth"
                      placeholder="Select Month"
                    >
                    </p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Competitor Pack Duration</label>
                    <div style="display: flex">
                      <div style="width: 60%">
                        <input
                          class="form-control"
                          formControlName="competitorDuration"
                          id="competitorDuration"
                          placeholder="Enter Pack Duration"
                          type="number"
                        />
                      </div>
                      <div style="width: 40%; height: 34px">
                        <p-dropdown
                          [options]="competitorDurationUnits"
                          formControlName="durationUnits"
                          optionLabel="label"
                          optionValue="label"
                          placeholder="Select Unit"
                        ></p-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Expiry</label>
                    <input
                      id="expiry"
                      type="date"
                      formControlName="expiry"
                      placeholder="DD/MM/YYYY"
                      class="form-control"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Current Pay</label>
                    <input
                      class="form-control"
                      name="amount"
                      id="amount"
                      formControlName="amount"
                      placeholder="Enter the Amount..."
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Customer Feedback</label>
                    <textarea
                      type="text"
                      class="form-control"
                      id="feedback"
                      placeholder="Enter the Customer Feedback..."
                      formControlName="feedback"
                    ></textarea>
                  </div>
                </div>
              </div>
            </fieldset>

            <!-- Rejected Lead Details -->

            <fieldset
              style="margin-top: 0rem; margin-bottom: 2rem"
              *ngIf="viewLeadListData?.leadStatus === 'Rejected'"
            >
              <legend>Rejected Lead Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-5 col-md-5 col-sm-6 col-xs-12 dataGroup m-0">
                    <label class="datalbl">Rejected Reason:&nbsp;&nbsp;&nbsp;</label>
                    <p-dropdown
                      [filter]="true"
                      formControlName="rejectReasonId"
                      [options]="rejectedReasonList"
                      optionValue="id"
                      optionLabel="name"
                      placeholder="Select a rejected reason"
                      (ngModelChange)="
                        selectRejectedReason(customerGroupForm.controls.rejectReasonId.value)
                      "
                    >
                    </p-dropdown>
                  </div>
                  <div
                    class="col-lg-5 col-md-5 col-sm-6 col-xs-12 dataGroup m-0"
                    *ngIf="rejectedSubReasonArr?.length > 0"
                  >
                    <label class="datalbl">Rejected Sub Reason:&nbsp;&nbsp;&nbsp;</label>
                    <p-dropdown
                      [filter]="true"
                      formControlName="rejectSubReasonId"
                      [options]="rejectedSubReasonArr"
                      optionValue="id"
                      optionLabel="name"
                      placeholder="Select Rejected sub reason for the lead."
                    >
                    </p-dropdown>
                  </div>
                </div>
              </div>
            </fieldset>

            <!-- CAF Basic Details -->

            <fieldset>
              <legend>Basic CAF Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>Title </label>
                    <!-- {{ myFinalCheck ? "*" : "" }} -->
                    <!-- <label>Title</label> -->
                    <p-dropdown
                      [options]="selectTitile"
                      optionValue="label"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      placeholder="Select a title"
                      formControlName="title"
                      [disabled]="customerGroupForm.value.title && ifReadonlyExtingInput"
                    ></p-dropdown>
                    <!-- <div class="errorWrap text-danger"
                                            *ngIf="submitted && customerGroupForm.controls.title.errors">
                                            <div class="error text-danger">Title is required.</div>
                                        </div> -->
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Contact Person {{ myFinalCheck ? "*" : "" }}</label>
                    <input
                      id="contactperson"
                      type="text"
                      class="form-control"
                      placeholder="Enter Contact Person"
                      formControlName="contactperson"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.contactperson"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.contactperson.errors"
                    >
                      <div class="error text-danger">Contact person is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="isMandatory">
                    <label>CAF No.</label>
                    <input
                      id="cafno"
                      min="0"
                      class="form-control"
                      placeholder="Enter CAF No"
                      formControlName="cafno"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.cafno"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label style="margin-bottom: 10px"
                      >Calendar Type {{ myFinalCheck ? "*" : "" }}</label
                    >
                    <p-dropdown
                      [options]="celendarTypeData"
                      optionValue="label"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      placeholder="Select a Calendar Type"
                      formControlName="calendarType"
                      [disabled]="ifReadonlyExtingInput"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.calendarType.errors"
                    >
                      <div class="error text-danger">Calender type is required.</div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row">
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    style="display: initial; margin-top: 25px"
                    *ngIf="myFinalCheck"
                  >
                    <div
                      class="form-group form-check inputcheckboxCenter"
                      style="align-items: flex-start"
                    >
                      <p-checkbox
                        binary="true"
                        class="checkbox-align"
                        formControlName="isCredentialMatchWithAccountNo"
                        name="isCheckBox"
                        (onChange)="onCredentialMatchChange($event)"
                      ></p-checkbox
                      >&nbsp;
                      <label
                        class="form-check-label"
                        for="acceptTerms"
                        style="margin-bottom: -4%; margin-left: 6px"
                        >Username is same as Account Number
                      </label>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="myFinalCheck">
                    <label>AAA Username {{ myFinalCheck ? "*" : "" }}</label>
                    <input
                      id="username"
                      type="text"
                      class="form-control"
                      autocomplete="off"
                      placeholder="Enter AAA Username"
                      formControlName="username"
                      (keydown.Tab)="onKey($event)"
                      (change)="validateUsername(customerGroupForm.controls.username.value)"
                      [readonly]="customerGroupForm.value.username && ifReadonlyExtingInput"
                    />
                    <div class="errorWrap text-info">
                      <div class="error text-info">
                        Press "<b><i>Tab</i></b
                        >" to check the existance of registered cust.
                      </div>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.username.errors"
                    >
                      <div class="error text-danger">Username is required.</div>
                    </div>
                  </div>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                    style="display: initial; margin-top: 25px"
                    *ngIf="myFinalCheck"
                  >
                    <div
                      class="form-group form-check inputcheckboxCenter"
                      style="align-items: flex-start"
                    >
                      <p-checkbox
                        binary="true"
                        class="checkbox-align"
                        formControlName="isPasswordAutoGenerated"
                        name="isCheckBox"
                        (onChange)="onPasswordAuotGenrated($event)"
                      ></p-checkbox>
                      <label
                        class="form-check-label"
                        for="acceptTerms"
                        style="margin-bottom: -4%; margin-left: 6px"
                        >Is Auto Generated Password
                      </label>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="myFinalCheck">
                    <label>AAA Password <span *ngIf="!isAutoGeneratedPassword">*</span> </label>
                    <div
                      class="form-control"
                      [ngClass]="isAutoGeneratedPassword ? 'displayflexdis' : 'displayflex'"
                    >
                      <div style="width: 95%">
                        <input
                          [ngClass]="{
                            'is-invalid': submitted && customerGroupForm.controls.password.errors
                          }"
                          [type]="_passwordType"
                          class="inputPassword"
                          formControlName="password"
                          id="password"
                          placeholder="Enter Password"
                          autocomplete="new-password"
                        />
                      </div>
                      <div style="width: 5%">
                        <div *ngIf="showPassword">
                          <i
                            (click)="showPassword = false; _passwordType = 'password'"
                            class="fa fa-eye"
                          ></i>
                        </div>
                        <div *ngIf="!showPassword">
                          <i
                            (click)="showPassword = true; _passwordType = 'text'"
                            class="fa fa-eye-slash"
                          ></i>
                        </div>
                      </div>
                    </div>
                    <!-- <div *ngIf="submitted && customerGroupForm.controls.password.errors"
                                            class="errorWrap text-danger">
                                            <div class="error text-danger">Password is required.</div>
                                        </div> -->
                    <div
                      *ngIf="submitted && customerGroupForm.controls.password.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="customerGroupForm.controls.password.errors['required']"
                        class="error text-danger"
                      >
                        Password is required.
                      </div>
                      <div
                        *ngIf="customerGroupForm.controls.password.errors['noSpace']"
                        class="error text-danger"
                      >
                        Password cannot contain spaces.
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <ng-container *ngIf="myFinalCheck">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Username {{ myFinalCheck ? "*" : "" }}</label>
                      <input
                        id="username"
                        type="text"
                        class="form-control"
                        placeholder="Enter Login Username"
                        formControlName="loginUsername"
                        (blur)="onKeyLoginUserName(customerGroupForm.controls.loginUsername.value)"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && customerGroupForm.controls?.loginUsername?.errors"
                      >
                        <div class="error text-danger">Login Username is required.</div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Password <span *ngIf="!isAutoGeneratedPassword">*</span> </label>
                      <div
                        class="form-control"
                        [ngClass]="isAutoGeneratedPassword ? 'displayflexdis' : 'displayflex'"
                      >
                        <div style="width: 95%">
                          <input
                            [ngClass]="{
                              'is-invalid':
                                submitted && customerGroupForm.controls.loginPassword.errors
                            }"
                            [type]="_loginPasswordType"
                            class="inputPassword"
                            formControlName="loginPassword"
                            id="loginPassword"
                            placeholder="Enter Login Password"
                            autocomplete="new-password"
                          />
                        </div>
                        <div style="width: 5%">
                          <div *ngIf="showLoginPassword">
                            <i
                              (click)="showLoginPassword = false; _loginPasswordType = 'password'"
                              class="fa fa-eye"
                            ></i>
                          </div>
                          <div *ngIf="!showLoginPassword">
                            <i
                              (click)="showLoginPassword = true; _loginPasswordType = 'text'"
                              class="fa fa-eye-slash"
                            ></i>
                          </div>
                        </div>
                      </div>
                      <div
                        *ngIf="submitted && customerGroupForm.controls.loginPassword.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="customerGroupForm.controls.loginPassword.errors['required']"
                          class="error text-danger"
                        >
                          Login Password is required.
                        </div>
                        <div
                          *ngIf="customerGroupForm.controls.loginPassword.errors['noSpace']"
                          class="error text-danger"
                        >
                          Login Password cannot contain spaces.
                        </div>
                      </div>
                    </div>
                  </ng-container>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                    *ngIf="myFinalCheck && customerGroupForm.value.custtype === 'Postpaid'"
                  >
                    <label>Bill Day *</label>
                    <p-dropdown
                      [options]="days"
                      optionValue="label"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      placeholder="Select a Day"
                      formControlName="billday"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.billday.errors
                      }"
                    ></p-dropdown>

                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.billday.errors"
                    >
                      <div class="error text-danger">Bill Day is required.</div>
                    </div>
                  </div>
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                    *ngIf="myFinalCheck && customerGroupForm.value.custtype === 'Postpaid'"
                  >
                    <label>Early Bill Day *</label>
                    <p-dropdown
                      id="earlybillday"
                      [options]="earlydays"
                      optionValue="label"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      placeholder="Select a Day"
                      formControlName="earlybillday"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.earlybillday.errors
                      }"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.earlybillday.errors"
                    >
                      <div class="error text-danger">Early Bill Day is required.</div>
                    </div>
                  </div>
                </div>
                <div class="row" *ngIf="isMandatory">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top" *ngIf="myFinalCheck">
                    <label>Renew Plan Limit</label>
                    <input
                      class="form-control"
                      placeholder="Enter Renew Plan Limit "
                      type="number"
                      formControlName="renewPlanLimit"
                      (keypress)="keypressSession($event)"
                      min="0"
                    />
                    <!-- <br /> -->
                  </div>
                </div>
              </div>
            </fieldset>

            <!-- KYC Details -->

            <!-- <fieldset style="margin-top: 1.5rem">
              <legend>KYC Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>GST</label>
                    <input
                      id="gst"
                      type="text"
                      class="form-control"
                      placeholder="Enter GST Number"
                      formControlName="gst"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.gst"
                    />
                    <br />
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>National Id</label>
                    <input
                      id="aadhar"
                      type="text"
                      class="form-control"
                      placeholder="Enter National Id. "
                      formControlName="aadhar"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.aadhar"
                    />
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Passport No</label>
                    <input
                      id="passportNo"
                      type="text"
                      class="form-control"
                      placeholder="Enter Passport No. "
                      formControlName="passportNo"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.passportNo"
                    />
                    <br />
                  </div>
                </div>
              </div>
            </fieldset> -->

            <!-- Contact Details -->

            <!-- <fieldset>
              <legend>Contact Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Telephone</label>
                    <input
                      id="phone"
                      type="number"
                      min="0"
                      class="form-control"
                      placeholder="Enter Telephone "
                      formControlName="phone"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.phone"
                    />
                    <br />
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Lead Customer Type</label>
                    <p-dropdown
                      [options]="Customertype"
                      (onChange)="getcustType($event)"
                      filter="true"
                      filterBy="text"
                      formControlName="leadCustomerType"
                      optionLabel="text"
                      optionValue="text"
                      placeholder="Select Lead Customer Type"
                    >
                    </p-dropdown>
                  </div>]
                  <div
                    *ngIf="
                      this.customerGroupForm.controls.leadCustomerSubType.enabled &&
                      isCustSubTypeCon
                    "
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                  >
                    <label>Customer Lead Sub-Type</label>
                    <p-dropdown
                      [options]="CustomertypeSubtype"
                      filter="true"
                      filterBy="text"
                      optionLabel="text"
                      optionValue="text"
                      formControlName="leadCustomerSubType"
                      id="email"
                      placeholder="Enter Lead Sub-Type"
                    >
                    </p-dropdown>
                  </div>
                  <div
                    *ngIf="
                      this.customerGroupForm.controls.leadCustomerSubType.enabled &&
                      !isCustSubTypeCon
                    "
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                  >
                    <label>Customer Lead Sub-Type</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Lead Customer Sub Type"
                      formControlName="leadCustomerSubType"
                    />
                  </div>
                </div>
              </div>
            </fieldset> -->

            <!-- Secondary Contact Details -->
            <fieldset *ngIf="isMandatory">
              <legend>Secondary Contact Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <!-- Landline Number -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Landline Number</label>
                    <input
                      id="landlineNumber"
                      type="text"
                      class="form-control"
                      placeholder="Enter Landline Number"
                      formControlName="landlineNumber"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.landlineNumber.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.landlineNumber.errors?.minlength ||
                          customerGroupForm.controls.landlineNumber.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else landlineRange
                        "
                      >
                        Landline Number must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #landlineRange>
                        Landline Number must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>

                  <!-- Secondary Email -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Email</label>
                    <input
                      id="secondaryEmail"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary Email"
                      formControlName="secondaryEmail"
                    />
                  </div>

                  <!-- Secondary Phone 1 -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Phone 1</label>
                    <input
                      id="secondaryPhone"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary phone number"
                      formControlName="secondaryPhone"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.secondaryPhone.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.secondaryPhone.errors?.minlength ||
                          customerGroupForm.controls.secondaryPhone.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else phone1Range
                        "
                      >
                        Secondary Phone 1 must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #phone1Range>
                        Secondary Phone 1 must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>

                  <!-- Secondary Phone 2 -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Phone 2</label>
                    <input
                      id="altmobile1"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary phone number"
                      formControlName="altmobile1"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.altmobile1.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.altmobile1.errors?.minlength ||
                          customerGroupForm.controls.altmobile1.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else phone2Range
                        "
                      >
                        Secondary Phone 2 must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #phone2Range>
                        Secondary Phone 2 must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <!-- Secondary Phone 3 -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Phone 3</label>
                    <input
                      id="altmobile2"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary phone number"
                      formControlName="altmobile2"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.altmobile2.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.altmobile2.errors?.minlength ||
                          customerGroupForm.controls.altmobile2.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else phone3Range
                        "
                      >
                        Secondary Phone 3 must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #phone3Range>
                        Secondary Phone 3 must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>

                  <!-- Secondary Phone 4 -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Phone 4</label>
                    <input
                      id="altmobile3"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary phone number"
                      formControlName="altmobile3"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.altmobile3.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.altmobile3.errors?.minlength ||
                          customerGroupForm.controls.altmobile3.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else phone4Range
                        "
                      >
                        Secondary Phone 4 must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #phone4Range>
                        Secondary Phone 4 must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>

                  <!-- Secondary Phone 5 -->
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary Phone 5</label>
                    <input
                      id="altmobile4"
                      type="text"
                      class="form-control"
                      placeholder="Enter Secondary phone number"
                      formControlName="altmobile4"
                      [attr.maxlength]="commondropdownService.maxMobileLength"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.altmobile4.errors
                      }"
                    />
                    <div
                      *ngIf="
                        submitted &&
                        (customerGroupForm.controls.altmobile4.errors?.minlength ||
                          customerGroupForm.controls.altmobile4.errors?.maxlength)
                      "
                      class="error text-danger"
                    >
                      <ng-container
                        *ngIf="
                          commondropdownService.minMobileLength ===
                            commondropdownService.maxMobileLength;
                          else phone5Range
                        "
                      >
                        Secondary Phone 5 must be exactly
                        {{ commondropdownService.minMobileLength }} digits.
                      </ng-container>
                      <ng-template #phone5Range>
                        Secondary Phone 5 must be between
                        {{ commondropdownService.minMobileLength }} and
                        {{ commondropdownService.maxMobileLength }} digits.
                      </ng-template>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>

            <!-- Payment Details -->

            <!-- <fieldset>
              <legend>Payment Details</legend>
              <div class="boxWhite">
                <div formGroupName="paymentDetails">
                  <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Amount</label>
                      <input
                        id="amount"
                        type="number"
                        min="0"
                        class="form-control"
                        placeholder="Enter Amount "
                        formControlName="amount"
                        [readonly]="ifReadonlyExtingInput && customerGroupForm.value.amount"
                      />
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Reference No</label>
                      <input
                        id="referenceno"
                        type="text"
                        class="form-control"
                        placeholder="Enter Reference No"
                        formControlName="referenceno"
                        [readonly]="ifReadonlyExtingInput && customerGroupForm.value.referenceno"
                      />
                      <br />
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Payment Date</label>
                      <div>
                        <input
                          type="date"
                          formControlName="paymentdate"
                          placeholder="DD/MM/YYYY"
                          class="form-control"
                          [readonly]="ifReadonlyExtingInput && customerGroupForm.value.paymentdate"
                        />
                      </div>

                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                      <label>Payment mode</label>

                      <p-dropdown
                        [options]="commondropdownService.commonListPaymentData"
                        optionValue="text"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a  Payment mode"
                        formControlName="paymode"
                        [readonly]="ifReadonlyExtingInput && customerGroupForm.value.paymode"
                      >
                      </p-dropdown>
                      <br />
                    </div>
                  </div>
                </div>
              </div>
            </fieldset> -->

            <!-- Permanent Address Details -->

            <!-- <fieldset>
              <legend>Permanent Address Details</legend>
              <div class="boxWhite">
                <div class="form-group form-check">
                  <input
                    type="checkbox"
                    id="presentAddress"
                    class="form-check-input"
                    [checked]="presentCheckForPermanent"
                    (change)="samepresentAddress($event, 'permanet')"
                  />
                  <label for="presentAddress" class="form-check-label" style="margin-left: 1rem">
                    Same as a present address
                  </label>
                </div>
                <div class="row" [formGroup]="permanentGroupForm">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Landmark</label>
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Enter Landmark"
                      name="landmark"
                      id="landmark"
                      formControlName="landmark"
                      [readonly]="ifReadonlyExtingInput && permanentGroupForm.value.landmark"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>{{ pincodeTitle }}</label>

                    <p-dropdown
                      [options]="pincodeDD"
                      optionValue="pincodeid"
                      optionLabel="pincode"
                      filter="true"
                      filterBy="pincode"
                      placeholder="Select a {{ pincodeTitle }}"
                      formControlName="pincodeId"
                      (onChange)="selectPINCODEChange($event, 'permanent')"
                      [readonly]="ifReadonlyExtingInput && permanentGroupForm.value.pincodeId"
                    ></p-dropdown>
                    <div></div>
                  </div>

                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>{{ areaTitle }}</label>
                    <p-dropdown
                      *ngIf="!selectAreaListPermanent"
                      [options]="commondropdownService.areaData"
                      optionValue="id"
                      optionLabel="name"
                      filter="true"
                      filterBy="name"
                      placeholder="Select a {{ areaTitle }}"
                      formControlName="areaId"
                      (onChange)="selectAreaChange($event, 'permanent')"
                      [disabled]="ifReadonlyExtingInput && permanentGroupForm.value.areaId"
                    ></p-dropdown>
                    <div></div>
                    <p-dropdown
                      *ngIf="selectAreaListPermanent"
                      [options]="permanentareaAvailableList"
                      optionValue="id"
                      optionLabel="name"
                      filter="true"
                      filterBy="name"
                      placeholder="Select a {{ areaTitle }}"
                      formControlName="areaId"
                      (onChange)="selectAreaChange($event, 'permanent')"
                      [disabled]="ifReadonlyExtingInput && permanentGroupForm.value.areaId"
                    >
                    </p-dropdown>
                    <div></div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>{{ cityTitle }}</label>
                    <select
                      class="form-control"
                      style="width: 100%"
                      name="cityId"
                      id="cityId"
                      formControlName="cityId"
                      disabled
                    >
                      <option value="">Select {{ cityTitle }}</option>
                      <option
                        *ngFor="let item of commondropdownService.cityListData"
                        value="{{ item.id }}"
                      >
                        {{ item.name }}
                      </option>
                    </select>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>{{ stateTitle }}</label>
                    <select
                      class="form-control"
                      style="width: 100%"
                      name="stateId"
                      id="stateId"
                      formControlName="stateId"
                      disabled
                    >
                      <option value="">Select {{ stateTitle }}</option>
                      <option
                        *ngFor="let item of commondropdownService.stateListData"
                        value="{{ item.id }}"
                      >
                        {{ item.name }}
                      </option>
                    </select>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>{{ countryTitle }}</label>
                    <select
                      class="form-control"
                      style="width: 100%"
                      name="countryId"
                      id="countryId"
                      formControlName="countryId"
                      disabled
                    >
                      <option value="">Select {{ countryTitle }}</option>
                      <option
                        *ngFor="let item of commondropdownService.countryListData"
                        value="{{ item.id }}"
                      >
                        {{ item.name }}
                      </option>
                    </select>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>Street Name</label>
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Enter Street Name"
                      name="streetName"
                      id="streetName"
                      formControlName="streetName"
                      [readonly]="ifReadonlyExtingInput && permanentGroupForm.value.streetName"
                    />
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15 top">
                    <label>House No</label>
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Enter House No"
                      name="houseNo"
                      id="houseNo"
                      formControlName="houseNo"
                      [readonly]="ifReadonlyExtingInput && permanentGroupForm.value.houseNo"
                    />
                    <br />
                  </div>
                </div>
              </div>
            </fieldset> -->

            <!-- Additional Service Details -->
            <!-- 
            <fieldset>
              <legend>Additional Service Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Voice Service Type</label>
                    <input
                      id="voicesrvtype"
                      type="text"
                      class="form-control"
                      placeholder="Enter Voice Service Type"
                      formControlName="voicesrvtype"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.voicesrvtype"
                    />
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>DID No</label>
                    <input
                      id="didno"
                      type="number"
                      min="0"
                      class="form-control"
                      placeholder="Enter Didno"
                      formControlName="didno"
                      [readonly]="ifReadonlyExtingInput && customerGroupForm.value.didno"
                    />
                    <br />
                  </div>
                </div>
              </div>
            </fieldset> -->

            <!--  charge Details -->

            <fieldset class="hide">
              <legend>Charge Details</legend>
              <div class="boxWhite">
                <table [formGroup]="chargeGroupForm" style="width: 100%">
                  <tr>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="commondropdownService.chargeByTypeData"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Charge"
                        formControlName="chargeid"
                        (onChange)="selectcharge($event)"
                        [ngClass]="{
                          'is-invalid': chargesubmitted && chargeGroupForm.controls.chargeid.errors
                        }"
                      ></p-dropdown>
                    </td>

                    <td style="text-align: center; padding: 0 10px 0 0">
                      <input
                        class="form-control"
                        type="number"
                        min="0"
                        placeholder="Enter Actual Price"
                        name="actualprice"
                        id="actualprice        "
                        formControlName="actualprice"
                        *ngIf="selectchargeValueShow"
                        disabled
                      />

                      <input
                        class="form-control"
                        type="number"
                        min="0"
                        *ngIf="!selectchargeValueShow"
                        placeholder="Enter Actual Price"
                        name="actualprice"
                        id="actualprice        "
                        formControlName="actualprice"
                        [ngClass]="{
                          'is-invalid':
                            chargesubmitted && chargeGroupForm.controls.actualprice.errors
                        }"
                      />
                    </td>

                    <td style="text-align: center; padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="chargeType"
                        optionValue="label"
                        optionLabel="label"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Type"
                        formControlName="type"
                        (onChange)="selectTypecharge($event)"
                        [ngClass]="{
                          'is-invalid': chargesubmitted && chargeGroupForm.controls.type.errors
                        }"
                      ></p-dropdown>
                    </td>

                    <td
                      *ngIf="chargeGroupForm.value.type == 'Recurring'"
                      style="text-align: center; padding: 0 10px 0 0"
                    >
                      <p-dropdown
                        [options]="billingCycle"
                        optionValue="label"
                        optionLabel="label"
                        filter="true"
                        filterBy="label"
                        placeholder="Select a Billing Cycle"
                        formControlName="billingCycle"
                        [ngClass]="{
                          'is-invalid': submitted && chargefromgroup.controls.billingCycle.errors
                        }"
                      ></p-dropdown>
                    </td>

                    <td style="text-align: center; padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="planDropdownInChageData"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Plan *"
                        formControlName="planid"
                        (onChange)="getPlanValidityForChagre($event)"
                        [ngClass]="{
                          'is-invalid': chargesubmitted && chargeGroupForm.controls.planid.errors
                        }"
                      ></p-dropdown>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div style="display: flex">
                        <div style="width: 40%">
                          <input
                            id="validity"
                            type="number"
                            min="1"
                            class="form-control"
                            placeholder="Enter Validity"
                            formControlName="validity"
                            readonly
                          />
                        </div>
                        <div style="width: 60%; height: 34px">
                          <p-dropdown
                            [options]="commondropdownService.validityUnitData"
                            optionValue="label"
                            optionLabel="label"
                            filter="true"
                            filterBy="label"
                            placeholder="Select a Unit"
                            formControlName="unitsOfValidity"
                            [disabled]="true"
                          >
                          </p-dropdown>
                        </div>
                      </div>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <input
                        class="form-control"
                        type="number"
                        min="0"
                        placeholder="Enter New Price"
                        name="price"
                        id="price"
                        formControlName="price"
                        [ngClass]="{
                          'is-invalid': chargesubmitted && chargeGroupForm.controls.price.errors
                        }"
                      />
                    </td>
                    <td style="text-align: left; padding: 0 10px 0 0">
                      <input
                        disabled
                        class="form-control"
                        formControlName="discount"
                        id="discount"
                        min="0"
                        name="discount"
                        placeholder="Enter discount"
                        type="number"
                      />
                    </td>
                    <td style="text-align: center; width: 5%">
                      <button
                        style="object-fit: cover; padding: 5px 8px"
                        class="btn btn-primary"
                        (click)="onAddoverChargeListField()"
                      >
                        <i class="fa fa-plus-square" aria-hidden="true"></i>
                        Add
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.chargeid.errors"
                      >
                        <div class="error text-danger">Charge is required.</div>
                      </div>
                    </td>
                    <td>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.actualprice.errors"
                      >
                        <div class="error text-danger">Charge Amount is required.</div>
                      </div>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.type.errors"
                      >
                        <div class="error text-danger">Charge Type is required.</div>
                      </div>
                    </td>
                    <td
                      *ngIf="chargeGroupForm.value.type == 'Recurring'"
                      style="text-align: center; padding: 0 10px 0 0"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.billingCycle.errors"
                      >
                        <div class="error text-danger">Billing Cycle Type is required.</div>
                      </div>
                    </td>
                    <td>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.planid.errors"
                      >
                        <div class="error text-danger">Plan is required.</div>
                      </div>
                    </td>
                    <td style="text-align: center; padding: 0 10px 0 0"></td>
                    <td style="text-align: center; padding: 0 10px 0 0">
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.price.errors"
                      >
                        <div class="error text-danger">New Price is required.</div>
                      </div>
                    </td>
                    <td style="text-align: center; width: 5%"></td>
                  </tr>
                </table>

                <div
                  class="errorWrap text-danger"
                  *ngIf="this.chargeGroupForm.value.price < this.chargeGroupForm.value.actualprice"
                >
                  <div class="error text-danger">
                    New Price must not be less than the actual charge price
                  </div>
                </div>

                <div
                  class="errorWrap text-danger"
                  *ngIf="
                    payMappingListFromArray.controls?.length == 0 &&
                    customerGroupForm.value.plangroupid == null
                  "
                >
                  <div class="error text-danger">Please Add Plan details first!</div>
                </div>

                <table class="table coa-table" style="margin-top: 3rem">
                  <thead>
                    <tr>
                      <th style="text-align: center">Charge Name</th>
                      <th style="text-align: center">Charge Amount</th>
                      <th style="text-align: center">Charge Type</th>
                      <th style="text-align: center">Plan Name</th>
                      <th style="text-align: center">Plan Validity</th>
                      <th style="text-align: center">New Price</th>
                      <th style="text-align: center">Discount (%)</th>
                      <th style="text-align: right; width: 5%; padding: 8px">Delete</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let row of overChargeListFromArray.controls
                          | paginate
                            : {
                                id: 'overChargeListFromArrayData',
                                itemsPerPage: overChargeListItemPerPage,
                                currentPage: currentPageoverChargeList,
                                totalItems: overChargeListtotalRecords
                              };
                        let index = index
                      "
                    >
                      <td style="padding-left: 8px">
                        <p-dropdown
                          [options]="commondropdownService.chargeByTypeData"
                          optionValue="id"
                          optionLabel="name"
                          filter="true"
                          filterBy="name"
                          [formControl]="row.get('chargeid')"
                          [disabled]="true"
                        ></p-dropdown>
                        <div></div>
                      </td>
                      <td>
                        <input
                          class="form-control"
                          type="number"
                          min="0"
                          placeholder="Enter Actual Price"
                          name="actualprice"
                          id="actualprice"
                          [formControl]="row.get('actualprice')"
                          readonly
                        />
                      </td>
                      <td style="padding-left: 8px">
                        <p-dropdown
                          [options]="chargeType"
                          optionValue="label"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          [formControl]="row.get('type')"
                          [disabled]="true"
                        ></p-dropdown>
                        <div></div>
                      </td>
                      <td>
                        <select
                          class="form-control"
                          style="width: 100%"
                          name="planId"
                          id="planId"
                          [formControl]="row.get('planid')"
                          disabled
                        >
                          <option value="">Select Plan</option>
                          <option
                            *ngFor="let item of commondropdownService.postpaidplanData"
                            value="{{ item.id }}"
                          >
                            {{ item.name }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <div style="display: flex">
                          <div style="width: 40%">
                            <input
                              id="validity"
                              type="number"
                              min="1"
                              class="form-control"
                              placeholder="Enter Validity"
                              [formControl]="row.get('validity')"
                              readonly
                            />
                          </div>
                          <div style="width: 60%; height: 34px">
                            <p-dropdown
                              [options]="commondropdownService.validityUnitData"
                              optionValue="label"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              [formControl]="row.get('unitsOfValidity')"
                              [disabled]="true"
                            ></p-dropdown>
                          </div>
                        </div>
                      </td>
                      <td>
                        <input
                          class="form-control"
                          type="number"
                          min="0"
                          placeholder="Enter Price"
                          name="price"
                          id="price"
                          [formControl]="row.get('price')"
                          readonly
                        />
                      </td>
                      <td>
                        <input
                          disabled
                          class="form-control"
                          [formControl]="row.get('discount')"
                          id="discount"
                          min="0"
                          name="discount"
                          placeholder="Enter discount"
                          type="number"
                        />
                      </td>
                      <td style="text-align: right">
                        <a
                          id="deleteAtt"
                          href="javascript:void(0)"
                          (click)="deleteConfirmonChargeField(index, 'Charge')"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="row">
                  <div class="col-md-12">
                    <pagination-controls
                      id="overChargeListFromArrayData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedOverChargeList($event)"
                    ></pagination-controls>
                  </div>
                </div>
                <br />
              </div>
            </fieldset>

            <!-- Mac Mappping List -->
            <!-- 
            <fieldset>
              <legend>Mac Mapping List</legend>
              <div class="boxWhite">
                <div class="row" [formGroup]="macGroupForm">
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Enter MACAddress"
                      name="macAddress"
                      id="macAddress"
                      formControlName="macAddress"
                      [ngClass]="{
                        'is-invalid': macsubmitted && macGroupForm.controls.macAddress.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="macsubmitted && macGroupForm.controls.macAddress.errors"
                    >
                      <div class="error text-danger">Mac Address is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <button
                      style="object-fit: cover; padding: 5px 8px"
                      class="btn btn-primary"
                      (click)="onAddMACList()"
                    >
                      <i class="fa fa-plus-square" aria-hidden="true"></i>
                      Add
                    </button>
                  </div>
                </div>

                <table class="table coa-table" style="margin-top: 10px">
                  <thead>
                    <tr>
                      <th style="text-align: center; width: 10%">MACAddress</th>
                      <th style="text-align: right; width: 10%; padding: 8px">Delete</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let row of custMacMapppingListFromArray.controls
                          | paginate
                            : {
                                id: 'custMacMapppingListFromArrayData',
                                itemsPerPage: custMacMapppingListtemPerPage,
                                currentPage: currentPagecustMacMapppingList,
                                totalItems: custMacMapppingListtotalRecords
                              };
                        let item = index
                      "
                    >
                      <td>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="Enter MACAddress"
                          name="macAddress"
                          id="macAddress"
                          [formControl]="row.get('macAddress')"
                        />
                      </td>
                      <td style="text-align: right">
                        <a
                          id="deleteAtt"
                          href="javascript:void(0)"
                          (click)="deleteConfirmonChargeField(item, 'MAC')"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <pagination-controls
                  id="custMacMapppingListFromArrayData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangesOnCreateEdit($event)"
                ></pagination-controls>
                <br />
              </div>
            </fieldset> -->

            <!-- Save and Edit Button -->
            <div class="addUpdateBtn" style="margin-top: 3.5rem">
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="!myFinalCheck && !isLeadEdit && createAccess"
                id="submit"
                (click)="addEditLead('', '', 'no')"
              >
                <i class="fa fa-check-circle"></i>
                Add Lead
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="!myFinalCheck && isLeadEdit && editAccess"
                id="submit"
                (click)="addEditLead(customerGroupForm.controls.id.value, '', 'no')"
              >
                <i class="fa fa-check-circle"></i>
                Update Lead
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="myFinalCheck && isLeadEdit && editAccess"
                id="submit"
                (click)="leadToCAFConversion()"
              >
                <i class="fa fa-check-circle"></i>
                Approve & Convert Lead To CAF
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Follow up schedulling screen Start -->

<div class="row" *ngIf="openFollowUpSchedulling">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="'displayflex">
          <div class="panel-title">Follow Up List</div>
        </div>
        <div class="right">
          <button
            *ngIf="scheduleFollowUpAccess"
            class="btn btn-primary statusbtn"
            style="
              padding: 12px;
              margin-left: 5px;
              background-color: #f7b206;
              border: none;
              outline: none;
            "
            (click)="scheduleFollowupPopupOpen()"
          >
            <i class="fa fa-calendar" style="font-size: 19px"></i>
            &nbsp; Schedule Follow Up
          </button>
        </div>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-lg-12 col-md-12 text-center" *ngIf="followUpList.length === 0">
            No Records Found
          </div>
          <div class="col-lg-12 col-md-12" *ngIf="followUpList.length > 0">
            <div [id]="tableWrapper">
              <div [id]="scrollId">
                <table class="table">
                  <thead>
                    <tr>
                      <th>FollowUp Name</th>
                      <th>FollowUp Date & Time</th>
                      <th>Remarks</th>
                      <th>Status</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container *ngFor="let followUpDetails of followUpList">
                      <tr
                        [ngStyle]="{
                          'background-color': checkFollowUpDatetimeOutDate(followUpDetails)
                            ? '#ffff0096'
                            : ''
                        }"
                      >
                        <td>{{ followUpDetails?.followUpName }}</td>
                        <td>
                          {{ followUpDetails?.followUpDatetime | date: "dd/MM/yyyy HH:mm:ss" }}
                        </td>
                        <td *ngIf="followUpDetails?.remarks != 'null'">
                          {{ followUpDetails?.remarks }}
                        </td>
                        <td *ngIf="followUpDetails?.remarks == 'null'">-</td>
                        <td>
                          <span
                            *ngIf="
                              followUpDetails?.status == 'Closed' ||
                              followUpDetails?.status == 'closed'
                            "
                            class="badge badge-info"
                            >Closed</span
                          >
                          <span
                            *ngIf="
                              followUpDetails?.status == 'Pending' ||
                              followUpDetails?.status == 'pending'
                            "
                            class="badge badge-danger"
                            >Pending</span
                          >
                          <span
                            *ngIf="
                              followUpDetails?.status == 'ReSchedule' ||
                              followUpDetails?.status == 'reschedule'
                            "
                            class="badge badge-info"
                            >ReSchedule</span
                          >
                        </td>
                        <td class="btnAction">
                          <button
                            [disabled]="
                              this.staffid != this.leadMasterObj?.nextApproveStaffId ||
                              followUpDetails?.status == 'Closed' ||
                              followUpDetails?.status == 'closed'
                            "
                            *ngIf="rescheduleFollowUpAccess"
                            type="button"
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 3px;
                            "
                            title="Reschedule FollowUp"
                            (click)="rescheduleFollowUp(followUpDetails)"
                          >
                            <img
                              style="width: 25px; height: 25px"
                              src="assets/img/D_Extend-Expiry-Date_Y.png"
                            />
                          </button>
                          <button
                            *ngIf="closeFollowUpAccess"
                            [disabled]="
                              this.staffid != this.leadMasterObj?.nextApproveStaffId ||
                              followUpDetails?.status == 'Closed' ||
                              followUpDetails?.status == 'closed'
                            "
                            type="button"
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 3px;
                            "
                            title="Close FollowUp"
                            (click)="closeFollowUp(followUpDetails)"
                          >
                            <img style="width: 25px; height: 25px" src="assets/img/reject.jpg" />
                          </button>
                          <button
                            *ngIf="remarkFollowUpAccess"
                            type="button"
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 3px;
                            "
                            title="Remark FollowUp"
                            (click)="remarkFollowUp(followUpDetails)"
                          >
                            <img style="width: 25px; height: 25px" src="assets/img/icons-03.png" />
                          </button>
                          <button
                            *ngIf="callAccess"
                            type="button"
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 3px;
                            "
                            title="Call"
                            (click)="makeACall()"
                          >
                            <img
                              style="width: 25px; height: 25px"
                              src="assets/img/phone-icon.png"
                            />
                          </button>
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Follow up schedulling screen end -->

<!-- Audit Trail screen Start -->

<div class="row" *ngIf="openAuditTrailScreen">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="panel-title">Audit Trail List</div>
      </div>
      <div class="panel-body">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <div [id]="tableWrapperAudit">
                <div [id]="scrollIdAudit">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Created On</th>
                        <th>Action</th>
                        <th>Log</th>
                        <th>Staff Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let leadAudit of leadAuditList">
                        <td>{{ leadAudit?.createdOn | date: "dd/MM/yyyy HH:mm:ss" }}</td>
                        <td>{{ leadAudit?.auditName }}</td>
                        <td>{{ leadAudit?.name }}</td>
                        <td>{{ leadAudit?.staffName }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Audit Trial screen end -->

<!-- View Lead Details Grid -->
<div class="row" *ngIf="listView">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Lead List</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#leadList"
            aria-expanded="false"
            aria-controls="leadList"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="leadList" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12" *ngIf="leadListData?.length > 0">
              <table class="table">
                <thead>
                  <tr>
                    <th width="12%">Customer Name</th>
                    <th width="8%">Lead No.</th>
                    <th width="12%">Mobile Number</th>
                    <th width="10%">Lead Source</th>
                    <th width="14%">Lead Sub Source</th>
                    <th width="12%">Lead Status</th>
                    <th width="12%">CAF Status</th>
                    <th width="10%">Assignee Name</th>
                    <th width="12%">ISP Name</th>
                    <th width="10%">CreatedBy Details</th>
                    <th width="12%">Converted Date</th>
                    <th width="12%">Converted By</th>
                    <th width="15%">Remaining time</th>
                    <th width="28%">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of leadListData
                        | paginate
                          : {
                              id: 'leadListpageData',
                              itemsPerPage: leadListdataitemsPerPage,
                              currentPage: currentPageLeadListdata,
                              totalItems: leadListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td *ngIf="data.firstname || data.lastname">
                      <a
                        href="javascript:void(0)"
                        style="color: #f7b206"
                        (click)="leadDetailOpen(data.id)"
                      >
                        {{ data.title }} {{ data.firstname }} {{ data.lastname }}
                      </a>
                    </td>
                    <td *ngIf="!data.firstname && !data.lastname">-</td>
                    <td>{{ data.leadNo || "-" }}</td>
                    <td>{{ data.mobile || "-" }}</td>
                    <td>{{ data.leadSourceName || "-" }}</td>
                    <td>{{ getLeadSourceName(data) }}</td>
                    <td [ngSwitch]="data.leadStatus">
                      <span *ngSwitchCase="'Inquiry'" class="badge badge-success">{{
                        data.leadStatus
                      }}</span>
                      <span *ngSwitchCase="'Rejected'" class="badge badge-danger">{{
                        data.leadStatus
                      }}</span>
                      <span *ngSwitchCase="'Re-Inquiry'" class="badge badge-info">{{
                        data.leadStatus
                      }}</span>
                      <span *ngSwitchCase="'Converted'" class="badge badge-success">{{
                        data.leadStatus
                      }}</span>
                      <span *ngSwitchDefault>-</span>
                    </td>
                    <td
                      *ngIf="
                        data.cstatus === 'Active' ||
                        data.cstatus === 'New Activation' ||
                        data.cstatus === 'NewActivation'
                      "
                    >
                      <span class="badge badge-success">{{ data.cstatus }}</span>
                    </td>
                    <td *ngIf="data.cstatus === 'Rejected' || data.cstatus === 'Reject'">
                      <span class="badge badge-danger">{{ data.cstatus }}</span>
                    </td>
                    <td *ngIf="data.cstatus == null || data.cstatus === ''">-</td>

                    <td *ngIf="data.assigneeName">{{ data.assigneeName }}</td>

                    <td
                      *ngIf="
                        !data.assigneeName &&
                        (data.leadStatus === 'Inquiry' || data.leadStatus === 'Re-Inquiry')
                      "
                      data-toggle="tooltip"
                      data-placement="bottom"
                      title="Lead Assignee process is running. Please wait for few seconds and refresh the lead data and try again."
                    >
                      In Progress
                    </td>
                    <td
                      *ngIf="
                        !data.assigneeName &&
                        (data.leadStatus === 'Converted' || data.leadStatus === 'Rejected')
                      "
                    >
                      -
                    </td>
                    <td>{{ data.mvnoName }}</td>
                    <td>
                      <a
                        (click)="openStaffDetailModal(data.createdBy)"
                        href="javascript:void(0)"
                        style="color: #f7b206"
                      >
                        {{ data.createdByName }}
                      </a>
                    </td>
                    <td *ngIf="!data.cafConvertedDate">-</td>
                    <td *ngIf="data.cafConvertedDate">{{ data.cafConvertedDate }}</td>
                    <td *ngIf="!data.cafCovertedStaffName">-</td>
                    <td *ngIf="data.cafCovertedStaffName">{{ data.cafCovertedStaffName }}</td>
                    <td>{{ data.remainTime }}</td>
                    <td class="btnAction">
                      <button
                        *ngIf="notesAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Add Notes"
                        id=" addNotes"
                        (click)="addNotesSetFunction(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Add Notes"
                      >
                        <img src="assets/img/icons-03.png" />
                      </button>

                      <button
                        *ngIf="editAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Edit Lead"
                        id="edit-button"
                        [disabled]="
                          !(this.staffid === data.nextApproveStaffId) ||
                          data.leadStatus === 'Converted' ||
                          data.leadStatus === 'Rejected'
                        "
                        (click)="editLead(data.id, false)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Manage Lead"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </button>
                      <!-- <button
                        *ngIf="deleteAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Delete Lead"
                        id="delete-button"
                        [disabled]="
                          data.leadStatus === 'Converted' ||
                          !(this.staffid === data.nextApproveStaffId)
                        "
                        (click)="deleteConfirmonLeadData(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Delete Lead"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </button>-->
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        [disabled]="
                          (data.nextApproveStaffId != null &&
                            (data.leadStatus !== 'Inquiry' || data.leadStatus !== 'Re-Inquiry')) ||
                          (data.nextApproveStaffId == null && data.leadStatus === 'Rejected')
                        "
                        (click)="pickModalOpen(data)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        *ngIf="data.nextApproveStaffId"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Approve Lead Workflow"
                        id="approveLead"
                        [disabled]="
                          this.staffid != data.nextApproveStaffId ||
                          data.leadStatus === 'Rejected' ||
                          data.leadStatus === 'Converted'
                        "
                        (click)="approveOrRejectLeadPopup(data, 'Approve')"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Approve Lead Workflow"
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>

                      <button
                        *ngIf="data.nextApproveStaffId"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Reject Lead Workflow"
                        id="rejectLead"
                        [disabled]="
                          !(this.staffid === data.nextApproveStaffId) ||
                          data.leadStatus === 'Rejected' ||
                          data.leadStatus === 'Converted'
                        "
                        (click)="approveOrRejectLeadPopup(data, 'Reject')"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Reject Lead Workflow"
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>

                      <button
                        *ngIf="statusAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="View Lead Status"
                        [disabled]="data.leadStatus === 'Rejected'"
                        id="operateLeadStatus"
                        (click)="viewLeadStatusPopupOpen(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="View Lead Status"
                      >
                        <img src="assets/img/E_Status_Y.png" />
                      </button>

                      <button
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="ReOpen Lead"
                        *ngIf="data.leadStatus === 'Rejected' && reopenLeadAccess"
                        [disabled]="!data.leadReopenAllow"
                        (click)="reopenLeadConfirmation(data.id, data.leadStatus)"
                        id="reopenLead"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="ReOpen Lead"
                      >
                        <img src="assets/img/icons-04.png" />
                      </button>
                      <button
                        *ngIf="closeAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Close Lead"
                        id="closeLead"
                        [disabled]="
                          data.leadStatus === 'Rejected' ||
                          data.leadStatus === 'Converted' ||
                          !(this.staffid === data.nextApproveStaffId)
                        "
                        (click)="rejectLeadPopupOpen(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Close Lead"
                      >
                        <img src="assets/img/01_Lead_Close_Y.png" alt="Close Lead" />
                      </button>
                      <button
                        *ngIf="uploadAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        [disabled]="
                          !(this.staffid === data.nextApproveStaffId) ||
                          data.leadStatus === 'Converted'
                        "
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Upload Documents"
                        id="edit-button"
                        [routerLink]="['/home/<USER>', data.id]"
                      >
                        <img width="32" height="32" src="assets/img/up.jpg" />
                      </button>
                      <button
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        (click)="assignWorkflow(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Assign Workflow"
                        id="edit-button"
                        *ngIf="
                          !data.nextApproveStaffId &&
                          !data.nextTeamMappingId &&
                          data.leadStatus === 'Inquiry'
                        "
                      >
                        <img width="32" height="32" src="assets/img/diagram.png" />
                      </button>

                      <button
                        *ngIf="reassignAccess"
                        class="disable-button"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        [disabled]="
                          data.leadStatus === 'Rejected' ? true : false || !data?.nextApproveStaffId
                        "
                        data-toggle="tooltip"
                        data-placement="top"
                        id="assign-button"
                        title="Reassign Lead"
                        (click)="onClickAssignLead(data.id, data.leadStatus)"
                      >
                        <img src="assets/img/icons-02.png" />
                      </button>

                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="View Details"
                        (click)="openModal(data.id)"
                      >
                        <img
                          src="assets/img/eye-icon.png"
                          alt="View Details"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                      <button
                        *ngIf="data.leadStatus == 'Rejected'"
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="View Reject reason"
                        (click)="openRejectReasonModel(data)"
                      >
                        <img
                          src="assets/img/eye-icon.png"
                          alt="View Reject reason"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="display: flex">
                <pagination-controls
                  id="leadListpageData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedLeadList($event)"
                >
                </pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [(ngModel)]="leadListdataitemsPerPage"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
            <div class="col-lg-12 col-md-12" *ngIf="leadListData && leadListData?.length === 0">
              No Records Found
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- View Lead Details End -->

<!-- select Parent Customer popup-->

<!-- Add Notes Model Start-->

<!-- Follow up schedulling popup start -->

<!-- Follow up schedulling popup end -->

<!-- Follow up close popup start -->

<!-- Follow up close popup end -->

<!-- Follow up reschedulling popup start -->

<!-- Follow up reschedulling popup end -->

<!-- remarkScheduleFollowup popup end start -->

<!-- remarkScheduleFollowup popup end -->

<!-- approveOrRejectLeadPopup popup end start -->

<!-- approveOrRejectLeadPopup popup end -->

<!-- Open close lead popup START -->

<!-- Open close lead popup END -->

<!-- View Specific Lead Details Screen START -->
<div class="row" *ngIf="isSpecificLeadOpen">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to View Lead List"
            (click)="viewLead()"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ leadDetailData.firstname }} {{ leadDetailData.lastname }} Lead Details
          </h3>
        </div>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#leadDetails"
            aria-expanded="false"
            aria-controls="leadDetails"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="leadDetails">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <p-tabView styleClass="x-tab-view">
                <p-tabPanel class="header" header="Basic Lead Details">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <p-card>
                        <div class="row">
                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Lead No :</label>
                            <span>
                              {{ leadDetailData.leadNo }}
                            </span>
                          </div>-->
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Lead Customer Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadCustomerType">{{
                              leadDetailData.leadCustomerType
                            }}</span>
                            <span *ngIf="!leadDetailData.leadCustomerType">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Lead Customer Sector:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadCustomerSector">{{
                              leadDetailData.leadCustomerSector
                            }}</span>
                            <span *ngIf="!leadDetailData.leadCustomerSector">-</span>
                          </div>
                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Category :</label>
                            <span>{{ leadDetailData.custcategory }}</span>
                            <span *ngIf="!leadDetailData.custcategory">-</span>
                          </div> -->
                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Sub Type :</label>
                            <span>{{ leadDetailData.customerSubType }}</span>
                            <span *ngIf="!leadDetailData.customerSubType">-</span>
                          </div> -->
                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Sector :</label>
                            <span>{{ leadDetailData.customerSector }}</span>
                            <span *ngIf="!leadDetailData.customerSector">-</span>
                          </div> -->
                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Sub Sector :</label>
                            <span>{{ leadDetailData.customerSubSector }}</span>
                            <span *ngIf="!leadDetailData.customerSubSector">-</span>
                          </div> -->
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Require Service Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.requireServiceType">{{
                              leadDetailData.requireServiceType
                            }}</span>
                            <span *ngIf="!leadDetailData.requireServiceType">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Lead Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadType">{{
                              leadDetailData.leadType
                            }}</span>
                            <span *ngIf="!leadDetailData.leadType">-</span>
                          </div>
                          <div
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup"
                            *ngIf="
                              leadDetailData.feasibility === 'N/A' &&
                              leadDetailData.leadCategory === 'Existing Customer'
                            "
                          >
                            <label class="datalbl">Lead Category:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadCategory">{{
                              leadDetailData.leadCategory
                            }}</span>
                            <span *ngIf="!leadDetailData.leadCategory">-</span>
                          </div>
                          <div
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup"
                            *ngIf="
                              leadDetailData.feasibility !== 'N/A' ||
                              leadDetailData.leadCategory !== 'Existing Customer'
                            "
                          >
                            <label class="datalbl">Lead Category:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadCategory">{{
                              leadDetailData.leadCategory
                            }}</span>
                            <span *ngIf="!leadDetailData.leadCategory">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Lead Origin Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.leadOriginType">{{
                              leadDetailData.leadOriginType
                            }}</span>
                            <span *ngIf="!leadDetailData.leadOriginType">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Lead Source:&nbsp;&nbsp;&nbsp;</label>
                            <span>{{ leadDetailData.leadSourceName }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Feasibility :&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.feasibility">{{
                              leadDetailData.feasibility
                            }}</span>
                            <span *ngIf="!leadDetailData.feasibility">-</span>
                          </div>
                          <div
                            *ngIf="leadDetailData.feasibility === 'N/A'"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup"
                          >
                            <label class="datalbl">Feasibility Remark:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.feasibilityRemark">{{
                              leadDetailData.feasibilityRemark
                            }}</span>
                            <span *ngIf="!leadDetailData.feasibilityRemark">-</span>
                          </div>

                          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Contact Person :</label>
                            <span>{{ leadDetailData.contactperson }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">CAF No. :</label>
                            <span>{{ leadDetailData.cafno }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Username :</label>
                            <span>{{ leadDetailData.username }}</span>
                          </div>

                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Status :</label>
                            <span>{{
                              leadDetailData.status == "Ingrace" ||
                              leadDetailData.status == "INGRACE"
                                ? "InGrace"
                                : leadDetailData.status
                            }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Type :</label>
                            <span>{{ leadDetailData.custtype }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Calendar Type :</label>
                            <span>{{ leadDetailData.calendarType }}</span>
                          </div>

                          <div
                            *ngIf="customerPopName"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                          >
                            <label class="datalbl">POP Name :</label>
                            <span>{{ customerPopName }}</span>
                          </div>
                          <div
                            *ngIf="leadDetailData.billday"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                          >
                            <label class="datalbl">Bill Day :</label>
                            <span>{{ leadDetailData.billday }}</span>
                          </div>
                          <div
                            *ngIf="leadDetailData.nextBillDate"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                          >
                            <label class="datalbl">Next bill date :</label>
                            <span>
                              {{ leadDetailData.nextBillDate | date : "dd-MM-yyy" }}
                            </span>
                          </div>

                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">First Activation Date :</label>
                            <span>{{
                              leadDetailData.firstActivationDate | date : "dd-MM-yyyy hh:mm a"
                            }}</span>
                          </div>

                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Secondary Mobile Number :</label>
                            <span *ngIf="leadDetailData.altmobile">
                              <span *ngIf="leadDetailData.countryCode">
                                ( {{ leadDetailData.countryCode }} )&nbsp;
                              </span>
                              {{ leadDetailData.altmobile }}
                            </span>
                            <span *ngIf="!leadDetailData.altmobile">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Telephone :</label>
                            <span *ngIf="leadDetailData.phone">
                              {{ leadDetailData.phone }}
                            </span>
                            <span *ngIf="!leadDetailData.phone">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">FAX Number :</label>
                            <span *ngIf="leadDetailData.pan">
                              {{ leadDetailData.fax }}
                            </span>
                            <span *ngIf="!leadDetailData.fax">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Address :</label>
                            <span>{{ leadDetailData.addressList[0].landmark }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Bill to Name :</label>
                            <span *ngIf="customerBill">
                              {{ customerBill }}
                            </span>
                            <span *ngIf="!customerBill">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Bill to Address :</label>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">PAN Number :</label>
                            <span *ngIf="leadDetailData.pan">
                              {{ leadDetailData.pan }}
                            </span>
                            <span *ngIf="!leadDetailData.pan">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Activated By :</label>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Account Status :</label>
                            <span></span>
                          </div>

                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Ezybill ID :</label>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Date of Birth :</label>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Dunning Enable :</label>
                            <span>{{ leadDetailData.isDunningEnable }}</span>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Automatic notification :</label>
                            <span>{{ leadDetailData.isNotificationEnable }}</span>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Disable selfcare :</label>
                            <span></span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Disable renew (self care & north bound) :</label>
                            <span></span>
                          </div> -->
                        </div>
                      </p-card>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel header="Basic Customer Details">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <p-card>
                        <div class="row">
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Name :</label>
                            <span>
                              {{ leadDetailData.title }}
                              {{ leadDetailData.firstname }}
                              {{ leadDetailData.lastname }}
                            </span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Primary Mobile Number :</label>
                            <span>
                              <span *ngIf="leadDetailData.countryCode">
                                ( {{ leadDetailData.countryCode }} )&nbsp;
                              </span>
                              {{ leadDetailData.mobile }}
                            </span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Primary Email:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.email">{{ leadDetailData.email }}</span>
                            <span *ngIf="!leadDetailData.email">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Service Area:</label>
                            <span *ngIf="leadDetailData.serviceareaid">
                              {{ serviceAreaDATA }}
                            </span>
                            <span *ngIf="!leadDetailData.serviceareaid">-</span>
                          </div>
                          <div
                            *ngIf="leadDetailData.parentCustomerName"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                          >
                            <label class="datalbl">Parent Customer :</label>
                            <span>{{ leadDetailData.parentCustomerName }}</span>
                          </div>
                          <div
                            *ngIf="leadDetailData.invoiceType"
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                          >
                            <label class="datalbl">Invoice Type :</label>
                            <span>{{ leadDetailData.invoiceType }}</span>
                          </div>
                          <div
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup"
                            style="word-break: break-all; overflow-wrap: break-word"
                          >
                            <label class="datalbl">Gender:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.gender">{{ leadDetailData.gender }}</span>
                            <span *ngIf="!leadDetailData.gender">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Bill To:</label>

                            <span *ngIf="customerBill">
                              {{ customerBill }}
                            </span>
                            <span *ngIf="!customerBill">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">TIN/PAN No.</label>
                            <span *ngIf="leadDetailData.pan">{{ leadDetailData.pan }}</span>
                            <span *ngIf="!leadDetailData.pan">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">VAT:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.tinNo">{{ leadDetailData.tinNo }}</span>
                            <span *ngIf="!leadDetailData.tinNo">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb- 15 dataGroup">
                            <label class="datalbl">Unit No:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.blockNo">{{ leadDetailData.blockNo }}</span>
                            <span *ngIf="!leadDetailData.blockNo">-</span>
                          </div>
                        </div>
                      </p-card>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel header="Present Address Details">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <p-card>
                        <div class="row">
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Landmark:</label>
                            <span>
                              {{ leadDetailData.addressList[0]?.landmark }}
                            </span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">{{ pincodeTitle }} :</label>
                            <span>{{ presentAdressDATA?.code }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">{{ areaTitle }} :</label>
                            <span>{{ presentAdressDATA?.name }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">{{ cityTitle }} :</label>
                            <span>{{ presentAdressDATA?.cityName }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">{{ stateTitle }} :</label>
                            <span>{{ presentAdressDATA?.stateName }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">{{ countryTitle }} :</label>
                            <span>{{ presentAdressDATA?.countryName }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">{{ street }} :</label>
                            <span>{{ leadDetailData.addressList[0]?.streetName }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">{{ houseNo }} :</label>
                            <span>{{ leadDetailData.addressList[0]?.houseNo }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">Valley Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.outsideValley != null">Outside Valley</span>
                            <span *ngIf="leadDetailData.insideValley != null">Inside Valley</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl"
                              >{{
                                leadDetailData.outsideValley != null
                                  ? "Outside Valley"
                                  : "Inside Valley"
                              }}:&nbsp;&nbsp;</label
                            >
                            <span *ngIf="leadDetailData.outsideValley != null">{{
                              leadDetailData.outsideValley
                            }}</span>
                            <span *ngIf="leadDetailData.insideValley != null">{{
                              leadDetailData.insideValley
                            }}</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">Latitude:</label>
                            <span *ngIf="leadDetailData.latitude">
                              {{ leadDetailData.latitude }}
                            </span>
                            <span *ngIf="!leadDetailData.latitude">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">Longitude:</label>
                            <span *ngIf="leadDetailData.longitude">
                              {{ leadDetailData.longitude }}
                            </span>
                            <span *ngIf="!leadDetailData.longitude">-</span>
                          </div>
                        </div>
                      </p-card>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel header="Competitor Pack Details">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <p-card>
                        <div class="row">
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Competitor Pack Duration:</label>

                            <span *ngIf="leadDetailData.competitorDuration">
                              {{ leadDetailData.competitorDuration }}
                            </span>
                            <span *ngIf="!leadDetailData.competitorDuration">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Expiry:</label>

                            <span *ngIf="leadDetailData.expiry">
                              {{ leadDetailData.expiry }}
                            </span>
                            <span *ngIf="!leadDetailData.expiry">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Previous Month:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.previousMonth">{{
                              leadDetailData.previousMonth
                            }}</span>
                            <span *ngIf="!leadDetailData.previousMonth">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup">
                            <label class="datalbl">Previous Amount:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.previousAmount">{{
                              leadDetailData.previousAmount
                            }}</span>
                            <span *ngIf="!leadDetailData.previousAmount">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Customer Feedback:</label>

                            <span *ngIf="leadDetailData.feedback">
                              {{ leadDetailData.feedback }}
                            </span>
                            <span *ngIf="!leadDetailData.feedback">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">Current Pay:</label>

                            <span *ngIf="leadDetailData.amount">
                              {{ leadDetailData.amount }}
                            </span>
                            <span *ngIf="!leadDetailData.amount">-</span>
                          </div>
                        </div>
                      </p-card>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel header="Basic CAF Details">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <p-card>
                        <div class="row">
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">Title:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.title">{{ leadDetailData.title }}</span>
                            <span *ngIf="!leadDetailData.title">-</span>
                          </div>

                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                            <label class="datalbl">Contact Person:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.contactperson">{{
                              leadDetailData.contactperson
                            }}</span>
                            <span *ngIf="!leadDetailData.contactperson">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">CAF No:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.cafno">{{ leadDetailData.cafno }}</span>
                            <span *ngIf="!leadDetailData.cafno">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">AAA Username:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.username">{{
                              leadDetailData.username
                            }}</span>
                            <span *ngIf="!leadDetailData.username">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                            <label class="datalbl">AAA Password :</label>
                            <span>{{
                              showAAAPasswordDetail
                                ? leadDetailData?.password
                                : maskPassword(leadDetailData?.password)
                            }}</span>
                            <button
                              *ngIf="leadDetailData?.password"
                              type="button"
                              (click)="togglePassword()"
                              style="
                                background: none;
                                border: none;
                                cursor: pointer;
                                padding-left: 10px;
                              "
                            >
                              <i
                                class="fa"
                                [ngClass]="showAAAPasswordDetail ? 'fa-eye-slash' : 'fa-eye'"
                              ></i>
                            </button>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">Calendar Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.calendarType">{{
                              leadDetailData.calendarType
                            }}</span>
                            <span *ngIf="!leadDetailData.calendarType">-</span>
                          </div>
                          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0">
                            <label class="datalbl">Parent Customer:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.parentCustomerName">{{
                              leadDetailData.parentCustomerName
                            }}</span>
                            <span *ngIf="!leadDetailData.parentCustomerName">-</span>
                          </div>
                          <div
                            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15 dataGroup m-0"
                            *ngIf="this.customerGroupForm.controls.invoiceType.enabled"
                          >
                            <label class="datalbl">Invoice Type:&nbsp;&nbsp;&nbsp;</label>
                            <span *ngIf="leadDetailData.invoiceType">{{
                              leadDetailData.invoiceType
                            }}</span>
                            <span *ngIf="!leadDetailData.invoiceType">-</span>
                          </div>
                        </div>
                      </p-card>
                    </div>
                  </div>
                </p-tabPanel>
              </p-tabView>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" *ngIf="openLeadStatusScreen">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to View Lead List"
            (click)="viewLead()"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">Lead Status</h3>
        </div>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#leadLeadStatusScreen"
            aria-expanded="false"
            aria-controls="leadLeadStatusScreen"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-body">
        <div class="panel-body">
          <div class="row" id="leadLeadStatusScreen">
            <div class="col-lg-12 col-md-12">
              <div id="custStatus" class="panel-collapse collapse in">
                <div class="panel-body table-responsive">
                  <div class="progressbarWrap" *ngIf="teamHierarchyList?.length > 0">
                    <div
                      class="circleWrap"
                      [ngClass]="{
                        complete: data.status == 'Approved',
                        progressdv: data.status == 'Pending'
                      }"
                      *ngFor="let data of teamHierarchyList; last as isLast"
                    >
                      <div class="circledv completedWorkFlowClass">
                        <i class="fa fa-check" *ngIf="data.status == 'Approved'"></i>
                      </div>
                      <p>{{ data.teamName }}</p>
                      <div class="lines" *ngIf="!isLast">
                        <div class="line"></div>
                      </div>
                    </div>
                  </div>
                  <div class="progressbarWrap" *ngIf="teamHierarchyList?.length == 0">
                    No record found for status.
                  </div>
                </div>
              </div>
              <div style="margin: 20px">
                <h3>Workflow Audit</h3>
                <div class="table-responsive">
                  <div class="row">
                    <div class="col-lg-12 col-md-12">
                      <table class="table">
                        <thead>
                          <tr>
                            <th>Customer Name</th>
                            <th>Action</th>
                            <th>Staff name</th>
                            <th>Remark</th>
                            <th>Action Date</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="
                              let data of workflowAuditDataI
                                | paginate
                                  : {
                                      id: 'searchMasterPageData',
                                      itemsPerPage: MasteritemsPerPageI,
                                      currentPage: currentPageMasterSlabI,
                                      totalItems: MastertotalRecordsI
                                    };
                              index as i
                            "
                          >
                            <td>
                              <div *ngIf="data.entityName">
                                {{ data.entityName }}
                              </div>
                              <div *ngIf="data.planName">
                                {{ data.planName }}
                              </div>
                            </td>
                            <td>
                              <div>
                                {{ data.action }}
                              </div>
                            </td>
                            <td>
                              <div>
                                {{ data.actionByName }}
                              </div>
                            </td>
                            <td>
                              <div>
                                {{ data.remark }}
                              </div>
                            </td>
                            <td>
                              <div>
                                {{ data.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <br />
                      <div class="pagination_Dropdown">
                        <pagination-controls
                          id="searchMasterPageData"
                          maxSize="10"
                          directionLinks="true"
                          previousLabel=""
                          nextLabel=""
                          (pageChange)="pageChangedMasterListI($event)"
                        ></pagination-controls>
                        <div id="itemPerPageDropdown">
                          <p-dropdown
                            [options]="pageLimitOptions"
                            optionLabel="value"
                            optionValue="value"
                            (onChange)="TotalItemPerPageWorkFlow($event)"
                          ></p-dropdown>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Fetching lead notes screen Start -->
<div class="row" *ngIf="openLeadNotesScreen">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Lead Notes</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <table class="table" *ngIf="leadNotesList && leadNotesList?.length > 0">
              <thead>
                <tr>
                  <th>Id</th>
                  <th>Created By</th>
                  <th>Created Date and time</th>
                  <th>Created Staff Team</th>
                  <th>Notes</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let leadNotes of leadNotesList
                      | paginate
                        : {
                            id: 'leadNotesListpageData',
                            itemsPerPage: leadNotesListItemsPerPage,
                            currentPage: leadNotesListDataCurrentPage,
                            totalItems: leadNotesDataTotalRecords
                          };
                    index as i
                  "
                >
                  <td>{{ leadNotes?.id }}</td>
                  <td>{{ leadNotes?.createdByName }}</td>
                  <td>{{ leadNotes?.createdOn | date: "dd-MM-yyyy hh:mm a" }}</td>
                  <td>
                    <a
                      (click)="openStaffDetailModal(leadNotes.createdBy)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                    >
                      {{ leadNotes.createdByName }}
                    </a>
                  </td>
                  <td>{{ leadNotes?.notes }}</td>
                </tr>
              </tbody>
            </table>
            <div style="display: flex" *ngIf="leadNotesList && leadNotesList?.length > 0">
              <pagination-controls
                id="leadNotesListpageData"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedLeadNotesList($event, myLead?.id)"
              >
              </pagination-controls>
              <div id="itemPerPageDropdown">
                <p-dropdown
                  [options]="pageLimitOptions"
                  optionLabel="value"
                  optionValue="value"
                  (onChange)="TotalLeadNotesItemPerPage($event, myLead?.id)"
                ></p-dropdown>
              </div>
            </div>
            <div
              class="col-lg-12 col-md-12"
              style="text-align: center"
              *ngIf="!leadNotesList || leadNotesList?.length === 0"
            >
              No Records Found
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Fetching lead notes screen end -->

<!-- Fetching Service Management start -->
<div *ngIf="isServiceManagementOpen">
  <app-add-service
    [custData]="leadDetailData"
    [isLeadMaster]="true"
    name="Circuit"
    (backButton)="backLeadDeatils($event)"
  >
  </app-add-service>
</div>
<!-- Fetching Service Management end -->

<!-- Fetching Quatation Management start -->
<div *ngIf="isQuotationDetailOpen">
  <app-common-quotation-management
    [LeadCustData]="leadDetailData"
    (backLeadData)="backLeadDeatils($event)"
  ></app-common-quotation-management>
</div>
<!-- Fetching Quatation Management end -->

<!--Select Plan group popup END-->

<!--Select Plan group popup ENSelect StaffReject RemarksD-->

<!-- existingCustomerModal -->

<p-dialog
  header="Select Customer"
  [(visible)]="selectParentCustomer"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalCloseParentCustomer()"
>
  <!-- <div class="modal fade" id="selectParentCustomer" role="dialog">
    <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Select Customer</h3>
        </div> -->
  <div class="modal-body">
    <h5>Search Parent Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selParentSearchOption($event)"
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption != 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchParentCustomer()"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchParentCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="reset"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Parent Customer</h5>
    <p-table
      #dt
      [value]="prepaidParentCustomerList"
      responsiveLayout="scroll"
      [(selection)]="selectedParentCust"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
        <tr>
          <td>
            <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
          </td>
          <td>{{ prepaidParentCustomerList.name }}</td>
          <td>{{ prepaidParentCustomerList.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          [rows]="parentCustomerListdataitemsPerPage"
          [first]="newFirst"
          [totalRecords]="parentCustomerListdatatotalRecords"
          (onPageChange)="paginate($event)"
        >
        </p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        style="object-fit: cover; padding: 5px 8px"
        class="btn btn-primary"
        (click)="saveSelCustomer()"
        [disabled]="this.selectedParentCust.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button type="button" class="btn btn-danger btn-sm" (click)="modalCloseParentCustomer()">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Add Notes"
  [(visible)]="addNotesPopup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeNotesModal()"
>
  <!-- <div class="modal fade" id="addNotesPopup" role="dialog">
    <div class="modal-dialog" style="width: 50%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Add Notes</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="addNotesForm">
      <div>
        <label class="datalbl">Notes: </label>
        <textarea
          type="text"
          class="form-control"
          id="notes"
          placeholder="Enter Notes..."
          formControlName="notes"
          [ngClass]="{
            'is-invalid': notesSubmitted && addNotesForm.controls.notes.errors
          }"
        ></textarea>

        <div
          class="errorWrap text-danger"
          *ngIf="notesSubmitted && addNotesForm.controls.notes.errors"
        >
          <div
            class="error text-danger"
            *ngIf="notesSubmitted && addNotesForm.controls.notes.errors.required"
          >
            Notes is required.
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="submit"
        id="submit"
        (click)="saveNotes(this.leadIdForNotes)"
        class="btn btn-success btn-sm"
      >
        Save
      </button>
      <button
        type="button"
        class="btn btn-danger btn-sm"
        (click)="closeNotesModal()"
        data-dismiss="modal"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Schedule a followup"
  [(visible)]="scheduleFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeFolloupPopup()"
>
  <!-- <div class="modal fade" id="scheduleFollowup" role="dialog" *ngIf="followupPopupOpen">
    <div class="modal-dialog nearSearchModalLocation"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Schedule a followup</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="followupScheduleForm">
      <label style="font-weight: bold"> Follow Up Name * </label>
      <input
        disabled
        type="text"
        class="form-control"
        placeholder="Enter the follow up name"
        formControlName="followUpName"
        [ngClass]="{
          'is-invalid': followupFormsubmitted && followupScheduleForm.controls.followUpName.errors
        }"
      />
      <div
        class="error text-danger"
        *ngIf="followupFormsubmitted && followupScheduleForm.controls.followUpName.errors"
      >
        Name is required.
      </div>
      <br />
      <label style="font-weight: bold">Follow Up Date & Time *</label>
      <p-calendar
        formControlName="followUpDatetime"
        [showTime]="true"
        [showSeconds]="true"
        inputId="time"
        [numberOfMonths]="3"
        [minDate]="dateTime"
        [ngClass]="{
          'is-invalid':
            followupFormsubmitted && this.followupScheduleForm.controls.followUpDatetime.errors
        }"
      >
      </p-calendar>
      <div
        class="error text-danger"
        *ngIf="followupFormsubmitted && followupScheduleForm.controls.followUpDatetime.errors"
      >
        Date & Time is required.
      </div>
      <br /><br />
      <label style="font-weight: bold">Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarks"
        [ngClass]="{
          'is-invalid': followupFormsubmitted && this.followupScheduleForm.controls.remarks.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="followupFormsubmitted && this.followupScheduleForm.controls.remarks.errors"
      >
        Remarks is required.
      </div>
      <!-- <br />
                    <label style="font-weight: bold">Status*</label>
                    <p-dropdown [options]="this.status" formControlName="status" optionLabel="label" optionValue="value"
                        filter="true" filterBy="label" placeholder="Select follow up status">
                    </p-dropdown>
                    <div class="errorWrap text-danger" *ngIf="followupFormsubmitted && followupScheduleForm.controls.status.errors">
                        <div class="error text-danger" *ngIf="followupFormsubmitted && followupScheduleForm.controls.status.errors.required">Status is required.
                        </div>
                   </div> -->
      <br />
      <div class="addUpdateBtn">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveFollowup()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          Schedule
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="closeFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Close Followup"
  [(visible)]="closeFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeActionFolloupPopup()"
>
  <!-- <div class="modal fade" id="closeFollowup" role="dialog">
    <div class="modal-dialog nearSearchModalLocation"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Close Followup</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="closeFollowupForm">
      <label style="font-weight: bold">Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarks"
        [ngClass]="{
          'is-invalid': closeFollowupFormsubmitted && this.closeFollowupForm.controls.remarks.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="closeFollowupFormsubmitted && this.closeFollowupForm.controls.remarks.errors"
      >
        Remarks is required.
      </div>
      <br />
      <div class="addUpdateBtn">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveCloseFollowUp()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeActionFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="ReSchedule a followup"
  [(visible)]="reScheduleFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeReFolloupPopup()"
>
  <!-- <div class="modal fade" id="reScheduleFollowup" role="dialog">
    <div class="modal-dialog nearSearchModalLocation"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">ReSchedule a followup</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="reFollowupScheduleForm">
      <label style="font-weight: bold">Current Follow Up Close Remarks *</label>
      <p-dropdown
        [options]="rescheduleRemarks"
        placeholder="Select the specific remark"
        formControlName="remarks"
      >
      </p-dropdown>
      <br />
      <label style="font-weight: bold"> ReSchedule Follow Up Name * </label>
      <input
        disabled
        type="text"
        class="form-control"
        placeholder="Enter the follow up name"
        formControlName="followUpName"
        [ngClass]="{
          'is-invalid':
            reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpName.errors
        }"
      />
      <div
        class="error text-danger"
        *ngIf="reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpName.errors"
      >
        ReSchedule ReSchedule Name is required.
      </div>
      <br />
      <label style="font-weight: bold">ReSchedule Follow Up Date & Time *</label>
      <p-calendar
        formControlName="followUpDatetime"
        [showTime]="true"
        [showSeconds]="true"
        inputId="time"
        [numberOfMonths]="3"
        [minDate]="dateTime"
        [ngClass]="{
          'is-invalid':
            reFollowupFormsubmitted && this.reFollowupScheduleForm.controls.followUpDatetime.errors
        }"
      >
      </p-calendar>
      <div
        class="error text-danger"
        *ngIf="reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpDatetime.errors"
      >
        ReSchedule Date & Time is required.
      </div>
      <br /><br />
      <label style="font-weight: bold">ReSchedule Remarks</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarksTemp"
      >
      </textarea>
      <!--          <div-->
      <!--            class="error text-danger"-->
      <!--            *ngIf="-->
      <!--              reFollowupFormsubmitted && this.reFollowupScheduleForm.controls.remarksTemp.errors-->
      <!--            "-->
      <!--          >-->
      <!--            Remarks is required.-->
      <!--          </div>-->

      <br />
      <div class="addUpdateBtn">
        <br />
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveReFollowup()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          ReSchedule
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="closeReFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="{{ this.leadApproveRejectDto.flag }} Remarks"
  [(visible)]="approveOrRejectLeadModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeApproveOrRejectLeadPopup()"
>
  <div class="modal-body">
    <form [formGroup]="leadApproveRejectForm">
      <div class="row">
        <div
          *ngIf="approved && leadApproveRejectDto.approveRequest"
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
        >
          <div class="card">
            <app-search-details
              [placeholder]="'Search Staff'"
              [searchValue]="searchStaffDeatil"
              (search)="searchStaffByNameReject($event)"
              (clear)="clearSearchFormReject()"
            >
            </app-search-details>
            <div *ngIf="approveLeadList && approveLeadList.length > 0">
              <h5>Select Staff</h5>
              <p-table
                [value]="approveLeadList"
                [(selection)]="selectStaff"
                responsiveLayout="scroll"
                [paginator]="true"
                [rows]="approveStaffListdataitemsPerPageForStaff"
                [rowsPerPageOptions]="[5, 10, 15, 20]"
                [first]="newStaffFirst"
                (onPage)="paginateStaff($event)"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
            <div *ngIf="approveLeadList && approveLeadList.length === 0">No records found.</div>
          </div>
          <br />
        </div>
        <div
          *ngIf="approved && !leadApproveRejectDto.approveRequest"
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
        >
          <div class="card">
            <app-search-details
              [placeholder]="'Search Staff'"
              [searchValue]="searchStaffDeatil"
              (search)="searchStaffByNameReject($event)"
              (clear)="clearSearchFormReject()"
            >
            </app-search-details>
            <div *ngIf="approveLeadList && approveLeadList.length > 0">
              <h5>Select Staff</h5>
              <p-table
                [value]="approveLeadList"
                [(selection)]="selectStaffReject"
                responsiveLayout="scroll"
                [paginator]="true"
                [rows]="approveStaffListdataitemsPerPageForStaff"
                [rowsPerPageOptions]="[5, 10, 15, 20]"
                [first]="newStaffFirst"
                (onPage)="paginateStaff($event)"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
                <!-- <ng-template pTemplate="summary">
                                            <p-paginator (onPageChange)="paginateStaff($event)" [first]="newStaffFirst"
                                                [rows]="approveStaffListdataitemsPerPageForStaff"
                                                [totalRecords]="approvestaffListdatatotalRecords"
                                                [rowsPerPageOptions]="[5, 10, 25, 50]"></p-paginator>
                                        </ng-template> -->
              </p-table>
            </div>
            <div *ngIf="approveLeadList && approveLeadList.length === 0">No records found.</div>
          </div>
          <br />
        </div>
        <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label style="font-weight: bold">Remarks *</label>
          <textarea
            type="text"
            class="form-control"
            placeholder="Enter the remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid':
                leadApproveRejectFormsubmitted && this.leadApproveRejectForm.controls.remark.errors
            }"
          >
          </textarea>
          <div
            class="error text-danger"
            *ngIf="
              leadApproveRejectFormsubmitted && this.leadApproveRejectForm.controls.remark.errors
            "
          >
            Remarks is required.
          </div>
          <br />
        </div>
        <div
          *ngIf="leadApproveRejectDto.flag == 'Reject' && !approved"
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
        >
          <div class="form-group">
            <label>Rejected Reason List *</label>
            <p-dropdown
              [options]="this.rejectedReasons"
              formControlName="rejectedReasonMasterId"
              optionLabel="name"
              optionValue="id"
              filter="true"
              filterBy="name"
              placeholder="Select Rejected Reason "
              appendTo="body"
            >
            </p-dropdown>
            <div
              class="error text-danger"
              *ngIf="
                leadApproveRejectFormsubmitted &&
                this.leadApproveRejectForm.controls.rejectedReasonMasterId.errors
              "
            >
              Rejected Reason is required.
            </div>
            <!-- <div
                class="error text-danger"
                *ngIf="
                  leadApproveRejectFormsubmitted &&
                  this.leadApproveRejectForm.controls.rejectedReasonMasterId.errors
                "
              >
              Rejected Reason is required.
              </div> -->
          </div>
        </div>
        <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="addUpdateBtn">
            <button
              type="submit"
              class="btn btn-primary"
              id="submit"
              (click)="approveOrRejectLead(leadObj)"
              [disabled]="this.showQtyError"
            >
              <i class="fa fa-check-circle"></i>
              {{ labelFlag }}
            </button>
            <br />
          </div>
        </div>

        <!--            <div>-->
        <!--              <div class="addUpdateBtn">-->
        <!--                <button-->
        <!--                  type="submit"-->
        <!--                  class="btn btn-primary"-->
        <!--                  id="submit"-->
        <!--                  (click)="assignToStaff(labelFlag)"-->
        <!--                >-->
        <!--                  <i class="fa fa-check-circle"></i>-->
        <!--                  Assign-->
        <!--                </button>-->
        <!--                <br />-->
        <!--              </div>-->
        <!--            </div>-->
        <!-- <div
              *ngIf="approved && !leadApproveRejectDto.approveRequest && !selectStaffReject"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            >
              <div class="addUpdateBtn">
                <button
                  disabled
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  (click)="assignToStaff(labelFlag)"
                >
                  <i class="fa fa-check-circle"></i>
                  Assign
                </button>
                <br />
              </div>
            </div> -->
        <!-- <div
              *ngIf="approved && !leadApproveRejectDto.approveRequest && selectStaffReject"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            >
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  (click)="assignToStaff(labelFlag)"
                >
                  <i class="fa fa-check-circle"></i>
                  Assign
                </button>
                <br />
              </div>
            </div> -->
        <!-- <div
              *ngIf="approved && leadApproveRejectDto.approveRequest && selectStaff"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            >
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  (click)="assignToStaff(labelFlag)"
                >
                  <i class="fa fa-check-circle"></i>
                  Assign
                </button>
                <br />
              </div>
            </div> -->
        <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="addUpdateBtn">
            <button
              type="submit"
              class="btn btn-primary"
              id="submit"
              (click)="assignToStaff(labelFlag)"
            >
              <i class="fa fa-check-circle"></i>
              Assign
            </button>
            <br />
          </div>
        </div>
        <!-- <div
              *ngIf="approved && !leadApproveRejectDto.approveRequest && selectStaffReject"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            >
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  (click)="assignToStaff(labelflag)"
                >
                  <i class="fa fa-check-circle"></i>
                  Assign
                </button>
                <br />
              </div>
            </div> -->
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeApproveOrRejectLeadPopup()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Remarks Followup"
  [(visible)]="remarkScheduleFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeRemarkPopup()"
>
  <!-- <div class="modal fade" id="remarkScheduleFollowup" role="dialog">
    <div class="modal-dialog nearSearchModalLocation">
 
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Remarks Followup</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="remarkFollowupForm">
      <label style="font-weight: bold">Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the remark"
        formControlName="remark"
        [ngClass]="{
          'is-invalid':
            remarkFollowupFormsubmitted && this.remarkFollowupForm.controls.remark.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="remarkFollowupFormsubmitted && this.remarkFollowupForm.controls.remark.errors"
      >
        Remarks is required.
      </div>
      <br />
      <div class="addUpdateBtn">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveRemarkFollowUp()"
          [disabled]="this.staffid != this.leadMasterObj?.nextApproveStaffId"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body">
    <h3 class="panel-title">Remarks List</h3>
    <hr />
    <div [id]="tableWrapperRemarks">
      <div [id]="scrollIdRemarks">
        <table class="table">
          <thead>
            <tr>
              <th>Remarks</th>
              <th>Created On</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let remarkDetails of followUpRemarkList">
              <td>{{ remarkDetails?.remark }}</td>
              <td>{{ remarkDetails?.createdOn | date: "dd/MM/yyyy HH:mm:ss" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeRemarkPopup()">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Close a lead"
  [(visible)]="openRejectLeadPopup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeRejectLeadPopup()"
>
  <!-- <div class="modal fade" id="openRejectLeadPopup" role="dialog">
    <div class="modal-dialog nearSearchModalLocation"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Close a lead</h3>
        </div> -->
  <div class="modal-body">
    <form [formGroup]="rejectLeadFormGroup">
      <div>
        <label style="font-weight: bold"> Rejected Reason * </label>
        <p-dropdown
          [options]="rejectedReasonList"
          optionLabel="name"
          optionValue="id"
          placeholder="Select Rejected reason for the lead."
          formControlName="rejectReasonId"
          (ngModelChange)="selectRejectedReason(rejectLeadFormGroup.controls.rejectReasonId.value)"
          [ngClass]="{
            'is-invalid':
              rejectedLeadFormSubmitted && rejectLeadFormGroup.controls.rejectReasonId.errors
          }"
        >
        </p-dropdown>
        <div
          class="errorWrap text-danger"
          *ngIf="rejectedLeadFormSubmitted && rejectLeadFormGroup.controls.rejectReasonId.errors"
        >
          <div
            class="error text-danger"
            *ngIf="
              rejectedLeadFormSubmitted &&
              rejectLeadFormGroup.controls.rejectReasonId.errors.required
            "
          >
            Rejection reason is required.
          </div>
        </div>
      </div>
      <br />
      <div *ngIf="rejectedSubReasonArr?.length !== 0">
        <label style="font-weight: bold">Rejected Sub Reason</label>
        <p-dropdown
          formControlName="rejectSubReasonId"
          [options]="rejectedSubReasonArr"
          optionValue="id"
          optionLabel="name"
          placeholder="Select Rejected sub reason for the lead."
        >
        </p-dropdown>
      </div>
      <br />
      <div>
        <label style="font-weight: bold">Remarks *</label>
        <textarea
          type="text"
          class="form-control"
          placeholder="Enter the Remarks"
          formControlName="remark"
          [ngClass]="{
            'is-invalid':
              rejectedLeadFormSubmitted && this.rejectLeadFormGroup.controls.remark.errors.required
          }"
        >
        </textarea>
        <div
          class="errorWrap text-danger"
          *ngIf="rejectedLeadFormSubmitted && this.rejectLeadFormGroup.controls.remark.errors"
        >
          <div
            class="error text-danger"
            *ngIf="
              rejectedLeadFormSubmitted && this.rejectLeadFormGroup.controls.remark.errors.required
            "
          >
            Remark is required.
          </div>
        </div>
      </div>
      <br />
      <div class="addUpdateBtn">
        <button
          class="btn btn-primary"
          id="submit"
          [disabled]="this.showQtyError"
          (click)="rejectLead(leadId)"
        >
          <i class="fa fa-check-circle"></i>
          Apply
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="closeRejectLeadPopup()"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Search Location"
  [(visible)]="ifsearchLocationModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="clearsearchLocationData()"
>
  <div class="modal-body">
    <form name="searchLocationForm" [formGroup]="searchLocationForm">
      <div class="form-group">
        <label for="searchLocationname">Search Location Name:</label>
        <div class="row">
          <div class="col-lg-7 col-md-6">
            <input
              type="searchLocationname"
              class="form-control"
              id="searchLocationname"
              placeholder="Enter Location Name"
              formControlName="searchLocationname"
            />
          </div>
          <div class="col-lg-5 col-md-6" style="padding: 0 10px !important">
            <button
              type="submit"
              class="btn btn-primary btn-sm"
              id="closeModal"
              (click)="searchLocation()"
              [disabled]="!searchLocationForm.valid"
            >
              <i class="fa fa-search"></i>
              Search
            </button>
            <button
              id="btn"
              type="button"
              class="btn btn-default btn-sm"
              (click)="clearLocationForm()"
              style="margin-left: 8px"
            >
              <i class="fa fa-refresh"></i>
              Clear
            </button>
          </div>
        </div>
      </div>
    </form>
    <div class="row">
      <div class="col-lg-12 col-md-12" style="margin-top: 3rem">
        <table class="table">
          <thead>
            <tr>
              <th style="width: 35%">Name</th>
              <th style="width: 65%">Address</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let data of searchLocationData
                  | paginate
                    : {
                        id: 'searchpageData',
                        itemsPerPage: searchLocationItemPerPage,
                        currentPage: currentPagesearchLocationList,
                        totalItems: searchLocationtotalRecords
                      };
                index as i
              "
            >
              <td
                class="HoverEffect"
                (click)="filedLocation(data.placeId)"
                data-toggle="tooltip"
                data-placement="bottom"
                title="Set value Latitude & Longitude"
                style="width: 35%"
              >
                {{ data.name }}
              </td>
              <td style="width: 65%">{{ data.address }}</td>
            </tr>
          </tbody>
        </table>
        <pagination-controls
          id="searchpageData"
          maxSize="10"
          directionLinks="true"
          previousLabel=""
          nextLabel=""
          (pageChange)="pageChangedSearchLocationList($event)"
        ></pagination-controls>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="button"
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="clearsearchLocationData()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Select Plan"
  [(visible)]="selectPlanGroup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalClosePlanChangeSubisu()"
>
  <!-- <div class="modal fade" id="selectPlanGroup" role="dialog">
    <div class="modal-dialog" style="width: 60%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Select Plan</h3>
        </div> -->
  <div class="modal-body">
    <div class="row">
      <div class="col-md-4 col-lg-4">
        <h5 style="margin-top: 15px">Select Plan Group:</h5>
        <p-dropdown
          [options]="filterNormalPlanGroup"
          optionValue="planGroupId"
          optionLabel="planGroupName"
          filter="true"
          filterBy="planGroupName"
          placeholder="Select a Plan Group"
          [(ngModel)]="planGroupSelectedSubisu"
          (onChange)="getPlanListByGroupIdSubisu()"
        ></p-dropdown>
      </div>
      <!-- <div class="col-md-4 col-lg-4">
            <h5 style="margin-top: 15px;">Invoice To Org:</h5>
            <p-dropdown
              [options]="isInvoiceData"
              placeholder="Select Invoice to org or not"
              optionValue="value"
              optionLabel="label"
              [(ngModel)]="isInvoiceToOrg"
              (onChange)="valueChange($event)"
            ></p-dropdown>
          </div> -->
    </div>

    <br />
    <h5 style="margin-top: 15px">Select Plan List</h5>
    <table class="table" style="margin-top: 10px; border: 1px solid #ddd">
      <thead>
        <tr>
          <th style="text-align: center">Name</th>
          <th style="text-align: center">Charge Name</th>
          <th style="text-align: center">Offer Price</th>
          <th style="text-align: center">New Offer Price</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of plansArray?.controls; let index = index">
          <td style="padding-left: 8px">
            <input
              class="form-control"
              placeholder="Enter planName"
              [formControl]="row.get('name')"
              readonly
            />
          </td>
          <td>
            <input
              class="form-control"
              placeholder="Enter Charge Name"
              [formControl]="row.get('chargeName')"
              readonly
            />
          </td>
          <td>
            <input
              class="form-control"
              placeholder="Enter offerPrice"
              [formControl]="row.get('offerPrice')"
              readonly
            />
          </td>
          <td>
            <input
              type="number"
              class="form-control"
              placeholder="Enter New Amount"
              [formControl]="row.get('newAmount')"
              randomly
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        style="object-fit: cover; padding: 5px 8px"
        class="btn btn-primary"
        (click)="modalClosePlanChangeSubisu()"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button type="button" class="btn btn-danger btn-sm" (click)="modalClosePlanChangeSubisu()">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Assign Lead"
  [(visible)]="assignLeadModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeAssignLeadModal()"
>
  <!-- <div class="modal-body">
    <form [formGroup]="assignLeadStaffForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Staff*</label>
          <div>
            <p-multiSelect
              [ngClass]="{
                'is-invalid': assignSubmmitted && assignLeadStaffForm.controls.staffId.errors
              }"
              [options]="assignableStaffList"
              formControlName="staffId"
              optionLabel="fullName"
              optionValue="id"
              filter="true"
              filterBy="fullName"
              placeholder="Please select staff."
            ></p-multiSelect>
          </div>
          <div
            *ngIf="assignSubmmitted && assignLeadStaffForm.controls.staffId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="assignSubmmitted && assignLeadStaffForm.controls.staffId.errors.required"
              class="error text-danger"
            >
              Staff is required.
            </div>
          </div>
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': assignSubmmitted && assignLeadStaffForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="assignSubmmitted && assignLeadStaffForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="assignSubmmitted && assignLeadStaffForm.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="assignLeadStaffSubmit()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Assign Staff
    </button>
    <button
      class="btn btn-default"
      (click)="closeAssignLeadModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div> -->
  <div class="modal-body">
    <form [formGroup]="assignLeadStaffForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <app-search-details
              [placeholder]="'Search Staff'"
              [searchValue]="searchReassignStaffDeatil"
              (search)="searchReassignStaffByName($event)"
              (clear)="clearReassignSearchForm()"
            >
            </app-search-details>
            <div *ngIf="reassignableStaffList && reassignableStaffList.length > 0">
              <h5>Select Staff</h5>
              <p-table
                [value]="reassignableStaffList"
                [(selection)]="selectStaff"
                responsiveLayout="scroll"
                [paginator]="true"
                [rows]="approveStaffListdataitemsPerPageForStaff"
                [rowsPerPageOptions]="[5, 10, 15, 20]"
                [first]="newStaffFirst"
                (onPage)="paginateStaff($event)"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
            <div *ngIf="reassignableStaffList && reassignableStaffList.length === 0">
              No records found.
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark *</label>
          <textarea
            class="form-control"
            name="remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': assignSubmmitted && assignLeadStaffForm.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="assignSubmmitted && assignLeadStaffForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="assignSubmmitted && assignLeadStaffForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="assignLeadStaffSubmit()"
      [disabled]="!selectStaff"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>

    <button
      type="button"
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeAssignLeadModal()"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Plan Details"
  [(visible)]="PlanDetailsShowModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="planDetailsPopUpClose()"
>
  <!-- <div aria-labelledby="myModalLabel" class="modal fade" id="PlanDetailsShow" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button aria-label="Close" class="close" data-dismiss="modal" type="button">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="PlanDetailsShow" style="color: #fff !important">
            Plan Details
          </h4>
        </div> -->
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <table class="table">
          <thead>
            <tr>
              <th>Plan</th>
              <th>Invoice Type</th>
              <th>Validity</th>
              <th>Offer Price</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let plandata of planDataShow">
              <td>{{ plandata.name }}</td>
              <td>{{ plandata.invoiceType !== null ? plandata.invoiceType : "-" }}</td>
              <td>{{ plandata?.validity }} {{ plandata?.unitsOfValidity }}</td>

              <td>{{ plandata.offerprice }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="planDetailsPopUpClose()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Existing Customer"
  [(visible)]="selectextingCustomerModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalCloseextingCustomer()"
>
  <!-- <div class="modal fade" id="selectextingCustomer" role="dialog">
    <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Existing Customer</h3>
        </div> -->
  <div class="modal-body">
    <h5>Search Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          [(ngModel)]="searchextingCustType"
          [options]="leadcustTypeList"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Customer Type"
          (onChange)="searchextingCustomerTypeChange($event)"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selextingSearchOption($event)"
          [(ngModel)]="searchextingCustOption"
          [options]="searchExtingcustomerOption"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          extingFieldEnable &&
          searchextingCustOption != 'status' &&
          searchextingCustOption !== 'serviceareaName' &&
          searchextingCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchextingCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchextingCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchextingCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchextingCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="extingFieldEnable" class="col-lg-3 col-md-3 col-sm-12">
        <button
          (click)="searchextingCustomer(1)"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
          [disabled]="!searchextingCustValue && !searchextingCustType"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchextingCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="reset"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Customer</h5>
    <p-table
      #dt
      [(selection)]="selectedextingCust"
      [value]="extingCustomerList"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
          <th>Mobile No.</th>
          <th>Service Area</th>
        </tr>
      </ng-template>
      <ng-template let-extingCustomerList let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="extingCustomerList"></p-tableRadioButton>
          </td>
          <td>
            {{ extingCustomerList.name }}
            {{ extingCustomerList.lastname }}
          </td>
          <td>{{ extingCustomerList.username }}</td>
          <td>{{ extingCustomerList.mobile }}</td>
          <td>{{ extingCustomerList.serviceArea }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="extingPaginate($event)"
          [first]="newFirstexting"
          [rows]="extingCustomerListdataitemsPerPage"
          [totalRecords]="extingCustomerListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="SelExtingCustomer('')"
        [disabled]="this.selectedextingCust.length == 0"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseextingCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Staff Details"
  [(visible)]="staffDetailModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModalStaff()"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Basic Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Name :</label>
            <span>{{ staffData.fullName }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Email :</label>
            <span>{{ staffData.email }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Phone :</label>
            <span>{{ staffData.phone }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Username :</label>
            <span>{{ staffData.username }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Service Area :</label>
            <a
              data-target="#serviceAreaDetail"
              (click)="onClickServiceArea()"
              href="javascript:void(0)"
              style="color: blue"
              >click here</a
            >
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Parent Staff Name :</label>
            <span>{{ staffData.parentstaffname }}</span>
          </div>
        </div>
        <!-- <div class="row">
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Team name List :</label>
              </div>
            </div> -->
      </div>
      <fieldset class="boxWhite">
        <legend>Team List :</legend>
        <div class="row">
          <div
            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
            *ngFor="let data of staffData.teamNameList"
          >
            <span>{{ data }}</span>
          </div>
        </div>
      </fieldset>
    </fieldset>
  </div>
  <div class="modal-footer">
    <button class="btn btn-default" (click)="closeModalStaff()" data-dismiss="modal" type="button">
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Service Area"
  [(visible)]="serviceAreaDetailModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModalOfArea()"
>
  <!-- <div
    aria-labelledby="myModalLabel"
    class="modal fade"
    id="serviceAreaDetail"
    role="dialog"
    tabindex="-1"
  > -->
  <!-- <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button aria-label="Close" class="close" data-dismiss="modal" type="button">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Service Area</h4>
        </div> -->
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <table>
          <tr *ngFor="let data of serviceAreaList">
            {{
              data
            }}
          </tr>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-default" data-dismiss="modal" type="button" (click)="closeModalOfArea()">
      Close
    </button>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>
<app-customer-view-details
  *ngIf="dialog"
  (closeCustomerViewDetails)="closeSelectStaff()"
  [custId]="custId"
  [sourceType]="'lead'"
></app-customer-view-details>

<p-dialog
  header="Reject reason Details"
  [(visible)]="rejectReasonDetailModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeRejectReasonModel()"
>
  <div class="modal-body">
    <div class="row" *ngIf="workflowAuditDataI">
      <fieldset
        style="margin-top: 0rem; margin-bottom: 2rem"
        *ngIf="workflowAuditDataI.length > 0; else noData"
      >
        <legend>Rejected Reason Details</legend>
        <div class="boxWhite">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
              <label class="datalbl">Rejected Reason :</label>
              <span>{{ rejectedReasonValue }}</span>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
              <label class="datalbl">Rejected By :</label>
              <span>{{ workflowAuditDataI[0].actionByName }}</span>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
              <!-- <label class="datalbl">Remark Reason:</label> -->
              <span>{{ workflowAuditDataI[0].remark }}</span>
            </div>
          </div>
        </div>
      </fieldset>
      <!-- <div
        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
        *ngIf="workflowAuditDataI.length > 0; else noData"
      >
       <label>Remark:</label>
        <span>
            {{ workflowAuditDataI[0].remark }}
        </span>
      </div> -->

      <ng-template #noData>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">No remark available.</div>
      </ng-template>
    </div>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeRejectReasonModel()"
    >
      Close
    </button>
  </div>
</p-dialog>
