<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.custname }} Child Customer List
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="inventoryListPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#inventoryListPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
        <button
          *ngIf="removeAccess"
          (click)="removeFromParent()"
          [disabled]="this.checkedList.length == 0"
          class="right"
          class="btn btn-primary statusbtn"
          data-backdrop="static"
          data-keyboard="false"
          data-target="#removeParent"
          data-toggle="modal"
          style="
            background-color: #f7b206 !important;
            font-size: 16px;
            padding: 3px 12px;
            margin-top: 10px;
          "
          title="Remove parent"
          type="submit"
        >
          Remove
        </button>
        <button
          *ngIf="makeParentAccess"
          (click)="makeParent(custData.id)"
          [disabled]="this.checkedList.length == 0"
          class="right"
          class="btn btn-primary statusbtn"
          data-backdrop="static"
          data-keyboard="false"
          data-target="#removeParent"
          data-toggle="modal"
          style="
            background-color: #f7b206 !important;
            font-size: 16px;
            padding: 3px 12px;
            margin-top: 10px;
          "
          title="Remove parent"
          type="submit"
        >
          Make Parent
        </button>
      </div>

      <div class="panel-collapse collapse in" id="inventoryListPreCust">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th width="2%">
                      <input
                        (change)="checkUncheckAll()"
                        [(ngModel)]="this.masterSelected"
                        name="master-checkbox"
                        type="checkbox"
                      />
                    </th>
                    <th width="16%">Name</th>
                    <th width="10%">Username</th>
                    <th width="12%">Service Area</th>
                    <th width="10%">Mobile Number</th>
                    <th width="18%">Account No</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of childCustomerDataList
                        | paginate
                          : {
                              id: 'childCustomerDataList',
                              itemsPerPage: pageSizeForChildsPage,
                              currentPage: pageNumberForChildsPage,
                              totalItems: childCustomerDataTotalRecords
                            };
                      index as i
                    "
                  >
                    <td width="2%">
                      <input
                        (change)="isAllSelected()"
                        [(ngModel)]="data.isSelected"
                        name="master-checkbox"
                        type="checkbox"
                      />
                    </td>
                    <td>
                      <a
                        [routerLink]="['/home/<USER>/details/' + custType + '/x/' + data.id]"
                        href="javascript:void(0)"
                        style="color: #f7b206"
                      >
                        {{ data.firstname }} {{ data.lastname }}
                      </a>
                    </td>
                    <td>{{ data.username }}</td>
                    <td>{{ data.serviceareaName }}</td>
                    <td>{{ data.mobile }}</td>
                    <td>{{ data.acctno }}</td>
                  </tr>
                </tbody>
              </table>
              <div style="display: flex">
                <pagination-controls
                  (pageChange)="pageChangeEventForChildCustomers($event)"
                  [directionLinks]="true"
                  [maxSize]="10"
                  id="childCustomerDataList"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="itemPerPageChangeEvent($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
