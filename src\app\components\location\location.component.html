<p-confirmDialog [style]="{ width: '40vw' }" [baseZIndex]="10000"></p-confirmDialog>
<div class="childComponent" pFocusTrap>
  <ngx-spinner [fullScreen]="false" type=" ball-clip-rotate-multiple " size=" medium ">
    <p class="loading">Loading...</p>
  </ngx-spinner>
  <div class="row">
    <div class="col-md-12">
      <!-- User Data -->
      <div class="panel top">
        <div class="panel-heading">
          <h3 class="panel-title">Location Master Management</h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#locationSearch"
              aria-expanded="false"
              aria-controls="locationSearch"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div id="locationSearch" class="panel-collapse collapse in">
          <div class="panel-body">
            <div class="searchForm">
              <form class="form-auth-small" [formGroup]="searchByNameForm">
                <div class="row">
                  <div class="col-md-3" style="padding-right: 0%; text-align: right">
                    <label style="padding: 5px">Location Master Name:</label>
                  </div>
                  <div class="col-md-5" style="padding-left: 0%">
                    <input
                      type=" text "
                      name=" name "
                      class="form-control"
                      placeholder=" Enter Location Master Name "
                      formControlName="name"
                      [ngClass]="{
                        'is-invalid': searchSubmitted && searchByNameForm.controls.name.errors
                      }"
                    />
                  </div>
                  <div class="col-md-4" style="padding-left: 0%">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      title="Search Location"
                      data-toggle="tooltip"
                      (click)="searchByName()"
                    >
                      <i class="fa fa-search"></i> Search
                    </button>
                    &nbsp;
                    <button
                      type="reset"
                      class="btn btn-default"
                      title="Clear"
                      data-toggle="tooltip"
                      (click)="clearSearchForm()"
                    >
                      <i class="fa fa-refresh"></i> Clear
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <!-- <div class="panel-body no-padding panel-udata">
            <div class="col-md-3 pcol">
              <div class="dbox">
                <a href="javascript:void(0)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Create</h5>
                  <p>Create Location Master</p>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcol">
              <div class="dbox">
                <a href="javascript:void(0)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Search</h5>
                  <p>Search Location Master</p>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcol">
              <div class="dbox">
                <a href="javascript:void(0)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>User Section</h5>
                  <p>Details Comes Here</p>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcol noborder">
              <div class="dbox">
                <a href="javascript:void(0)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>User Section</h5>
                  <p>Details Comes Here</p>
                </a>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <!-- END User Data -->
    </div>
  </div>
  <div class="row">
    <div class="col-md-6 left">
      <!-- Data Table -->
      <div class="panel">
        <div class="panel-heading">
          <h3 class="panel-title">Location Master</h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#locationTablePanel"
              aria-expanded="false"
              aria-controls="locationTablePanel"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div id="locationTablePanel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 15%">Location ID</th>
                  <th style="width: 30%">Name</th>
                  <th>Identify Value</th>
                  <th style="width: 10%" *ngIf="this.accessData.locationMaster.createUpdateAccess">
                    Status
                  </th>
                  <th
                    style="width: 15%"
                    *ngIf="
                      this.accessData.locationMaster.createUpdateAccess ||
                      this.accessData.locationMaster.deleteAccess ||
                      editAccess
                    "
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let location of locationData
                      | paginate
                        : {
                            id: 'listing_locationdata',
                            itemsPerPage: itemsPerPage,
                            currentPage: currentPage,
                            totalItems: totalRecords
                          };
                    index as i
                  "
                >
                  <td style="width: 15%">{{ location.locationMasterId }}</td>
                  <td style="width: 30%" class="detailOnAnchorClick">
                    <a
                      (click)="getLocationDetail(location.locationMasterId)"
                      data-title="Click here to see Location  detail"
                      data-toggle="modal"
                      data-target="#myModal"
                      >{{ location.name }}</a
                    >
                  </td>
                  <td>{{ location.locationIdentifyValue }}</td>
                  <td style="width: 10%" *ngIf="this.accessData.locationMaster.createUpdateAccess">
                    <label class="switch">
                      <input
                        [checked]="location.status == 'Active' ? true : false"
                        type="checkbox"
                        (change)="changeStatus(location.name, location.status, location.mvnoId)"
                      />
                      <span class="slider round"></span>
                    </label>
                  </td>
                  <td
                    style="width: 15%"
                    *ngIf="
                      this.accessData.locationMaster.createUpdateAccess ||
                      this.accessData.locationMaster.deleteAccess ||
                      editAccess
                    "
                    class="btnAction"
                  >
                    <a
                      *ngIf="this.accessData.locationMaster.createUpdateAccess && editAccess"
                      data-title="Edit"
                      data-toggle="tooltip"
                      (click)="editById(location.locationMasterId, i, location.mvnoId)"
                      ><img src="assets/img/ioc01.jpg"
                    /></a>
                    <!-- <a
                      *ngIf="this.accessData.locationMaster.deleteAccess"
                      data-title="Delete"
                      data-toggle="tooltip"
                      (click)="
                        deleteConfirm(
                          location.locationMasterId,
                          location.mvnoId,
                          i
                        )
                      "
                      ><img src="assets/img/ioc02.jpg" />
                    </a> -->
                  </td>
                </tr>
              </tbody>
            </table>
            <br />
            <div class="row">
              <div class="col-md-12" style="display: flex">
                <pagination-controls
                  id="listing_locationdata"
                  [maxSize]="10"
                  [directionLinks]="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChanged($event)"
                >
                </pagination-controls>

                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>

                <!-- <select (change)="paginationTotalPage($event)">
                                        <option *ngFor="let option of pageLimitOptions" [value]="option.value"
                                            [selected]="option.value == currentPage">
                                            {{ option.value }}
                                        </option>
                                    </select> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- END Data Table -->
    </div>
    <div class="col-md-6 right" *ngIf="this.accessData.locationMaster.createUpdateAccess">
      <!-- Form Design -->
      <div class="panel">
        <div class="panel-heading">
          <h3 class="panel-title">{{ editMode ? "Update" : "Create" }} Location Master</h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#locationFormPanel"
              aria-expanded="false"
              aria-controls="locationFormPanel"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div id="locationFormPanel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !editMode">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (editMode && editAccess)">
            <form class="form-auth-small" [formGroup]="createForm">
              <div *ngIf="userId == superAdminId">
                <label>Mvno Name *</label>
                <div>
                  <p-dropdown
                    #dd
                    [options]="mvnoData"
                    placeholder="Select mvno"
                    optionLabel="name"
                    optionValue="id"
                    formControlName="mvnoName"
                    [readonly]="editMode"
                    [ngClass]="{
                      'is-invalid': submitted && createForm.controls.mvnoName.errors
                    }"
                  ></p-dropdown>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="submitted && createForm.controls.mvnoName.errors"
                  >
                    <div
                      class="error text-danger"
                      *ngIf="submitted && createForm.controls.mvnoName.errors.required"
                    >
                      Please select MVNO name.
                    </div>
                  </div>
                </div>
                <br />
              </div>

              <label>Location Master Name *</label>
              <input
                type="text"
                name="name"
                [readonly]="editMode"
                class="form-control"
                placeholder="Enter Location Master Name"
                formControlName="name"
                [ngClass]="{
                  'is-invalid': submitted && createForm.controls.name.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && createForm.controls.name.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && createForm.controls.name.errors.required"
                >
                  Please enter Location Master name.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="submitted && createForm.controls.name.errors?.cannotContainSpace"
                >
                  <p class="error">White space are not allowed.</p>
                </div>
              </div>

              <br /><label>Check Item</label>
              <input
                type=" text "
                name=" checkItem "
                class="form-control"
                placeholder=" Enter Check Item "
                formControlName="checkItem"
              />
              <!-- <div
                class="errorWrap text-danger"
                *ngIf="submitted && createForm.controls.checkItem.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && createForm.controls.checkItem.errors.required"
                >
                  Please enter Check Item.
                </div>
              </div> -->
              <br />
              <label>Location Identify Attribute</label>
              <input
                type="text"
                name="locationIdentifyAttribute"
                class="form-control"
                placeholder="Enter Location Identify Attribute"
                formControlName="locationIdentifyAttribute"
              />
              <br />
              <label>Location Identify Value</label>
              <input
                type="text"
                name="locationIdentifyValue"
                class="form-control"
                placeholder="Enter Location Identify Value"
                formControlName="locationIdentifyValue"
              />
              <br />
              <div>
                <label style="display: block">Status *</label>
                <p-dropdown
                  [options]="status"
                  placeholder="Select Location Master Status"
                  optionLabel="label"
                  optionValue="label"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && createForm.controls.status.errors
                  }"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && createForm.controls.status.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="submitted && createForm.controls.status.errors.required"
                  >
                    Please select status.
                  </div>
                </div>
              </div>
              <br />
              <div>
                <label style="display: block">Location Mac</label>
                <p-dropdown
                  id="parentcust"
                  [disabled]="true"
                  [showClear]="true"
                  filter="true"
                  filterBy="name"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Location Mac"
                  styleClass="disableDropdown"
                ></p-dropdown>
                <button
                  id="opencust"
                  type="button"
                  (click)="locationMacModelOpen()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  type="button"
                  (click)="locationMacDetailsModelOpen()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
                >
                  <i _ngcontent-nrc-c2="" class="fa fa-eye"></i>
                </button>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  title="Submit Location Details"
                  (click)="addConcurrent()"
                >
                  <i class="fa fa-check-circle"></i>
                  {{ editMode ? "Update Location Master " : "Add Location Master " }}
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
      <p-confirmDialog [style]="{ width: '50vw' }" key="positionDialog" [baseZIndex]="10000">
      </p-confirmDialog>
      <!-- END Form Design -->
    </div>
  </div>
</div>
<div class="row" *ngIf="showDialogue">
  <div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog" style="width: 35%">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <!-- <h4 class="modal-title">{{plan.planName}} Detail</h4> -->
          <h3 class="panel-title">Location Details</h3>
        </div>
        <div class="modal-body">
          <div class="container-fluid">
            <div class="row" id="viewDetail">
              <div class="col-md-5">
                <label for="name">Name : </label>
              </div>
              <div class="col-md-7">
                <label for="nameValue">{{ this.showLocationDetails.name }}</label>
              </div>
            </div>
            <div class="row" id="viewDetail">
              <div class="col-md-5">
                <label for="type">Status : </label>
              </div>
              <div class="col-md-7">
                <label for="typeValue">{{ this.showLocationDetails.status }}</label>
              </div>
            </div>
            <div class="row" id="viewDetail">
              <div class="col-md-5">
                <label for="quota">Check Item : </label>
              </div>
              <div class="col-md-7">
                <label for="quotaValue">{{ this.showLocationDetails.checkItem }}</label>
              </div>
            </div>
            <div class="row" id="viewDetail">
              <div class="col-md-5">
                <label for="quotaReserInterval">Location Identify Attribute: </label>
              </div>
              <div class="col-md-7">
                <label for="quotaReserIntervalValue">{{
                  this.showLocationDetails.locationIdentifyAttribute
                }}</label>
              </div>
            </div>
            <div class="row" id="viewDetail">
              <div class="col-md-5">
                <label for="quotaReserInterval">Location Identify Value: </label>
              </div>
              <div class="col-md-7">
                <label for="quotaReserIntervalValue">{{
                  this.showLocationDetails.locationIdentifyValue
                }}</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Location Mac Model -->
<p-dialog
  header="Location Mac"
  [(visible)]="showLocationMac"
  [style]="{ width: '75vw' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="locationMacModelClose()"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0.5rem">
      <legend>Location Mac</legend>
      <div class="boxWhite">
        <table [formGroup]="locationMacForm" style="width: 100%">
          <td style="text-align: center; padding: 0 10px 0 0">
            <input
              class="form-control"
              formControlName="identity"
              id="identity        "
              name="identity"
              placeholder="Enter Identity"
              type="text"
            />
          </td>
          <td style="text-align: center; padding: 0 10px 0 0">
            <input
              [ngClass]="{
                'is-invalid': locationMacsubmitted && locationMacForm.controls.mac.errors
              }"
              class="form-control"
              formControlName="mac"
              id="mac        "
              name="mac"
              (keyup)="keypdown($event)"
              placeholder="Enter Mac"
              type="text"
            />
          </td>
          <td style="text-align: center; width: 9%">
            <button
              (click)="addLocationMacListField()"
              class="btn btn-primary form-control"
              style="object-fit: cover; float: right"
            >
              <i aria-hidden="true" class="fa fa-plus-square"></i>
              Add
            </button>
          </td>

          <tr>
            <td>
              <!-- <div
                *ngIf="locationMacsubmitted && locationMacForm.controls.identity.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Identity is required.</div>
              </div> -->
            </td>
            <td>
              <div
                *ngIf="locationMacsubmitted && locationMacForm.controls.mac.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Mac is required.</div>
              </div>
              <div
                *ngIf="locationMacsubmitted && isMacExist && !locationMacForm.controls.mac.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Mac already exist.</div>
              </div>
            </td>
            <td style="text-align: center; width: 9%"></td>
          </tr>
        </table>

        <table class="table coa-table" style="margin-top: 3rem">
          <thead>
            <tr>
              <th style="text-align: center">Identity</th>
              <th style="text-align: center">Mac</th>
              <th style="text-align: center">Delete</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let row of overLocationMacArray.controls
                  | paginate
                    : {
                        id: 'overChargeListFromArrayData',
                        itemsPerPage: overChargeListItemPerPage,
                        currentPage: currentPageoverChargeList,
                        totalItems: overChargeListtotalRecords
                      };
                let index = index
              "
            >
              <td>
                <input
                  [formControl]="row.get('identity')"
                  class="form-control"
                  id="identity"
                  name="identity"
                  placeholder="Enter identity"
                  readonly
                  type="text"
                />
              </td>
              <td>
                <input
                  disabled
                  class="form-control"
                  [formControl]="row.get('mac')"
                  id="mac"
                  name="mac"
                  placeholder="Enter Mac"
                  type="text"
                />
              </td>
              <td style="text-align: center">
                <span>
                  <button
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                    (click)="deleteLocationMapField(index)"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span>
              </td>
            </tr>
          </tbody>
        </table>

        <br />
      </div>
    </fieldset>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn mr-2">
      <button
        type="button"
        class="btn btn-primary btn-sm"
        (click)="saveLocationMacData()"
        [disabled]="overLocationMacArray.controls.length == 0"
      >
        Save
      </button>
    </div>
    <div class="addUpdateBtn" style="padding-left: 8px">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="locationMacModelClose()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Location Map Details Model -->
<p-dialog
  header="Location Mac Details"
  [(visible)]="showChargeDetails"
  [style]="{ width: '75vw' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="locationMacDetailsModelClose()"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0.5rem">
      <legend>Add Location Mac</legend>
      <div class="boxWhite">
        <table class="table coa-table" style="margin-top: 3rem">
          <thead>
            <tr>
              <th style="text-align: center">Identity</th>
              <th style="text-align: center">Mac</th>
              <th style="text-align: center">Delete</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let row of locationMacData
                  | paginate
                    : {
                        id: 'overChargeListFromArrayData',
                        itemsPerPage: overChargeListItemPerPage,
                        currentPage: currentPageoverChargeList,
                        totalItems: overChargeListtotalRecords
                      };
                let index = index
              "
            >
              <td style="text-align: center">{{ row.identity }}</td>
              <td style="text-align: center">
                {{ row.mac }}
              </td>
              <td style="text-align: center">
                <span>
                  <button
                    (click)="deleteLocationMapField(index)"
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- <div class="row">
          <div class="col-md-12">
            <pagination-controls
              (pageChange)="pageChangedOverChargeList($event)"
              directionLinks="true"
              id="overChargeListFromArrayData"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            >
            </pagination-controls>
          </div>
        </div> -->
        <br />
      </div>
    </fieldset>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="locationMacDetailsModelClose()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
