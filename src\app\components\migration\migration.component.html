<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Migration Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchProduct"
            aria-expanded="false"
            aria-controls="searchProduct"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchProduct" class="panel-collapse collapse in">
        <div class="panel-body no-padding panel-udata">
          <div
            class="pcol col-md-6"
            [ngClass]="{
              activeSubMenu: createScreen
            }"
          >
            <div class="dbox">
              <a class="detailOnAnchorClick" (click)="openCreateTab()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Data</h5>
              </a>
            </div>
          </div>
          <div
            class="pcol col-md-6"
            [ngClass]="{
              activeSubMenu: updateScreen
            }"
          >
            <div class="dbox">
              <a class="detailOnAnchorClick" (click)="openUpdateTab()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Update Data</h5>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Data -->
<div class="row" *ngIf="createScreen">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Create Data</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createProduct"
            aria-expanded="false"
            aria-controls="createProduct"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createProduct" class="panel-collapse collapse in">
        <div class="panel-body">
          <div>
            <form [formGroup]="createMigration">
              <div class="boxWhite">
                <div class="row">
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Migration Type</label>
                      <p-dropdown
                        [options]="migrationTypeList"
                        placeholder="Select Migration Type"
                        optionLabel="label"
                        optionValue="value"
                        formControlName="migrationType"
                        filter="true"
                        filterBy="label"
                        (onChange)="migrationTypeChange($event)"
                        [ngClass]="{
                          'is-invalid': submitted && createMigration.controls.migrationType.errors
                        }"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && createMigration.controls.migrationType.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && createMigration.controls.migrationType.errors.required
                          "
                        >
                          Migration Type is required
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Select File</label>
                      <input
                        (change)="onFileChangeUpload($event)"
                        class="form-control"
                        formControlName="file"
                        id="txtSelectDocument"
                        multiple="multiple"
                        placeholder="Select Attachment"
                        type="file"
                        accept=".XLSX,XLS"
                      />
                      <div
                        *ngIf="submitted && createMigration.controls.file.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && createMigration.controls.file.errors.required"
                          class="error text-danger"
                        >
                          file is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="addUpdateBtn">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      data-title="Submit Details"
                      (click)="uploadDocument()"
                      style="background: #f7b206 !important; margin-top: -1%"
                      [disabled]="!formSubmit"
                    >
                      <i class="fa fa-check-circle"></i>
                      Migrate data
                    </button>

                    <button
                      type="submit"
                      class="btn btn-primary"
                      data-title="Submit Details"
                      (click)="downloadClick()"
                      style="background: #f7b206 !important; margin-left: 10px; margin-top: -1%"
                      [disabled]="!createMigration.value.migrationType"
                    >
                      <i class="fa fa-check-circle"></i>
                      Download Sample File
                    </button>
                    <br />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Update Data -->
<div class="row" *ngIf="updateScreen">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Update Data</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createProduct"
            aria-expanded="false"
            aria-controls="createProduct"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createProduct" class="panel-collapse collapse in">
        <div class="panel-body">
          <div>
            <form [formGroup]="updateMigration">
              <div class="boxWhite">
                <div class="row">
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Migration Type</label>
                      <p-dropdown
                        [options]="updateTypeList"
                        placeholder="Select Migration Type"
                        optionLabel="label"
                        optionValue="value"
                        formControlName="migrationType"
                        filter="true"
                        filterBy="label"
                        (onChange)="updateMigrationTypeChange($event)"
                        [ngClass]="{
                          'is-invalid': submitted && updateMigration.controls.migrationType.errors
                        }"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && updateMigration.controls.migrationType.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && updateMigration.controls.migrationType.errors.required
                          "
                        >
                          Migration Type is required
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Select File</label>
                      <input
                        (change)="onUpdateFileChangeUpload($event)"
                        class="form-control"
                        formControlName="file"
                        id="txtSelectDocument"
                        multiple="multiple"
                        placeholder="Select Attachment"
                        type="file"
                        accept=".XLSX,XLS"
                      />
                      <div
                        *ngIf="submitted && updateMigration.controls.file.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && updateMigration.controls.file.errors.required"
                          class="error text-danger"
                        >
                          file is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="addUpdateBtn">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      data-title="Submit Details"
                      (click)="uploadDocumentForUpdate()"
                      style="background: #f7b206 !important; margin-top: -1%"
                      [disabled]="!formSubmit"
                    >
                      <i class="fa fa-check-circle"></i>
                      Migrate data
                    </button>

                    <button
                      type="submit"
                      class="btn btn-primary"
                      data-title="Submit Details"
                      (click)="downloadClickForUpdate()"
                      style="background: #f7b206 !important; margin-left: 10px; margin-top: -1%"
                      [disabled]="!updateMigration.value.migrationType"
                    >
                      <i class="fa fa-check-circle"></i>
                      Download Sample File
                    </button>
                    <br />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Sample File"
  [(visible)]="isFIleNameDialog"
  [modal]="true"
  [style]="{ width: '40vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
  (onHide)="closeFileNameDialog()"
>
  <div class="modal-body-junk">
    <div class="form-group">
      <div class="row">
        <!-- Email -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>File name</label>
          <div>
            <input
              class="form-control"
              [(ngModel)]="fileName"
              placeholder="Enter sample file name"
              type="text"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="downloadSampleFile()"
        class="btn btn-primary"
        id="submit"
        type="submit"
        [disabled]="!fileName"
      >
        <i class="fa fa-check-circle"></i>
        Download
      </button>
    </div>
  </div>
</p-dialog>
