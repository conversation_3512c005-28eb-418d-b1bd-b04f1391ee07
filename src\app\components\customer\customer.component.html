<div class="row" *ngIf="isShowMenu">
  <div class="col-md-12">
    <div class="panel mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }} Management</h3>
        <div class="right">
          <button
            aria-controls="searchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="searchPreCust">
        <div class="panel-body no-padding panel-udata">
          <div [ngClass]="'col-md-6'" class="pcol" *ngIf="createAccess">
            <div>
              <a
                [routerLink]="['/home/<USER>/create/' + custType]"
                class="curson_pointer"
                href="javascript:void(0)"
                class="dbox"
              >
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create {{ title }}</h5>
              </a>
              <div
                *ngIf="isShowCreateView"
                [ngClass]="{
                  activeSubMenu: true
                }"
              ></div>
            </div>
          </div>
          <div [ngClass]="'col-md-6'" class="pcol">
            <div>
              <a
                [routerLink]="['/home/<USER>/list/' + custType]"
                class="curson_pointer"
                href="javascript:void(0)"
                class="dbox"
              >
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search {{ title }}</h5>
              </a>
              <div
                [ngClass]="{
                  activeSubMenu: true
                }"
                *ngIf="isShowListView"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<router-outlet> </router-outlet>
