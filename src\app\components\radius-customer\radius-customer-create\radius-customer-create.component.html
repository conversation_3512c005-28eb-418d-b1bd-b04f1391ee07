<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 *ngIf="!iscustomerEdit" class="panel-title">Create Radius Customer</h3>
        <h3 *ngIf="iscustomerEdit" class="panel-title">
          Update Customer {{ custData.title }} {{ custData.custname }}
        </h3>
        <div class="right">
          <button
            id="create"
            aria-controls="createPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#createPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="createPreCust">
        <div class="panel-body">
          <form [formGroup]="customerGroupForm">
            <!-- Basic Details -->
            <fieldset style="margin-top: 1.5rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Title *</label>
                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.title.errors
                      }"
                      id="title"
                      [options]="selectTitile"
                      filter="true"
                      filterBy="label"
                      formControlName="title"
                      optionLabel="label"
                      optionValue="label"
                      placeholder="Select a Type"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.title.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Title is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>First Name *</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.firstname.errors
                      }"
                      class="form-control"
                      formControlName="firstname"
                      id="firstname"
                      placeholder="Enter First Name"
                      type="text"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.firstname.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">First Name is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Last Name *</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.lastname.errors
                      }"
                      class="form-control"
                      formControlName="lastname"
                      id="lastname"
                      placeholder="Enter Last Name"
                      type="text"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.lastname.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Last Name is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Username *</label>
                    <input
                      [readonly]="iscustomerEdit"
                      (keydown.Tab)="onKey($event)"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.username.errors
                      }"
                      class="form-control"
                      formControlName="username"
                      id="username"
                      placeholder="Enter Username"
                      type="text"
                      autocomplete="off"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.username.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Username is required.</div>
                    </div>
                    <div
                    *ngIf="customerGroupForm.controls.username.errors['noSpace']"
                        class="error text-danger"
                      >
                     Username cannot contain spaces.
                    </div>
                  </div>
                  <div *ngIf="!iscustomerEdit" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Password *</label>
                    <div class="form-control displayflex">
                      <div style="width: 95%">
                        <input
                          [ngClass]="{
                            'is-invalid': submitted && customerGroupForm.controls.password.errors
                          }"
                          [readonly]="iscustomerEdit"
                          [type]="_passwordType"
                          class="inputPassword"
                          formControlName="password"
                          id="password"
                          placeholder="Enter Password"
                          autocomplete="new-password"
                        />
                      </div>
                      <div style="width: 5%">
                        <div *ngIf="showPassword">
                          <i
                            (click)="showPassword = false; _passwordType = 'password'"
                            class="fa fa-eye"
                          ></i>
                        </div>
                        <div *ngIf="!showPassword">
                          <i
                            (click)="showPassword = true; _passwordType = 'text'"
                            class="fa fa-eye-slash"
                          ></i>
                        </div>
                      </div>
                    </div>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.password.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Password is required.</div>
                    </div>
                    <div
                        *ngIf="customerGroupForm.controls.password.errors['noSpace']"
                        class="error text-danger"
                      >
                        Password cannot contain spaces.
                      </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Mobile Number *</label>
                    <div style="display: flex">
                      <div style="width: 30%">
                        <p-dropdown
                          [filter]="true"
                          [options]="countries"
                          formControlName="countryCode"
                          id="countryCode"
                          optionLabel="dial_code"
                          optionValue="dial_code"
                          placeholder="+91"
                        ></p-dropdown>
                      </div>
                      <div style="width: 70%">
                        <input
                          (input)="onInputMobile($event)"
                          [ngClass]="{
                            'is-invalid': submitted && customerGroupForm.controls.mobile.errors
                          }"
                          class="form-control"
                          formControlName="mobile"
                          id="mobile"
                          [attr.maxLength]="commondropdownService.maxMobileLength"
                          placeholder="Enter Mobile"
                          type="text"
                        />

                        <!-- Validation Errors -->
                        <div
                          *ngIf="submitted && customerGroupForm.controls.mobile.errors"
                          class="errorWrap text-danger"
                        >
                          <div *ngIf="customerGroupForm.controls.mobile.errors.required">
                            Mobile Number is required.
                          </div>

                          <div
                            *ngIf="
                              customerGroupForm.controls.mobile.errors.minlength ||
                              customerGroupForm.controls.mobile.errors.maxlength
                            "
                          >
                            <ng-container
                              *ngIf="
                                commondropdownService.minMobileLength ===
                                  commondropdownService.maxMobileLength;
                                else mobileLengthRange
                              "
                            >
                              Mobile Number must be exactly
                              {{ commondropdownService.minMobileLength }} digits.
                            </ng-container>
                            <ng-template #mobileLengthRange>
                              Mobile Number must be between
                              {{ commondropdownService.minMobileLength }} and
                              {{ commondropdownService.maxMobileLength }} digits.
                            </ng-template>
                          </div>
                        </div>

                        <!-- Starts with 0 Error -->
                        <div *ngIf="mobileError" class="errorWrap text-danger">
                          <div class="error text-danger">
                            Mobile number should not start with 0.
                          </div>
                        </div>
                      </div>
                    </div>

                    <div style="display: flex">
                      <div style="width: 30%"></div>
                      <div style="width: 70%">
                        <div
                          *ngIf="submitted && customerGroupForm.controls.mobile.errors"
                          class="errorWrap text-danger"
                        >
                          <div *ngIf="customerGroupForm.controls.mobile.errors.required">
                            Mobile Number minimum 10 character is required.
                          </div>
                        </div>
                        <div
                          *ngIf="inputMobile && customerGroupForm.value.mobile"
                          class="error text-danger"
                        >
                          {{ this.inputMobile }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Email *</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.email.errors
                      }"
                      class="form-control"
                      formControlName="email"
                      id="email"
                      placeholder="Enter Email"
                      type="text"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.email.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && this.customerGroupForm.controls.email.errors.required"
                        class="error text-danger"
                      >
                        Email is required.
                      </div>
                      <div
                        *ngIf="submitted && this.customerGroupForm.controls.email.errors.email"
                        class="error text-danger"
                      >
                        Email is not valid.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Acct No</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.acct_no.errors
                      }"
                      class="form-control"
                      formControlName="acct_no"
                      id="acct_no"
                      placeholder="Enter Acct No"
                      type="text"
                    />
                    <div
                      *ngIf="submitted && customerGroupForm.controls.acct_no.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && this.customerGroupForm.controls.acct_no.errors.required"
                        class="error text-danger"
                      >
                        Acct No is required.
                      </div>
                      <div
                        *ngIf="submitted && this.customerGroupForm.controls.acct_no.errors.email"
                        class="error text-danger"
                      >
                        Acct No is not valid.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <label>Expiry Date</label>
                    <input
                      id="expiryDate"
                      type="date"
                      formControlName="expiryDate"
                      placeholder="DD/MM/YYYY"
                      class="form-control"
                    />
                    <div></div>
                    <br />
                  </div>
                  <div *ngIf="!iscustomerEdit" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Status*</label>
                    <p-dropdown
                      [disabled]="iscustomerEdit"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.status.errors
                      }"
                      id="Status"
                      [options]="commondropdownService.CustomerStatusValue"
                      filter="true"
                      filterBy="text"
                      formControlName="status"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Status"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && customerGroupForm.controls.status.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && customerGroupForm.controls.status.errors.required"
                        class="error text-danger"
                      >
                        Status is required.
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!iscustomerEdit" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Customer Type*</label>
                    <p-dropdown
                      id="custtype"
                      [disabled]="iscustomerEdit"
                      [options]="customerTypeValue"
                      filter="true"
                      filterBy="label"
                      formControlName="custtype"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Customer Type"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.custtype.errors"
                    >
                      <div class="error text-danger">Cust Type Is required</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <label for="branchId">Partner *</label>
                    <p-dropdown
                      id="partner"
                      [options]="partnerListByServiceArea"
                      optionValue="id"
                      optionLabel="name"
                      filter="true"
                      filterBy="name"
                      placeholder="Select a Partner"
                      formControlName="partner"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.partner.errors
                      }"
                    >
                    </p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.partner.errors"
                    >
                      <div class="error text-danger">Partner Is required</div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
            <!-- Radius Service Details -->
            <fieldset>
              <legend>Network Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>GateWay IP</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="gatewayIpBind"
                      id="gatewayIpBind"
                      placeholder="Enter GateWay IP"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Skipnet Conf</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="skipnetconf"
                      id="skipnetconf"
                      placeholder="Enter Skipnet Conf"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Rdim Port</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="rdimport"
                      id="rdimport"
                      placeholder="Enter Rdim Port (Validate)"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Bngrouter Interface</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="bngrouterinterface"
                      id="bngrouterinterface"
                      placeholder="Enter Bngrouter Interface"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>QOS</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="qos"
                      id="qos"
                      placeholder="Enter QOS"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Vlan</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="vlanid"
                      id="vlanid"
                      placeholder="Enter vlanid"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>WanIp</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="wanip"
                      id="wanip"
                      placeholder="Enter WanIp"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>LanIp</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="lanip"
                      id="lanip"
                      placeholder="Enter LanIp"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>ASN Number</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="asnnumber"
                      id="lanip"
                      placeholder="Enter LanIp"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>LL account Id</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="llaccountid"
                      id="llaccountid"
                      placeholder="Enter LL account Id"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>IP Prefixes</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="ipprefixes"
                      id="ipprefixes"
                      placeholder="Enter IP Prefixes"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Rdex Port</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="rdexport"
                      id="rdexport"
                      placeholder="Enter Rdex Port"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>RD Value</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="rdvalue"
                      id="rdvalue"
                      placeholder="Enter RD Value"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>VRF Name</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="vrfname"
                      id="vrfname"
                      placeholder="Enter VRF Name"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Peer IP</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="peerip"
                      id="peerip"
                      placeholder="Enter Peer IP"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>vsiid</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="vsiid"
                      id="vsiid"
                      placeholder="Enter vsiid"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>vsiname</label>
                    <input
                      [readonly]="iscustomerEdit"
                      class="form-control"
                      formControlName="vsiname"
                      id="vsiname"
                      placeholder="Enter vsiname"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Framed IP Netmask</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.framedIPNetmask.errors
                      }"
                      class="form-control"
                      formControlName="framedIPNetmask"
                      id="framedIPNetmask"
                      placeholder="Framed IP Netmask"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Framed IPv6 Prefix</label>
                    <input
                      [ngClass]="{
                        'is-invalid':
                          submitted && customerGroupForm.controls.framedIPv6Prefix.errors
                      }"
                      class="form-control"
                      formControlName="framedIPv6Prefix"
                      id="framedIPv6Prefix"
                      placeholder="Framed IPv6 Prefix"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Primary DNS</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.primaryDNS.errors
                      }"
                      class="form-control"
                      formControlName="primaryDNS"
                      id="primaryDNS"
                      placeholder="Primary DNS"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Primary IPv6 DNS</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.primaryIPv6DNS.errors
                      }"
                      class="form-control"
                      formControlName="primaryIPv6DNS"
                      id="primaryIPv6DNS"
                      placeholder="Primary IPv6 DNS"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary IPv6 DNS</label>
                    <input
                      [ngClass]="{
                        'is-invalid':
                          submitted && customerGroupForm.controls.secondaryIPv6DNS.errors
                      }"
                      class="form-control"
                      formControlName="secondaryIPv6DNS"
                      id="secondaryIPv6DNS"
                      placeholder="Secondary IPv6 DNS"
                      type="text"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Secondary DNS</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.secondaryDNS.errors
                      }"
                      class="form-control"
                      formControlName="secondaryDNS"
                      id="secondaryDNS"
                      placeholder="Secondary DNS"
                      type="text"
                    />
                  </div>
                </div>
              </div>
            </fieldset>
            <!--    Param Details    -->
            <fieldset>
              <legend>Param Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 col-xs-12">
                    <label>Param 1*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Param 1"
                      formControlName="thparam1"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.thparam1.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.thparam1.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && customerGroupForm.controls.thparam1.errors.required"
                      >
                        Param 1 is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-xs-12">
                    <label>Param 2*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Param 2"
                      formControlName="thparam2"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.thparam2.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.thparam2.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && customerGroupForm.controls.thparam2.errors.required"
                      >
                        Param 2 is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-xs-12">
                    <label>Param 3*</label>
                    <input
                      id="thparam3"
                      type="text"
                      class="form-control"
                      placeholder="Enter Param 3"
                      formControlName="thparam3"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.thparam3.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.thparam3.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && customerGroupForm.controls.thparam3.errors.required"
                      >
                        Param 3 is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-xs-12">
                    <label>Param 4*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Base Param 1"
                      formControlName="thparam4"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.thparam4.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.thparam4.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && customerGroupForm.controls.thparam4.errors.required"
                      >
                        Param 4 is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-xs-12">
                    <label>Remarks*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Remarks"
                      formControlName="remarks"
                      [ngClass]="{
                        'is-invalid': submitted && customerGroupForm.controls.remarks.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && customerGroupForm.controls.remarks.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && customerGroupForm.controls.remarks.errors.required"
                      >
                        Remarks is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
              </div>
            </fieldset>
            <div class="addUpdateBtn" style="margin-top: 3.5rem">
              <button
                (click)="checkUsernme('')"
                *ngIf="!iscustomerEdit"
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Add Customer
              </button>
              <button
                (click)="addEditcustomer(editCustId)"
                *ngIf="iscustomerEdit"
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Update Customer
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
