<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Audit Log Management</h3>
        <div class="right">
          <button
            aria-controls="searchTicketM"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchTicketM"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchTicketM">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-2 col-md-2 m-b-10">
              <label style="margin-left: 10%; margin-top: 4px">Select Module</label>
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <p-dropdown
                [(ngModel)]="selectedModule"
                [filter]="true"
                [options]="moduleList"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Module"
                (onChange)="moduleChange($event)"
              ></p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <h3 class="panel-title">{{ selectedModuleName }} Audits</h3>
        </div>

        <div class="right">
          <button class="btn refreshbtn" type="reset" (click)="clearSearch()">
            <i class="fa fa-refresh"></i>
          </button>
          <button
            aria-controls="TicketMList"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#TicketMList"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchTicketM">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <label>Profile Name</label>
              <input
                type="text"
                class="form-control"
                placeholder="Enter Profile Name"
                [(ngModel)]="searchKey"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <label>Start Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Start Date"
                [(ngModel)]="startDate"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <label>End Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Pay To Date"
                [(ngModel)]="endDate"
                name="enddate"
                [disabled]="!startDate"
                [min]="startDate"
                inputmode="none"
                (keydown)="preventManualInput($event)"
              />
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchKey || (startDate && endDate)">
              <div style="padding-top: 26px">
                <button (click)="search()" class="btn btn-primary" id="searchbtn" type="button">
                  <i class="fa fa-search"></i>
                  Search
                </button>
                <button (click)="clearSearch()" class="btn btn-default" id="searchbtn" type="reset">
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="TicketMList">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Profile Name</th>
                        <th>Entity Type</th>
                        <th>Module</th>
                        <th>Action Type</th>
                        <th>Username</th>
                        <th>User Team</th>
                        <th>Audit Date</th>
                        <th>Ip Address</th>
                        <th>Snapshot</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let data of AuditlogListData
                            | paginate
                              : {
                                  id: 'AuditlogListpageData',
                                  itemsPerPage: AuditlogListdataitemsPerPage,
                                  currentPage: currentPageAuditlogListdata,
                                  totalItems: AuditlogListdatatotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ data.entityName == "" ? "-" : data.entityName }}</td>
                        <td>{{ data.entityType == "" ? "-" : data.entityType }}</td>
                        <td>{{ data.moduleName }}</td>
                        <td>{{ data.actionType }}</td>
                        <td>{{ data.authorUserName }}</td>
                        <td>{{ data.authorUserTeams == "" ? "-" : data.authorUserTeams }}</td>
                        <td>{{ data.updatedOn | date: "dd-MM-YYYY HH:mm:ss" }}</td>
                        <td>{{ data.ipAddress != "" ? data.ipAddress : "NA" }}</td>
                        <td>
                          <span
                            data-target="#showDialogue"
                            class="HoverEffect"
                            (click)="snapShotOpen(data.snapshot)"
                          >
                            Click here
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <br />

                  <div class="pagination_Dropdown">
                    <pagination-controls
                      id="AuditlogListpageData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedAuditlogList((currentPageAuditlogListdata = $event))"
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        [options]="pageLimitOptions"
                        [(ngModel)]="AuditlogListdataitemsPerPage"
                        optionLabel="value"
                        optionValue="value"
                        (onChange)="TotalItemPerPage($event, 'CMS')"
                      ></p-dropdown>
                    </div>
                  </div>
                  <div class="flex align-items-center justify-content-between">
                    In total there are {{ AuditlogListdatatotalRecords }} Records
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="card flex justify-content-center"> -->
<p-dialog
  header=" Snapshot Details"
  [(visible)]="showDialogue"
  [style]="{ width: '45%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="snapShotClose()"
>
  <div class="description">
    <p>
      {{ snapshotdata }}
    </p>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal" (click)="snapShotClose()">
      Close
    </button>
  </div></p-dialog
>
