.msg {
  margin-bottom: 2.4375rem;
  display: flex;
  font-size: 1.75rem;
  color: rgb(15 81 50);
}

.msg .bubble {
  padding: 0.625rem;
  border-radius: 0.3125rem;
  background-color: rgb(209 231 221);
  margin: 0 0.625rem 0.625rem 0.625rem;
  position: relative;
  min-width: 25.8125rem;
  max-width: 45.8125rem;
  padding-top: 3rem;
  word-break: break-all;
  .msg_time_send {
    position: absolute;
    left: 0;
    bottom: -1.7rem;
    color: darkgray;
    font-size: 1.2rem;
  }

  .msg_name_send {
    position: absolute;
    left: 0.3rem;
    top: 0.3rem;
    color: rgb(114, 111, 111);
    font-size: 1.5rem;
  }
}

.msg.msg-right .bubble {
  color: #56627e;
  background-color: #e0f2ff;

  .msg_time_send {
    right: 0;
    left: unset;
  }

  .msg_name_send {
    right: 1;
    left: unset;
  }
}

.msg .bubble .name {
  color: #393d4a;
  font-size: 0.75rem;
  font-weight: 500;
}

.msg.msg-left {
  justify-content: flex-start;
}

.msg.msg-right {
  flex-direction: row-reverse;
}

.body {
  max-height: 50rem;
  overflow: scroll;
}

.row {
  display: flex;
  flex-wrap: wrap;
}

// .header {
//   font-size: 20px;
//   font-weight: bolder;
// }

:host ::ng-deep .first-tabview .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border: 1px solid;
  padding: 10px;
  box-shadow: 2px 2px #f7b206 !important; /* Add box-shadow */
}

:host ::ng-deep .first-tabview .p-tabview .p-tabview-nav {
  display: flex;
  justify-content: space-between; /* spread across row */
}

:host ::ng-deep .first-tabview .p-tabview .p-tabview-nav li {
  flex: 1 1 20%; /* each tab 20% */
  max-width: 20%;
  text-align: center;
}

:host ::ng-deep .first-tabview .p-tabview .p-tabview-nav .p-tabview-nav-link {
  width: 100%;
  justify-content: center; /* center text inside */
}

:host ::ng-deep .p-dialog .p-dialog-header {
  background: #f7b206 !important ;
}

.modal-body-ticket {
  position: relative;
}

pre {
  background-color: transparent; /* or the desired background color */
  border: none;
  outline: none;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  color: #726f6f;
  font-size: 1.5rem;
}

.remark {
  white-space: pre-line;
}

// :host ::ng-deep .p-dialog-content {
//   overflow-y: hidden;
// }

// :host ::ng-deep .p-calendar .p-datepicker {
//   min-width: 100%;
//   position: fixed;
//   overflow: visible;
// }
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border-bottom: none !important;
  padding: 10px;
  box-shadow: none !important;
}
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav {
  display: flex;
  flex-wrap: nowrap;
  list-style-type: none;
  margin: 0;
  margin-top: 15px;
  padding: 0;
}

:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li {
  width: unset !important;
  margin-right: unset !important;
}

:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover,
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li .p-tabview-nav-link:focus {
  outline: none !important;
  box-shadow: none !important;
  border-bottom: 2px solid #f7b206 !important;
}
:host ::ng-deep .card-tabview .p-tabview-nav {
  display: grid;
  grid-template-columns: repeat(3, minmax(300px, 1fr)); /* Wider tabs */
  gap: 1rem;
  padding: 1rem;
  background: transparent;
  border: none;
}

:host ::ng-deep .card-tabview .p-tabview-nav li {
  margin: 0;
  border: none;
  width: 100%; /* Force li to fill the grid column */
}

:host ::ng-deep .card-tabview .p-tabview-nav-link {
  display: block;
  width: 100%; /* ⬅️ Very important */
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ccc;
  padding: 1.5rem;
  font-size: 16px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition:
    box-shadow 0.2s ease,
    border-color 0.2s ease;
}

:host ::ng-deep .card-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border-color: #f7b206;
  box-shadow: 0 0 8px #f7b206;
  font-weight: 600;
}
