<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Search Payment</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#paymentDetails"
            aria-expanded="false"
            aria-controls="paymentDetails"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="paymentDetails" class="panel-collapse collapse in">
        <div class="panel-body no-padding panel-udata">
          <div class="col-md-4 pcol" *ngIf="batchPaymentAccess">
            <div class="dbox">
              <a href="javascript:void(0)" (click)="batchList()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Batch Payment</h5>
              </a>
            </div>
          </div>
          <div class="col-md-4 pcol">
            <div class="dbox">
              <a href="javascript:void(0)" (click)="SearchPayment()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search Payment</h5>
              </a>
            </div>
          </div>
          <div class="col-md-4 pcol">
            <div class="dbox">
              <a href="javascript:void(0)" (click)="SearchOnlinePaymentAudit()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Online Payment Audit</h5>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" *ngIf="ifPaymentList">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <!-- <h3 class="panel-title">Search Payment</h3> -->
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchPayment"
            aria-expanded="false"
            aria-controls="searchPayment"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchPayment" class="panel-collapse collapse in">
        <div class="panel-body">
          <!-- <form [formGroup]="searchPaymentFormGroup"> -->
          <div class="row">
            <div class="col-lg-2">
              <label>Customer</label>
              <div>
                <p-dropdown
                  [disabled]="true"
                  [options]="parentCustList"
                  [showClear]="true"
                  [filter]="true"
                  filterBy="name"
                  [(ngModel)]="customerid"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a Customer"
                  styleClass="disableDropdown"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <button
                  type="button"
                  (click)="modalOpenParentCustomer()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  [disabled]="customerid == null"
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                  (click)="removeSelParentCust()"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Pay From Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Pay From Date"
                [(ngModel)]="payfromdate"
                (keydown.enter)="searchPayment('')"
              />
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Pay To Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Pay To Date"
                [(ngModel)]="paytodate"
                (keydown.enter)="searchPayment('')"
              />
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Pay Status</label>
              <div>
                <p-dropdown
                  [options]="payStatus"
                  optionValue="value"
                  optionLabel="label"
                  [filter]="true"
                  filterBy="value"
                  resetFilterOnHide="true"
                  placeholder="Select a Pay Status"
                  [(ngModel)]="paystatus"
                  (keydown.enter)="searchPayment('')"
                ></p-dropdown>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Cheque Number</label>
              <div>
                <input
                  filterBy="label"
                  class="form-control"
                  placeholder="Select a Cheque Number"
                  [(ngModel)]="chequeNumber"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-2">
              <label>Invoice Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Invoice Number"
                  [(ngModel)]="invoiceNumber"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
          </div>
          <div class="row">
            <div class="col-lg-2 pl-lg-1">
              <label>Created By Staff</label>
              <div>
                <p-dropdown
                  [disabled]="true"
                  [options]="staffCustList"
                  [showClear]="true"
                  [filter]="true"
                  filterBy="firstname"
                  [(ngModel)]="staffid"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a staff"
                  styleClass="disableDropdown"
                  (keydown.enter)="searchPayment('')"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <button
                  (click)="modalOpenStaff()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  [disabled]="staffid == null"
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                  (click)="removeSelStaff()"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Payment Mode</label>
              <div>
                <p-dropdown
                  [options]="paymentMode"
                  (onChange)="getPaymentMode()"
                  optionLabel="text"
                  optionValue="value"
                  [filter]="true"
                  filterBy="text"
                  resetFilterOnHide="true"
                  placeholder="Select a Payment Mode"
                  [(ngModel)]="paymode"
                  (keydown.enter)="searchPayment('')"
                ></p-dropdown>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Serive Area</label>
              <p-dropdown
                [options]="commondropdownService.serviceAreaList"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                resetFilterOnHide="true"
                placeholder="Select a Service Area"
                [(ngModel)]="serviceAreaId"
                (keydown.enter)="searchPayment('')"
              >
              </p-dropdown>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1" *ngIf="partnerId == 1">
              <label>Branch</label>
              <div>
                <p-dropdown
                  [options]="commondropdownService.branchesByServiceArea"
                  optionLabel="name"
                  optionValue="name"
                  filter="true"
                  filterBy="name"
                  resetFilterOnHide="true"
                  placeholder="Select a Branch"
                  [(ngModel)]="branchName"
                  (keydown.enter)="searchPayment('')"
                >
                </p-dropdown>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1" *ngIf="partnerId != 1">
              <label>Partner</label>
              <p-dropdown
                [options]="commondropdownService.partnerAllNAme"
                optionLabel="name"
                optionValue="name"
                filter="true"
                filterBy="name"
                resetFilterOnHide="true"
                placeholder="Select a Partner"
                [(ngModel)]="partnerName"
                (keydown.enter)="searchPayment('')"
              >
              </p-dropdown>
              <br />
            </div>

            <div class="col-lg-2 pl-lg-1">
              <label>Transaction Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Transaction Number"
                  [(ngModel)]="referenceno"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Business Unit</label>
              <div>
                <p-dropdown
                  [options]="businessUnit"
                  (onChange)="getBusinessUnit()"
                  optionLabel="buname"
                  optionValue="id"
                  filter="true"
                  filterBy="buname"
                  resetFilterOnHide="true"
                  placeholder="Select a Branch"
                  [(ngModel)]="buid"
                  (keydown.enter)="searchPayment('')"
                >
                </p-dropdown>
              </div>
              <br />
            </div>
          </div>
          <div class="row">
            <div class="col-lg-2 pl-lg-1">
              <label>Assigned to Staff</label>
              <div>
                <p-dropdown
                  [disabled]="true"
                  [options]="selectApproveList"
                  [showClear]="true"
                  [filter]="true"
                  filterBy="firstname"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a staff"
                  styleClass="disableDropdown"
                  [(ngModel)]="approveId"
                  (keydown.enter)="searchPayment('')"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <button
                  (click)="modalOpenApprove()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  [disabled]="approveId == null"
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                  (click)="removeSelAssigned()"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Receipt Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Invoice Number"
                  [(ngModel)]="receiptNo"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Cheque Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Cheque Date"
                [(ngModel)]="chequedate"
                (keydown.enter)="searchPayment('')"
              />
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Bank</label>
              <select
                [ngClass]="{
                  'is-invalid': submitted && paymentFormGroup.controls.bankManagement.errors
                }"
                class="form-control"
                [(ngModel)]="paydetails1"
                style="width: 100%"
              >
                <option value="">Select Source Bank</option>
                <option *ngFor="let bank of bankDataList" value="{{ bank.bankname }}">
                  {{ bank.bankname }} - {{ bank.accountnum }}
                </option>
              </select>
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Destination Bank</label>
              <select
                [ngClass]="{
                  'is-invalid': submitted && paymentFormGroup.controls.destinationBank.errors
                }"
                class="form-control"
                [(ngModel)]="destinationBank"
                style="width: 100%"
              >
                <option value="">Select Destination Bank</option>
                <option *ngFor="let bankDest of bankDestination" value="{{ bankDest.bankname }}">
                  {{ bankDest.bankname }} - {{ bankDest.accountnum }}
                </option>
              </select>
              <br />
              <br />
            </div>
          </div>
          <div class="addUpdateBtn">
            <button
              type="submit"
              class="btn btn-primary"
              id="searchbtn"
              (click)="searchPayment('')"
            >
              <i class="fa fa-search"></i>
              Search Payment
            </button>
            <button type="submit" class="btn btn-default" id="searchbtn" (click)="clearPayment()">
              <i class="fa fa-refresh"></i>
              Clear
            </button>
            <!-- <button type="submit" class="btn btn-default" id="searchbtn" (click)="openModal('myModal',63)">
                                                <i class="fa fa-refresh"></i> Open modal
                                            </button> -->
          </div>
          <!-- </form> -->
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-12" *ngIf="isPaymentSearch">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title" *ngIf="!ifAddbatchData">Payment List</h3>
        <h3 class="panel-title" *ngIf="ifAddbatchData">{{ BatchName }} Add New Mapping Data</h3>

        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#paymentList"
            aria-expanded="false"
            aria-controls="paymentList"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div *ngIf="!ifAddbatchData && createBatchAccess" style="margin: 2rem 12px">
        <button
          type="submit"
          class="btn btn-primary"
          title="Create Batch Name"
          data-target="#createBatchName"
          data-toggle="modal"
          data-backdrop="static"
          data-keyboard="false"
          (click)="clearBatchName()"
          [disabled]="chakedPaymentData.length == 0"
        >
          Create Batch
        </button>
      </div>
      <div *ngIf="ifAddbatchData" style="margin: 2rem 12px">
        <button
          type="submit"
          class="btn btn-primary"
          title="Add New Batch Mapping Data"
          (click)="addBatchPaymentMapping()"
        >
          Add New Batch Mapping Data
        </button>
      </div>

      <app-payment-amount-model
        *ngIf="displayInvoiceDetails"
        dialogId="PaymentDetailModal"
        [paymentId]="paymentId"
        (closeParentCustt)="closeParentCust()"
      ></app-payment-amount-model>

      <app-workflow-audit-details-modal
        *ngIf="ifModelIsShow"
        [auditcustid]="auditcustid"
        dialogId="custauditWorkflowModal"
        (closeParentCustt)="closeParentCustt()"
      ></app-workflow-audit-details-modal>

      <div id="paymentList" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length !== 0">
          <div class="row">
            <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 dataGroup">
              <label class="datalbl">Total Selected :</label>
              <span> {{ this.totalCheckedPayments.totalSelPayments }} </span>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 dataGroup">
              <label class="datalbl">Total Amount :</label>
              <span> {{ this.totalCheckedPayments.totalAmount }} </span>
            </div>
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th class="widthCheckboxColom">
                      <div class="centerCheckbox">
                        <p-checkbox
                          name="allChecked"
                          [(ngModel)]="ispaymentChecked"
                          [binary]="true"
                          (onChange)="allSelectBatch($event)"
                          [disabled]="!ifAddbatchData"
                        ></p-checkbox>
                      </div>
                    </th>
                    <th>Customer</th>
                    <th style="width: 7%">Amount</th>
                    <th>Receipt No/Credit Note No.</th>
                    <th>Invoice No.</th>
                    <th>Cheque No.</th>
                    <th>TDS</th>
                    <th>ABBS</th>
                    <th>Reference No.</th>
                    <th>Attachment</th>
                    <th>Pay Mode</th>
                    <th>Type</th>
                    <th>Payment Date</th>
                    <th>Status</th>
                    <th>ISP Name</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let payment of searchPaymentData
                        | paginate
                          : {
                              id: 'searchPaymentPageData',
                              itemsPerPage: paymentitemsPerPage,
                              currentPage: currentPagePaymentSlab,
                              totalItems: paymenttotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <div class="centerCheckbox">
                        <p-checkbox
                          class="p-field-checkbox"
                          [value]="payment.isSinglepaymentChecked"
                          [inputId]="payment.id"
                          [(ngModel)]="payment.isSinglepaymentChecked"
                          (onChange)="addbatchChecked(payment.id, $event)"
                          [binary]="true"
                          [disabled]="
                            payment.status === 'approved' ||
                            payment.status === 'rejected' ||
                            payment.batchAssigned == true ||
                            payment.approverid !== this.staffID
                          "
                        ></p-checkbox>
                      </div>
                    </td>
                    <td>
                      <a
                        href="javascript:void(0)"
                        style="color: #f7b206"
                        (click)="openModal('custmerDetailModal', payment.custId)"
                      >
                        {{ payment.customerName }}
                      </a>
                    </td>
                    <td>
                      <span
                        class="curson_pointer"
                        style="color: #f7b206"
                        (click)="openPaymentInvoiceModal('PaymentDetailModal', payment.id)"
                      >
                        <!-- {{ payment.amount | currency: currency : true : "1.2-2" }} -->
                        <!-- {{currency}} {{ payment.amount | number : "1.2-2" }} -->
                        <span>
                          <!-- {{
                            payment?.currency
                              ? payment?.currency + " " + (payment?.amount | number: "1.2-2")
                              : currency + " " + (payment?.amount | number: "1.2-2")
                          }} -->
                          {{
                            payment?.amount
                              | currency: payment?.currency || currency : "symbol" : "1.2-2"
                          }}
                        </span>
                      </span>
                    </td>
                    <td>{{ payment.documentno }}</td>
                    <td>{{ payment.invoiceNumber }}</td>
                    <td>{{ payment.paydetails2 }}</td>
                    <td>{{ payment.tdsamount }}</td>
                    <td>{{ payment.abbsAmount }}</td>
                    <td>{{ payment.referenceno }}</td>
                    <td>
                      <a
                        (click)="downloadFile(payment.filename, payment.id, payment.custId)"
                        href="javascript:void(0)"
                        style="color: blue"
                        >{{ payment.filename ? payment.filename : "-" }}</a
                      >
                    </td>

                    <td>
                      <a
                        *ngIf="payment.paymode == 'Cheque'"
                        style="color: #f7b206"
                        class="HoverEffect"
                        data-backdrop="static"
                        data-keyboard="false"
                        title="Payment Details"
                        (click)="openPaymentModal(payment.id)"
                      >
                        {{ payment.paymode }}
                      </a>
                      <span *ngIf="payment.paymode !== 'Cheque'">
                        {{ payment.paymode }}
                      </span>
                    </td>
                    <td>{{ payment.type }}</td>
                    <td>{{ payment.paymentdate }}</td>
                    <td *ngIf="payment.status === 'Fully Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'advance'">
                      <span class="badge badge-success">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'approved'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="
                        payment.status === 'pending' && payment.nextTeamHierarchyMappingId == null
                      "
                    >
                      <span class="badge badge-info">
                        {{ "Collected" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="
                        payment.status === 'pending' && payment.nextTeamHierarchyMappingId != null
                      "
                    >
                      <span class="badge badge-info">
                        {{ "Submitted" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'Partialy Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'rejected'">
                      <span class="badge badge-danger">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td>{{ payment.mvnoName }}</td>

                    <td class="btnAction">
                      <button
                        *ngIf="downloadAccess"
                        id="download-button"
                        type="button"
                        class="approve-btn"
                        title="Download"
                        [disabled]="payment.status === 'pending' || payment.status === 'rejected'"
                        (click)="downloadreceipt(payment.id)"
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/pdf.png"
                        />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        *ngIf="payment.status !== 'Fully Adjusted'"
                        [disabled]="
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'advance' ||
                          payment.status === 'approved' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.approverid != null
                        "
                        (click)="pickModalOpen(payment)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        type="button"
                        class="approve-btn"
                        (click)="approveModalOpen(payment)"
                        title="Approve"
                        [disabled]="
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'advance' ||
                          payment.status === 'approved' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.batchAssigned == true ||
                          payment.approverid !== this.staffID
                        "
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>

                      <button
                        type="button"
                        class="approve-btn"
                        (click)="rejectModalOpen(payment)"
                        title="Reject"
                        [disabled]="
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'approved' ||
                          payment.status === 'advance' ||
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.batchAssigned == true ||
                          payment.approverid !== this.staffID
                        "
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>
                      <a
                        class="detailOnAnchorClick"
                        title="Audit & Status Details"
                        style="margin-left: 4px"
                        (click)="openPaymentWorkFlow('custauditWorkflowModal', payment.id)"
                      >
                        <img
                          width="25"
                          height="25"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>
                      <button
                        type="button"
                        class="approve-btn"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        id="assign-button"
                        title="Reassign Payment"
                        (click)="StaffReasignList(payment.id)"
                        *ngIf="
                          !(
                            payment.status == 'Partialy Adjusted' ||
                            payment.status == 'Fully Adjusted' ||
                            payment.status === 'approved' ||
                            payment.status === 'rejected'
                          ) && reassignAccess
                        "
                        [disabled]="payment.approverid !== this.staffID"
                      >
                        <img
                          width="32"
                          height="32"
                          alt="Assign CAF"
                          src="assets/img/icons-02.png"
                        />
                      </button>
                    </td>
                    <!-- <td class="btnAction">
                                                                        <a id="edit-button" href="javascript:void(0)" type="button"
                                                                            (click)="editCountry(payment.id)"><img src="assets/img/ioc01.jpg" /></a>
                                                                        <a id="delete-button" href="javascript:void(0)"
                                                                            (click)="deleteConfirmonCountry(payment.id)">
                                                                            <img src="assets/img/ioc02.jpg" />
                                                                        </a>
                                                                    </td> -->
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="searchPaymentPageData"
                  [maxSize]="10"
                  [directionLinks]="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedPaymentList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length === 0">
          Details are not available
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Batch Payment Search Start-->

<div class="row" *ngIf="ifBatchList && batchPaymentAccess">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <!-- <h3 class="panel-title">Search Payment</h3> -->
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchPayment"
            aria-expanded="false"
            aria-controls="searchPayment"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchPayment" class="panel-collapse collapse in">
        <div class="panel-body">
          <!-- <form [formGroup]="searchPaymentFormGroup"> -->
          <div class="row">
            <div class="col-lg-2 pl-lg-1">
              <label>Batch Status</label>
              <div>
                <p-multiSelect
                  [options]="batchStatus"
                  optionValue="value"
                  optionLabel="label"
                  [filter]="true"
                  filterBy="value"
                  resetFilterOnHide="true"
                  placeholder="Select a Pay Status"
                  [(ngModel)]="status"
                  (keydown.enter)="searchBatch('')"
                ></p-multiSelect>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Staff</label>
              <div>
                <p-dropdown
                  [disabled]="true"
                  [options]="staffCustList"
                  [showClear]="true"
                  [filter]="true"
                  filterBy="firstname"
                  [(ngModel)]="batchStaffid"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a staff"
                  styleClass="disableDropdown"
                  (keydown.enter)="searchBatch('')"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <button
                  (click)="modalOpenStaff()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  [disabled]="staffid == null"
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                  (click)="removeSelStaff()"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Serive Area</label>
              <p-dropdown
                [options]="commondropdownService.serviceAreaList"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                resetFilterOnHide="true"
                placeholder="Select a Service Area"
                [(ngModel)]="serviceAreaId"
                (keydown.enter)="searchBatch('')"
              >
              </p-dropdown>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1" *ngIf="partnerId == 1">
              <label>Branch</label>
              <div>
                <p-dropdown
                  [options]="commondropdownService.branchesByServiceArea"
                  optionLabel="name"
                  optionValue="id"
                  filter="true"
                  filterBy="name"
                  resetFilterOnHide="true"
                  placeholder="Select a Branch"
                  [(ngModel)]="branchName"
                  (keydown.enter)="searchBatch('')"
                >
                </p-dropdown>
              </div>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1" *ngIf="partnerId != 1">
              <label>Partner</label>
              <p-dropdown
                [options]="commondropdownService.partnerAllNAme"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                resetFilterOnHide="true"
                placeholder="Select a Partner"
                [(ngModel)]="partnerName"
                (keydown.enter)="searchBatch('')"
              >
              </p-dropdown>
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Pay From Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter From Date"
                [(ngModel)]="batchPayfromdate"
                (keydown.enter)="searchBatch('')"
              />
              <br />
            </div>
            <div class="col-lg-2 pl-lg-1">
              <label>Pay To Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter To Date"
                [(ngModel)]="batchPaytodate"
                (keydown.enter)="searchBatch('')"
              />
              <br />
            </div>
          </div>
          <div class="row">
            <div class="col-lg-2 pl-lg-1">
              <label>Destination Bank</label>
              <select class="form-control" [(ngModel)]="batchDestinationBank" style="width: 100%">
                <option value="">Select Destination Bank</option>
                <option *ngFor="let bankDest of bankDestination" value="{{ bankDest.id }}">
                  {{ bankDest.bankname }} - {{ bankDest.accountnum }}
                </option>
              </select>
              <br />
              <br />
            </div>
          </div>
          <div class="addUpdateBtn">
            <button type="submit" class="btn btn-primary" id="searchbtn" (click)="searchBatch('')">
              <i class="fa fa-search"></i>
              Search Batch
            </button>
            <button type="submit" class="btn btn-default" id="searchbtn" (click)="clearBatch()">
              <i class="fa fa-refresh"></i>
              Clear
            </button>
            <!-- <button type="submit" class="btn btn-default" id="searchbtn" (click)="openModal('myModal',63)">
                                                <i class="fa fa-refresh"></i> Open modal
                                            </button> -->
          </div>
          <!-- </form> -->
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="col-md-12" *ngIf="isPaymentSearch">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title" *ngIf="!ifAddbatchData">Payment List</h3>
        <h3 class="panel-title" *ngIf="ifAddbatchData">{{ BatchName }} Add New Mapping Data</h3>

        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#paymentList"
            aria-expanded="false"
            aria-controls="paymentList"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div *ngIf="!ifAddbatchData && createBatchAccess" style="margin: 2rem 12px">
        <button
          type="submit"
          class="btn btn-primary"
          title="Create Batch Name"
          data-target="#createBatchName"
          data-toggle="modal"
          data-backdrop="static"
          data-keyboard="false"
          (click)="clearBatchName()"
          [disabled]="chakedPaymentData.length == 0"
        >
          Create Batch
        </button>
      </div>
      <div *ngIf="ifAddbatchData" style="margin: 2rem 12px">
        <button
          type="submit"
          class="btn btn-primary"
          title="Add New Batch Mapping Data"
          (click)="addBatchPaymentMapping()"
        >
          Add New Batch Mapping Data
        </button>
      </div>

      <div id="paymentList" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length !== 0">
          <div class="row">
            <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 dataGroup">
              <label class="datalbl">Total Selected :</label>
              <span> {{ this.totalCheckedPayments.totalSelPayments }} </span>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 dataGroup">
              <label class="datalbl">Total Amount :</label>
              <span> {{ this.totalCheckedPayments.totalAmount }} </span>
            </div>
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th class="widthCheckboxColom">
                      <div class="centerCheckbox">
                        <p-checkbox
                          name="allChecked"
                          [(ngModel)]="ispaymentChecked"
                          [binary]="true"
                          (onChange)="allSelectBatch($event)"
                          [disabled]="!ifAddbatchData"
                        ></p-checkbox>
                      </div>
                    </th>
                    <th>Customer</th>
                    <th style="width: 7%">Amount</th>
                    <th>Receipt No/Credit Note No.</th>
                    <th>Invoice No.</th>
                    <th>Cheque No.</th>
                    <th>TDS</th>
                    <th>ABBS</th>
                    <th>Reference No.</th>
                    <th>Pay Mode</th>
                    <th>Type</th>
                    <th>Payment Date</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let payment of searchPaymentData
                        | paginate
                          : {
                              id: 'searchPaymentPageData',
                              itemsPerPage: paymentitemsPerPage,
                              currentPage: currentPagePaymentSlab,
                              totalItems: paymenttotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <div class="centerCheckbox">
                        <p-checkbox
                          class="p-field-checkbox"
                          [value]="payment.isSinglepaymentChecked"
                          [inputId]="payment.id"
                          [(ngModel)]="payment.isSinglepaymentChecked"
                          (onChange)="addbatchChecked(payment.id, $event)"
                          [binary]="true"
                          [disabled]="
                            payment.status === 'approved' ||
                            payment.status === 'rejected' ||
                            payment.batchAssigned == true ||
                            payment.approverid !== this.staffID
                          "
                        ></p-checkbox>
                      </div>
                    </td>
                    <td>
                      <a
                        href="javascript:void(0)"
                        style="color: #f7b206"
                        (click)="openModal('custmerDetailModal', payment.custId)"
                      >
                        {{ payment.customerName }}
                      </a>
                    </td>
                    <td>
                      <span
                        class="curson_pointer"
                        style="color: #f7b206"
                        (click)="openPaymentInvoiceModal('PaymentDetailModal', payment.id)"
                      >
                        {{ payment.amount | number : "1.2-2" }}
                      </span>
                    </td>
                    <td>{{ payment.documentno }}</td>
                    <td>{{ payment.invoiceNumber }}</td>
                    <td>{{ payment.paydetails2 }}</td>
                    <td>{{ payment.tdsamount }}</td>
                    <td>{{ payment.abbsAmount }}</td>
                    <td>{{ payment.referenceno }}</td>
                    <td>
                      <a
                        *ngIf="payment.paymode == 'Cheque'"
                        style="color: #f7b206"
                        class="HoverEffect"
                        data-backdrop="static"
                        data-keyboard="false"
                        data-target="#paymentModal"
                        data-toggle="modal"
                        title="Payment Details"
                        (click)="openPaymentModal(payment.id)"
                      >
                        {{ payment.paymode }}
                      </a>
                      <span *ngIf="payment.paymode !== 'Cheque'">
                        {{ payment.paymode }}
                      </span>
                    </td>
                    <td>{{ payment.type }}</td>
                    <td>{{ payment.paymentdate }}</td>
                    <td *ngIf="payment.status === 'Fully Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'advance'">
                      <span class="badge badge-success">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'approved'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="
                        payment.status === 'pending' && payment.nextTeamHierarchyMappingId == null
                      "
                    >
                      <span class="badge badge-info">
                        {{ "Collected" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="
                        payment.status === 'pending' && payment.nextTeamHierarchyMappingId != null
                      "
                    >
                      <span class="badge badge-info">
                        {{ "Submitted" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'Partialy Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'rejected'">
                      <span class="badge badge-danger">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>

                    <td class="btnAction">
                      <a
                        id="download-button"
                        type="button"
                        class="curson_pointer"
                        (click)="downloadreceipt(payment.id)"
                        title="Download"
                        [attr.disabled]="
                          payment.status === 'pending' || payment.status === 'rejected'
                        "
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/pdf.png"
                        />
                      </a>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        *ngIf="payment.status !== 'Fully Adjusted'"
                        [disabled]="
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'advance' ||
                          payment.status === 'approved' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.approverid != null
                        "
                        (click)="pickModalOpen(payment)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        type="button"
                        class="approve-btn"
                        (click)="approveModalOpen(payment)"
                        title="Approve"
                        [disabled]="
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'advance' ||
                          payment.status === 'approved' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.batchAssigned == true ||
                          payment.approverid !== this.staffID
                        "
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>

                      <button
                        type="button"
                        class="approve-btn"
                        (click)="rejectModalOpen(payment)"
                        title="Reject"
                        [disabled]="
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'approved' ||
                          payment.status === 'advance' ||
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.batchAssigned == true ||
                          payment.approverid !== this.staffID
                        "
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>
                      <a
                        class="detailOnAnchorClick"
                        title="Audit & Status Details"
                        style="margin-left: 4px"
                        (click)="openPaymentWorkFlow('custauditWorkflowModal', payment.id)"
                      >
                        <img
                          width="25"
                          height="25"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>
                      <button
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        id="assign-button"
                        title="Reassign Payment"
                        (click)="StaffReasignList(payment.id)"
                        *ngIf="
                          !(
                            payment.status == 'Partialy Adjusted' ||
                            payment.status == 'Fully Adjusted'
                          ) && reassignAccess
                        "
                      >
                        <img
                          width="32"
                          height="32"
                          alt="Assign CAF"
                          src="assets/img/icons-02.png"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="searchPaymentPageData"
                  [maxSize]="10"
                  [directionLinks]="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedPaymentList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length === 0">
          Details are not available
        </div>
      </div>
    </div>
  </div> -->
</div>

<!-- Batch Payment Search End -->

<div class="row" *ngIf="ifBatchList">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Batch</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#batchPayment"
            aria-expanded="false"
            aria-controls="batchPayment"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="batchPayment" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="batchPaymentList.length !== 0">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Batch Name</th>
                    <th>Total Invoices count</th>
                    <th>Total Payment Amount</th>
                    <th>TDS</th>
                    <th>ABBS</th>
                    <th>Assignee</th>
                    <!-- <th>Assignment Status</th> -->
                    <th>Batch Status</th>
                    <th>File</th>
                    <th>Approve / Reject</th>
                    <th style="width: 15%">Action</th>
                    <!-- <th>View</th> -->
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of batchPaymentList
                        | paginate
                          : {
                              id: 'searchbatchPageData',
                              itemsPerPage: batchitemsPerPage,
                              currentPage: currentPagebatch,
                              totalItems: batchtotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <!-- <span
                        class="curson_pointer"
                        style="color: #f7b206"
                        title="Batch Mapping List"
                        data-backdrop="static"
                        data-keyboard="false"
                        (click)="batchMappingList(data.batchId)"
                      > -->
                      {{ data.batchName }}
                      <!-- </span> -->
                    </td>
                    <td>{{ data.invoiceCount }}</td>
                    <td>{{ data.totalAmount | number: "1.2-2" }}</td>
                    <td>{{ tdsAmount(data) | number: "1.2-2" }}</td>
                    <td>{{ abbsAmount(data) | number: "1.2-2" }}</td>
                    <td>{{ data.nextstaffname }}</td>
                    <!-- <td>
                      <span *ngIf="data.assignmentStatus === 'Approved'">
                        <span class="badge badge-success">
                          {{ data.assignmentStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.assignmentStatus === 'Pending'">
                        <span class="badge badge-info">
                          {{ data.assignmentStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.assignmentStatus === 'Rejected'">
                        <span class="badge badge-danger">
                          {{ data.assignmentStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.assignmentStatus === 'Pending Approve'">
                        <span class="badge badge-info">
                          {{ data.assignmentStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.assignmentStatus === 'Partial Approved'">
                        <span class="badge badge-success">
                          {{ data.assignmentStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.assignmentStatus === 'AssignedToOtherTeam'">
                        <span class="badge badge-success"> Assigned </span>
                      </span>
                    </td> -->
                    <td>
                      <span *ngIf="data.batchStatus.toLowerCase() === 'approved'">
                        <span class="badge badge-success">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span>
                      <!-- <span *ngIf="data.batchStatus === 'Pending'">
                        <span class="badge badge-info">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span> -->
                      <span *ngIf="data.batchStatus.toLowerCase() === 'rejected'">
                        <span class="badge badge-danger">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.batchStatus.toLowerCase() === 'pending approve'">
                        <span class="badge badge-info">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.batchStatus.toLowerCase() === 'partial approved'">
                        <span class="badge badge-success">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.batchStatus.toLowerCase() === 'verified'">
                        <span class="badge badge-success">
                          {{ data.batchStatus | titlecase }}
                        </span>
                      </span>
                      <span *ngIf="data.batchStatus.toLowerCase() === 'pending'">
                        <span class="badge badge-info">
                          {{
                            checkPendingStatus(data.assignmentStatus, data.batchStatus) | titlecase
                          }}
                        </span>
                      </span>
                    </td>
                    <td>
                      <a
                        (click)="downloadInvoice(data.creditDocId, data.custId, data.filename)"
                        *ngIf="data.filename != null"
                        href="javascript:void(0)"
                        style="color: #28a745; font-size: 20px"
                        title="Download File"
                      >
                        <img src="assets/img/pdf.png" style="width: 25px; height: 25px" />
                      </a>
                      <span *ngIf="data.filename == null"> - </span>
                    </td>
                    <td class="btnAction">
                      <div class="displayflex">
                        <div>
                          <button
                            type="button"
                            class="btn btn-success gridbtn"
                            [disabled]="
                              (data.assignmentStatus == 'Approved' &&
                                data.batchStatus == 'Approved') ||
                              (data.assignmentStatus == 'Rejected' &&
                                data.batchStatus == 'Rejected') ||
                              data.assignmentStatus == 'AssignedToOtherTeam'
                            "
                            (click)="openBatchApporve(data.batchId, data)"
                            title="Approve"
                          >
                            <i class="fa fa-check" aria-hidden="true"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-danger gridbtn"
                            [disabled]="
                              (data.assignmentStatus == 'Approved' &&
                                data.batchStatus == 'Approved') ||
                              (data.assignmentStatus == 'Rejected' &&
                                data.batchStatus == 'Rejected') ||
                              data.assignmentStatus == 'AssignedToOtherTeam'
                            "
                            (click)="openBatchReject(data.batchId)"
                            title="Reject"
                          >
                            <i class="fa fa-times" aria-hidden="true"></i>
                          </button>
                        </div>
                      </div>
                    </td>
                    <td class="btnAction">
                      <!-- <button
                        *ngIf="deleteAccess"
                        type="button"
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        [disabled]="
                          data.createdBy != data.assignee || data.batchStatus != 'Pending'
                        "
                        (click)="deleteBatchPayment(data.batchId)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </button> -->

                      <a
                        *ngIf="batchAuditDetailsAccess"
                        class="detailOnAnchorClick"
                        style="margin-right: 3px"
                        title="Batch Audit Details"
                        data-backdrop="static"
                        data-keyboard="false"
                        (click)="batchPaymentAuditDetails(data.batchId, '')"
                      >
                        <img
                          width="32"
                          height="32"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>

                      <button
                        *ngIf="createAccess"
                        type="button"
                        class="approve-btn"
                        style="border: none; margin-right: 3px"
                        (click)="newADDbatch(data)"
                        [disabled]="
                          data.createdBy != data.assignee || data.batchStatus != 'Pending'
                        "
                        title="Add Batch Payment Mapping"
                      >
                        <i
                          class="fa fa-plus"
                          style="
                            background: #f7b206;
                            padding: 5.5px;
                            height: 31px;
                            width: 31px;
                            color: white;
                            font-size: 16px;
                          "
                        ></i>
                      </button>
                      <!-- <button
                                                            type="button"
                                                            class="approve-btn"
                                                            style="
                                                              border: none;
                                                              background: transparent;
                                                              padding: 0;
                                                              margin-right: 3px;
                                                            "
                                                            [disabled]="data.assignmentStatus == 'Approved'"
                                                            (click)="openBatchApporve(data.batchId)"
                                                            title="Approve"
                                                            data-target="#assignbatchModal"
                                                            data-toggle="modal"
                                                            data-backdrop="static"
                                                            data-keyboard="false"
                                                          >
                                                            <img src="assets/img/assign.jpg" />
                                                          </button> -->
                      <!-- <button
                                                            type="button"
                                                            class="approve-btn"
                                                            style="
                                                              border: none;
                                                              background: transparent;
                                                              padding: 0;
                                                              margin-right: 3px;
                                                            "
                                                            [disabled]="data.assignmentStatus == 'Approved'"
                                                            (click)="openBatchReject(data.batchId)"
                                                            title="Reject"
                                                            data-target="#assignbatchModal"
                                                            data-toggle="modal"
                                                            data-backdrop="static"
                                                            data-keyboard="false"
                                                          >
                                                            <img src="assets/img/reject.jpg" />
                                                          </button> -->
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="searchbatchPageData"
                  [maxSize]="10"
                  [directionLinks]="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedPaymentBatchList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPageBatchList($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-body table-responsive" *ngIf="batchPaymentList.length === 0">
          Details are not available
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <div class="modal fade" id="createBatchName" role="dialog">
  <div class="modal-dialog" style="width: 35%"> -->
<!-- Modal content-->
<!-- <div class="modal-content"> -->
<!-- <div class="modal-header">
        <h3 class="panel-title">Create Batch</h3>
      </div> -->
<p-dialog
  header="Create Batch"
  [(visible)]="isBatchNameModelVisible"
  [modal]="true"
  [style]="{ width: '30vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
  (onHide)="closeBatchName()"
>
  <div class="modal-body">
    <div class="form-group">
      <label for="newBatchName">Batch Name:</label>
      <input
        type="newBatchName"
        class="form-control"
        id="newBatchName"
        placeholder="Enter Batch Name"
        [(ngModel)]="newBatchName"
      />
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        (click)="batchSaveOnly()"
        [disabled]="!newBatchName"
      >
        Save Only
      </button>
      <!-- <button
                                type="submit"
                                class="btn btn-primary btn-sm"
                                id="closeModal"
                                (click)="batchSaveAssign()"
                                [disabled]="!newBatchName"
                              >
                                Save & Assign
                              </button> -->

      <button
        type="button"
        class="btn btn-danger btn-sm"
        (click)="closeBatchName()"
        style="margin-left: 1rem"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>

<!-- <div
  class="modal fade"
  id="assignbatchModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
> -->
<!-- <div class="modal-dialog" role="document"> -->
<!-- <div class="modal-content"> -->
<!-- <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
<p-dialog
  header="{{
    batchAssignStaff
      ? 'Batch Payment Assign By Staff'
      : batchApporve
        ? 'Approve Batch'
        : 'Reject Batch'
  }}"
  [(visible)]="isAssignbatchModelVisible"
  [modal]="true"
  [style]="{ width: '30vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="false"
  (onHide)="closeBatchName()"
>
  <!-- <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
    <span *ngIf="batchAssignStaff">Batch Payment Assign By Staff</span>
    <span *ngIf="batchApporve">Apporve Batch</span>
    <span *ngIf="batchReject">Reject Batch</span>
  </h4> -->
  <!-- </div> -->
  <div class="modal-body">
    <form [formGroup]="assignPaymentStaffForm">
      <div class="row" *ngIf="batchAssignStaff">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Staff *</label>
          <div>
            <p-dropdown
              [options]="staffList"
              optionValue="staffId"
              optionLabel="fullName"
              [filter]="true"
              filterBy="fullName"
              placeholder="Select a staff"
              formControlName="nextStaffId"
              [ngClass]="{
                'is-invalid': staffsubmmitted && assignPaymentStaffForm.controls.nextStaffId.errors
              }"
            ></p-dropdown>
          </div>
          <div
            class="errorWrap text-danger"
            *ngIf="staffsubmmitted && assignPaymentStaffForm.controls.nextStaffId.errors"
          >
            <div
              class="error text-danger"
              *ngIf="staffsubmmitted && assignPaymentStaffForm.controls.nextStaffId.errors.required"
            >
              staff is required.
            </div>
          </div>
          <br />
        </div>
      </div>
      <div class="row" *ngIf="batchApporve || batchReject">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark *</label>
          <textarea
            class="form-control"
            name="remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': staffsubmmitted && assignPaymentStaffForm.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="staffsubmmitted && assignPaymentStaffForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="staffsubmmitted && assignPaymentStaffForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="batchAssignStaff"
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="batchPaymentAssignStaff()"
      [disabled]="!assignPaymentStaffForm.value.nextStaffId"
      data-dismiss="modal"
    >
      <i class="fa fa-check-circle"></i>
      Assign Staff
    </button>
    <button
      *ngIf="batchApporve"
      type="submit"
      class="btn btn-primary"
      id="submit"
      [disabled]="!assignPaymentStaffForm.value.remark"
      (click)="AssignApporveStaff()"
      data-dismiss="modal"
    >
      <i class="fa fa-check-circle"></i>
      Approve Batch
    </button>
    <button
      *ngIf="batchReject"
      type="submit"
      class="btn btn-primary"
      id="submit"
      [disabled]="!assignPaymentStaffForm.value.remark"
      (click)="AssignRejectedStaff()"
      data-dismiss="modal"
    >
      <i class="fa fa-check-circle"></i>
      Reject Batch
    </button>
    <button
      type="button"
      class="btn btn-default"
      data-dismiss="modal"
      (click)="batchModelVisibleClose()"
    >
      Close
    </button>
  </div>
  <!-- </div> -->
  <!-- </div> -->
  <!-- </div> -->
  <!-- <div aria-labelledby="myModalLabel" class="modal fade" id="teamModal" role="dialog" tabindex="-1"> -->
  <!-- <div class="modal-dialog" role="document"> -->
  <!-- <div class="modal-content"> -->
  <!-- <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Select Team</h4>
      </div> -->
</p-dialog>

<p-dialog
  header="Select Team"
  [(visible)]="isSelectTeamModelVisible"
  [modal]="true"
  [style]="{ width: '30vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
  (onHide)="batchModelVisibleClose()"
>
  <div class="modal-body">
    <div>
      <p-table
        *ngIf="!teamToggle"
        [value]="teams"
        [(selection)]="teamselected"
        dataKey="id"
        [tableStyle]="{ 'min-width': '50rem' }"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 4rem"></th>
            <th>Name</th>
            <th>Partner Name</th>
            <th>Status</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-product>
          <tr>
            <td>
              <p-tableRadioButton [value]="product"></p-tableRadioButton>
            </td>
            <td>{{ product.name }}</td>
            <td>{{ product.partnername }}</td>
            <td>{{ product.status }}</td>
          </tr>
        </ng-template>
      </p-table>
      <p-table
        *ngIf="teamToggle"
        [value]="staffDataList"
        [(selection)]="staffselected"
        dataKey="id"
        [tableStyle]="{ 'min-width': '50rem' }"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 4rem"></th>
            <th>Name</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-product>
          <tr>
            <td>
              <p-tableRadioButton [value]="product"></p-tableRadioButton>
            </td>
            <td>{{ product.name }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" *ngIf="teamToggle" (click)="teamToggle = false" type="button">
      < Back
    </button>
    <button class="btn btn-primary" *ngIf="!teamToggle" (click)="selectedTeam()" type="button">
      Select Staff
    </button>
    <button
      class="btn btn-primary"
      *ngIf="teamToggle"
      type="button"
      (click)="assignBatchModelVisible()"
    >
      Save
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="batchModelVisibleClose()"
    >
      Close
    </button>
  </div>
</p-dialog>
<!-- </div> -->
<!-- </div> -->
<!-- </div> -->
<p-dialog
  header="Batch Payment Audit Details"
  [(visible)]="batchPaymentAudit"
  [modal]="true"
  [style]="{ width: '75%' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
>
  <!-- <div class="modal fade" id="batchPaymentAudit" role="dialog">
    <div class="modal-dialog" style="width: 75%">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
            Batch Payment Audit Details
          </h4>
        </div> -->
  <div class="modal-body">
    <div class="panel-body table-responsive">
      <div class="row">
        <div class="col-lg-12 col-md-12">
          <table class="table">
            <thead>
              <tr>
                <th>Batch Name</th>
                <th>Staff Name</th>
                <th>Team Name</th>
                <th>Status</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let data of batchPaymentAuditList
                    | paginate
                      : {
                          id: 'searchbatchAuditPageData',
                          itemsPerPage: batchAudititemsPerPage,
                          currentPage: currentPagebatchAudit,
                          totalItems: batchAudittotalRecords
                        };
                  index as i
                "
              >
                <td>{{ data.batchName }}</td>
                <td>{{ data.staffName }}</td>
                <td>
                  <span *ngIf="data.teamName">{{ data.teamName }}</span>
                  <span *ngIf="!data.teamName">-</span>
                </td>
                <td>
                  <span *ngIf="data.status === 'Approved'">
                    <span class="badge badge-success">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Pending'">
                    <span class="badge badge-info">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Rejected'">
                    <span class="badge badge-danger">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Pending Approve'">
                    <span class="badge badge-info">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Partial Approved'">
                    <span class="badge badge-success">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                </td>
                <td>
                  <span *ngIf="data.remark">{{ data.remark }}</span>
                  <span *ngIf="!data.remark">-</span>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="pagination_Dropdown">
            <pagination-controls
              id="searchbatchAuditPageData"
              [maxSize]="10"
              [directionLinks]="true"
              previousLabel=""
              nextLabel=""
              (pageChange)="pageChangedPaymentBatchAuditList($event)"
            ></pagination-controls>
            <div id="itemPerPageDropdown">
              <p-dropdown
                [options]="pageLimitOptions"
                optionLabel="value"
                optionValue="value"
                (onChange)="TotalItemPerPageBatchAuditList($event)"
              ></p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" (click)="closeBatchpaymentAuditModel()">
      Close
    </button>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Batch Details"
  [(visible)]="batchMapping"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeBatchDetailsDialog()"
>
  <!-- <div class="modal fade" id="batchMapping" role="dialog">
  <div class="modal-dialog" style="width: 75%">
    <div class="modal-content">
      <div class="modal-header">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
          
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Batch Details</h4>
      </div> -->
  <div class="modal-body">
    <div class="panel-body table-responsive">
      <div class="row">
        <div class="col-lg-12 col-md-12">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 10px">
                  <input
                    (change)="checkUncheckAllInvoice()"
                    [(ngModel)]="masterSelected"
                    name="master-checkbox"
                    type="checkbox"
                  />
                </th>
                <th>Customer Name</th>
                <th>Amount</th>
                <th>TDS</th>
                <th>ABBS</th>
                <th>Paymode</th>
                <th>Status</th>
                <th>Date</th>
                <th>Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let data of batchMappingData
                    | paginate
                      : {
                          id: 'searchbatchMappingPageData',
                          itemsPerPage: batchMappingitemsPerPage,
                          currentPage: currentPagebatchMapping,
                          totalItems: batchMappingtotalRecords
                        };
                  index as i
                "
              >
                <td>
                  <input
                    (change)="isAllSelectedInvoice()"
                    [(ngModel)]="data.isSelected"
                    name="master-checkbox"
                    type="checkbox"
                  />
                </td>
                <td>{{ data.customerName }}</td>
                <td *ngIf="!data.isSelected">{{ data.amount }}</td>
                <td *ngIf="data.isSelected">
                  <input
                    [(ngModel)]="data.amount"
                    class="form-control"
                    type="number"
                    name="amount"
                    (keypress)="keypressId($event)"
                  />
                </td>
                <td>{{ data.tdsAmount }}</td>
                <td>{{ data.abbsAmount }}</td>
                <td>{{ data.paymode }}</td>
                <td>
                  <span *ngIf="data.status === 'Fully Adjusted'">
                    <span class="badge badge-success">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'pending'">
                    <span class="badge badge-info">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'rejected'">
                    <span class="badge badge-danger">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Pending Approve'">
                    <span class="badge badge-info">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                  <span *ngIf="data.status === 'Partialy Adjusted'">
                    <span class="badge badge-success">
                      {{ data.status | titlecase }}
                    </span>
                  </span>
                </td>
                <td>{{ data.paymentdate }}</td>
                <td>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Delete"
                    (click)="deleteBatchPaymentMapping(data.mappingId)"
                    [disabled]="
                      batchMappingPersonalData.createdBy != batchMappingPersonalData.assignee ||
                      batchMappingPersonalData.batchStatus != 'Pending'
                    "
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="pagination_Dropdown">
            <pagination-controls
              id="searchbatchMappingPageData"
              [maxSize]="10"
              [directionLinks]="true"
              previousLabel=""
              nextLabel=""
              (pageChange)="pageChangedPaymentBatchMappingtList($event)"
            ></pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      id="addAtt"
      class="btn btn-primary"
      data-dismiss="modal"
      (click)="updateBatch()"
      [disabled]="this.checkedList.length == 0"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button
      type="button"
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeBatchDetailsDialog()"
    >
      Close
    </button>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>
<p-dialog
  header="Assign Payment"
  [(visible)]="assignCustomerCAFModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Payment</h4>
      </div> -->
  <div class="modal-body">
    <div class="row">
      <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <div class="row">
            <div class="col-md-6">
              <input
                id="searchStaffName"
                type="text"
                name="username"
                class="form-control"
                placeholder="Global Search Filter"
                [(ngModel)]="searchStaffDeatil"
                (keydown.enter)="searchStaffName()"
                [ngModelOptions]="{ standalone: true }"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <button
                (click)="searchStaffName()"
                class="btn btn-primary"
                id="searchbtn"
                type="submit"
                [disabled]="!searchStaffDeatil"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button (click)="clearSearch()" class="btn btn-default" id="searchbtn" type="reset">
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
          <h5>Select Staff</h5>
          <p-table
            [value]="approveCAF"
            [(selection)]="selectStaff"
            responsiveLayout="scroll"
            [paginator]="true"
            [rows]="5"
            [rowsPerPageOptions]="[5, 10, 15, 20]"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-product>
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      *ngIf="approved"
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="assignToStaff(true)"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button type="button" class="btn btn-default" (click)="closeAssignCustomerModel()">
      Close
    </button>
  </div>
  <!-- </div>
  </div> -->
</p-dialog>
<!-- <div
  class="modal fade"
  id="assignCustomerCAFModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  
</div> -->
<p-dialog
  header="Assign Credit Note"
  [(visible)]="rejectCustomerCAFModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div
    class="modal fade"
    id="rejectCustomerCAFModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="myModalLabel"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
            Assign Credit Note
          </h4>
        </div> -->
  <div class="modal-body">
    <div class="row">
      <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <h5>Select Staff</h5>
          <p-table [value]="rejectCAF" [(selection)]="selectStaffReject" responsiveLayout="scroll">
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-product>
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      id="selectStaffRejectsubmit"
      (click)="assignToStaff(false)"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="{{ ifApproveStatus ? ' Approve Payment' : ' Reject Payment' }}"
  [(visible)]="ApproveRejectModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="approveRejectModelClose()"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark *</label>
        <textarea
          [(ngModel)]="approveRejectRemark"
          placeholder="Remarks"
          class="form-control"
          name="remark"
        ></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="ifApproveStatus"
      (click)="statusApporeved()"
      [disabled]="!approveRejectRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button
      *ngIf="!ifApproveStatus"
      (click)="statusRejected()"
      [disabled]="!approveRejectRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>

    <button class="btn btn-default" type="button" (click)="approveRejectModelClose()">Close</button>
  </div>
</p-dialog>
<!-- <div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="ApproveRejectModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4
          class="modal-title"
          id="myModalLabel"
          style="color: #fff !important"
          *ngIf="ifApproveStatus"
        >
          Approve Payment
        </h4>
        <h4
          class="modal-title"
          id="myModalLabel"
          style="color: #fff !important"
          *ngIf="!ifApproveStatus"
        >
          Reject Payment
        </h4>
      </div> -->

<!-- </div>
  </div>
</div> -->
<!-- The Modal -->
<p-dialog
  header="Cheque Details"
  [(visible)]="paymentModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closePaymentModal()"
>
  <!-- <div class="modal fade" id="paymentModal">
  <div class="modal-dialog">
    <div class="modal-content"> -->
  <!-- Modal Header -->
  <!-- <div class="modal-header">
        <h4 class="modal-title">Cheque Details:</h4>
      </div> -->

  <!-- Modal body -->
  <div class="modal-body">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>amount</th>
          <!-- <th>branch</th> -->
          <th>chequedate</th>
          <th>chequeNo</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let cheque of chequeDetail">
          <td>{{ cheque.amount }}</td>
          <!-- <td>{{ cheque.branch }}</td> -->
          <td>{{ cheque.chequedate }}</td>
          <td>{{ cheque.chequeNo }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Modal footer -->
  <div class="modal-footer">
    <button class="btn btn-default" (click)="closePaymentModal()" type="button">Close</button>
  </div>

  <!-- </div>
  </div>
</div> -->
</p-dialog>
<p-dialog
  header="Select Customer"
  [(visible)]="selectParentCustomer"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalCloseParentCustomer()"
>
  <!-- <div class="modal fade" id="selectParentCustomer" role="dialog">
  <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Select Customer</h3>
      </div> -->
  <div class="modal-body">
    <h5>Search Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selParentSearchOption($event)"
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption != 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchParentCustomer()"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchParentCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="reset"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Customer</h5>
    <p-table
      #dt
      [(selection)]="selectedParentCust"
      [value]="customerList"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template let-customerList let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="customerList"></p-tableRadioButton>
          </td>
          <td>
            {{ customerList.name }}
            <!-- {{ customerList.lastname }} -->
          </td>
          <td>{{ customerList.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="paginate($event)"
          [first]="newFirst"
          [rows]="parentCustomerListdataitemsPerPage"
          [totalRecords]="parentCustomerListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedParentCust.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>
<!----staff Model  -->

<app-customer-details
  *ngIf="dialogId"
  (closeSelectStaff)="closeSelectStaff()"
  [custId]="custId"
></app-customer-details>

<p-dialog
  header="Select Staff"
  [(visible)]="selectApproveModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalCloseApprove()"
>
  <!-- 
<div class="modal fade" id="selectApprove" role="dialog">
  <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Select Staff</h3>
      </div> -->
  <div class="modal-body">
    <h5>Search Staff</h5>
    <div class="row">
      <!-- <div class="col-lg-3 col-md-3 m-b-10">
            <p-dropdown
              [(ngModel)]="searchOption"
              [options]="searchStaffOptionSelect"
              [filter]="true"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Search Option"
            ></p-dropdown>
          </div> -->
      <div class="col-md-3">
        <input
          id="searchStaffName"
          type="text"
          name="username"
          class="form-control"
          placeholder="Global Search Filter"
          [(ngModel)]="searchDeatil"
          (keydown.enter)="searchStaffByName()"
        />
      </div>
      <div class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchStaffByName()"
          class="btn btn-primary"
          id="searchbtn"
          type="submit"
          [disabled]="!searchDeatil"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button (click)="clearSearchForm()" class="btn btn-default" id="searchbtn" type="reset">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Staff</h5>
    <p-table #dt [(selection)]="selectedApprove" [value]="staffData" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template let-staffData let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="staffData"></p-tableRadioButton>
          </td>
          <td>
            {{ staffData.firstname }}
          </td>
          <td>{{ staffData.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="paginateStaff($event)"
          [first]="newFirst"
          [rows]="parentStaffListdataitemsPerPage"
          [totalRecords]="parentstaffListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelstaffApprove()"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedApprove.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseApprove()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>

<p-dialog
  header="Select Staff"
  [(visible)]="selectStaffModal"
  [style]="{ width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="modalCloseStaff()"
>
  <!-- <div class="modal fade" id="selectStaff" role="dialog">
    <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Select Staff</h3>
        </div> -->
  <div class="modal-body">
    <h5>Search Staff</h5>
    <div class="row">
      <!-- <div class="col-lg-3 col-md-3 m-b-10">
            <p-dropdown
              [(ngModel)]="searchOption"
              [options]="searchStaffOptionSelect"
              [filter]="true"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Search Option"
            ></p-dropdown>
          </div> -->
      <div class="col-md-3">
        <input
          id="searchStaffName"
          type="text"
          name="username"
          class="form-control"
          placeholder="Global Search Filter"
          [(ngModel)]="searchDeatil"
          (keydown.enter)="searchStaffByName()"
        />
      </div>
      <div class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchStaffByName()"
          class="btn btn-primary"
          id="searchbtn"
          type="submit"
          [disabled]="!searchDeatil"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button (click)="clearSearchForm()" class="btn btn-default" id="searchbtn" type="reset">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Staff</h5>
    <p-table #dt [(selection)]="selectedStaffCust" [value]="staffData" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
          <th>Partner Name</th>
        </tr>
      </ng-template>
      <ng-template let-staffData let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="staffData"></p-tableRadioButton>
          </td>
          <td>
            {{ staffData.firstname }}
          </td>
          <td>{{ staffData.username }}</td>
          <td>{{ staffData.partnerName }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="paginateStaff($event)"
          [first]="newFirst"
          [rows]="parentStaffListdataitemsPerPage"
          [totalRecords]="parentstaffListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelstaff()"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedStaffCust.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseStaff()" class="btn btn-danger btn-sm" type="button">Close</button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Approve Customer"
  [(visible)]="reasignpayment"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div
  class="modal fade"
  id="reasignpayment"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel" -->

  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Approve Customer
        </h4>
      </div> -->
  <div class="modal-body">
    <form [formGroup]="assignPaymentForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <h5>Select Staff</h5>
            <p-table [value]="staffDataList" [(selection)]="selectStaff" responsiveLayout="scroll">
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 3rem"></th>
                  <th>Name</th>
                  <th>Username</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-product>
                <tr>
                  <td>
                    <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                  </td>
                  <td>{{ product.fullName }}</td>
                  <td>
                    {{ product.username }}
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark</label>
          <textarea
            class="form-control"
            name="remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': assignPlansubmitted && assignPlansubmitted.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="assignPlansubmitted && assignPlansubmitted.controls.remark.errors"
          ></div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="reassignWorkflow()"
      [disabled]="!selectStaff"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>

    <button type="button" class="btn btn-default" (click)="closeReassignModel()">Close</button>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>

<div class="row" *ngIf="ifOnlinePaymentAuditList">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <!-- <h3 class="panel-title">Search Payment</h3> -->
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchPayment"
            aria-expanded="false"
            aria-controls="searchPayment"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchPayment" class="panel-collapse collapse in">
        <div class="panel-body">
          <!-- <form [formGroup]="searchPaymentFormGroup"> -->
          <div class="row">
            <div class="col-lg-3 pl-lg-3">
              <label>Search Option</label>
              <div>
                <p-dropdown
                  [options]="auditSearchOption"
                  optionValue="value"
                  optionLabel="label"
                  [filter]="true"
                  filterBy="value"
                  resetFilterOnHide="true"
                  placeholder="Search option"
                  [(ngModel)]="selectedAuditSearchOption"
                  (keydown.enter)="searchOnlineAuditPayment(true)"
                  (onChange)="selSearchOption()"
                ></p-dropdown>
              </div>
            </div>
            <div class="col-lg-3 pl-lg-3" *ngIf="selectedAuditSearchOption != 'transactionDate'">
              <label>Search Value</label>
              <input
                type="text"
                class="form-control"
                placeholder="Search Value"
                [(ngModel)]="selectedAuditSearchValue"
                (keydown.enter)="searchOnlineAuditPayment(true)"
              />
            </div>
            <div class="col-lg-3 pl-lg-3" *ngIf="selectedAuditSearchOption == 'transactionDate'">
              <label>Search Value</label>
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="selectedAuditSearchValue"
                placeholder="Enter Date"
              ></p-calendar>
            </div>
            <div class="col-lg-3 pl-lg-3">
              <label>From Date</label>
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="searchFromDate"
                placeholder="Select Start Date"
              ></p-calendar>
            </div>

            <div class="col-lg-3 pl-lg-3">
              <label>To Date</label>
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="searchTodate"
                placeholder="Select End Date"
              ></p-calendar>
            </div>
            <div class="col-lg-12 pl-lg-3">
              <div class="addUpdateBtn" style="margin-top: 25px">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="searchbtn"
                  (click)="searchOnlineAuditPayment(true)"
                  [disabled]="!selectedAuditSearchValue && !searchFromDate && !searchTodate"
                >
                  <i class="fa fa-search"></i>
                  Search Payment
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  id="searchbtn"
                  (click)="clearAuditSearch()"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
                <button class="btn btn-success" (click)="exportExcel()" id="searchbtn" type="reset">
                  <!-- [disabled]="!searchFromDate && !searchTodate" -->
                  <i class="fa fa-file-excel-o"></i>
                  Export
                </button>
              </div>
            </div>
            <!-- <div class="col-lg-3 pl-lg-3">
                            <div class="addUpdateBtn" style="margin-top: 25px">
                        
                        </div>
                        </div> -->

            <!-- <div class="addUpdateBtn">
                    <button
                        type="submit"
                        class="btn btn-primary"
                        id="searchbtn"
                        (click)="searchOnlineAuditPayment('')"
                    >
                        <i class="fa fa-search"></i>
                        Search Payment
                    </button>
                    
                    </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="row" *ngIf="ifOnlinePaymentAuditList">
      <div class="col-md-12">
        <div class="panel">
          <div class="panel-heading">
            <h3 class="panel-title">Online Payment Audit</h3>
            <div class="right">
              <button
                type="button"
                class="btn-toggle-collapse"
                data-toggle="collapse"
                data-target="#batchPayment"
                aria-expanded="false"
                aria-controls="batchPayment"
              >
                <i class="fa fa-minus-circle"></i>
              </button>
            </div>
          </div>

          <div id="onlinePaymentAudit" class="panel-collapse collapse in">
            <div class="panel-body table-responsive">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Reference No</th>

                        <th>Transaction No</th>
                        <th>Account Number</th>
                        <th>Customer Username</th>
                        <th>Payment Amount</th>
                        <th>Status</th>
                        <th>Gateway Status</th>
                        <th>Failure reason</th>
                        <th>Payment Date</th>
                        <th>Merchant Name</th>
                        <!-- <th>Assignment Status</th> -->
                        <th>Transaction Date</th>
                        <th>Payer Mobile Number</th>
                        <th>Auto Payment Initiator</th>
                        <!-- <th>View</th> -->
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let data of onlinePaymentAuditList
                            | paginate
                              : {
                                  id: 'OnlinPayAuditPageData',
                                  itemsPerPage: onlinePaymentAudititemsPerPage,
                                  currentPage: currentPageOnlinePaymentAudit,
                                  totalItems: onlinePaymentAuditotalRecords
                                };
                          index as i
                        "
                      >
                        <td>
                          {{ data.orderId }}
                        </td>
                        <td>
                          {{
                            data?.pgTransactionId && data.pgTransactionId !== "null"
                              ? data.pgTransactionId
                              : "-"
                          }}
                        </td>
                        <td>
                          {{
                            data?.accountNumber && data.accountNumber !== "null"
                              ? data.accountNumber
                              : "-"
                          }}
                        </td>
                        <td>{{ data.customerUsername }}</td>
                        <td>{{ data.payment }}</td>

                        <td>
                          <span [ngSwitch]="data.status?.toUpperCase()">
                            <span *ngSwitchCase="'PENDING'" class="badge badge-info">{{
                              data.status | titlecase
                            }}</span>
                            <span *ngSwitchCase="'INITIATE'" class="badge badge-info">{{
                              data.status | titlecase
                            }}</span>
                            <span *ngSwitchCase="'SUCCESS'" class="badge badge-success">{{
                              data.status | titlecase
                            }}</span>
                            <span *ngSwitchCase="'SUCCESSFUL'" class="badge badge-success">{{
                              data.status | titlecase
                            }}</span>
                            <span *ngSwitchCase="'FAILED'" class="badge badge-danger">{{
                              data.status | titlecase
                            }}</span>
                            <span *ngSwitchDefault class="badge badge-info">
                              {{
                                data.status == null ||
                                data.status?.toLowerCase()?.trim() === "" ||
                                data.status?.toLowerCase()?.trim() === "null"
                                  ? "-"
                                  : data.status
                              }}
                            </span>
                          </span>
                        </td>

                        <td>
                          <span [ngSwitch]="data.gatewayStatus?.toLowerCase()">
                            <span *ngSwitchCase="'success'" class="badge badge-success">{{
                              data.gatewayStatus | titlecase
                            }}</span>
                            <span *ngSwitchCase="'successful'" class="badge badge-success">{{
                              data.gatewayStatus | titlecase
                            }}</span>
                            <span *ngSwitchCase="'initiate'" class="badge badge-info">{{
                              data.gatewayStatus | titlecase
                            }}</span>
                            <span *ngSwitchCase="'pending'" class="badge badge-info">{{
                              data.gatewayStatus | titlecase
                            }}</span>
                            <span *ngSwitchCase="'failed'" class="badge badge-danger">{{
                              data.gatewayStatus | titlecase
                            }}</span>
                            <span *ngSwitchDefault class="badge badge-info">
                              {{
                                data.gatewayStatus == null ||
                                data.gatewayStatus?.toLowerCase()?.trim() === "" ||
                                data.gatewayStatus?.toLowerCase()?.trim() === "null"
                                  ? "-"
                                  : data.gatewayStatus
                              }}
                            </span>
                          </span>
                        </td>
                        <!-- <td > 
                                                <button *ngIf="data.failureDescription" class="approve-btn"
                                                type="button" title="View Failure" (click)="openFailureReason()"
                                                >
                                                <img class="icon" style="max-width: 32px" src="assets/img/eye-icon.png" />
                                                </button>
                                                <span *ngIf="!data.failureDescription">-</span>
                                            </td> -->
                        <td>
                          <span *ngIf="data.failureDescription; else noFailure">
                            <span
                              class="badge badge-warning"
                              style="cursor: pointer"
                              (click)="openFailureReason(data)"
                            >
                              View
                            </span>
                          </span>
                          <ng-template #noFailure>
                            <span style="margin-right: 5px">-</span>
                          </ng-template>
                        </td>
                        <td>{{ data.paymentDate | date: "dd-MM-YYYY HH:mm:ss" }}</td>
                        <td>{{ data.merchantName }}</td>
                        <td>{{ data.transactionDate | date: "dd-MM-YYYY HH:mm:ss" }}</td>
                        <td>
                          {{ data.payerMobileNumber }}
                        </td>
                        <td>
                          {{ data.autoPaymentInitiator }}
                        </td>
                        <td>
                          <button
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 5px;
                            "
                            title="Retry Status"
                            [disabled]="
                              data?.gatewayStatus?.toLowerCase() === 'success' ||
                              data?.gatewayStatus?.toLowerCase() === 'successful'
                            "
                            (click)="retryPayment(data.orderId)"
                          >
                            <img
                              src="assets/img/refresh.png"
                              alt="Retry Status"
                              style="width: 25px; height: 25px; margin-right: 3px"
                            />
                          </button>
                          <button
                            class="approve-btn"
                            style="border: none; background: transparent; padding: 0"
                            type="button"
                            title="Manually Settlement"
                            [disabled]="
                              !(data?.pgTransactionId == null || data?.pgTransactionId == 'NA')
                            "
                            (click)="addToWallet(data.orderId)"
                          >
                            <img
                              class="icon"
                              style="width: 25px; height: 25px; margin-right: 3px"
                              src="assets/img/19_Promise-to-Pay_Y.png"
                            />
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="pagination_Dropdown">
                    <pagination-controls
                      id="OnlinPayAuditPageData"
                      [maxSize]="10"
                      [directionLinks]="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedOnlinePayAuditList($event)"
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <!-- [(ngModel)]="onlinePaymentAudititemsPerPage" -->
                      <p-dropdown
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                        (onChange)="TotalItemPerPageAuditList($event)"
                        [(ngModel)]="onlinePaymentAudititemsPerPage"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="panel-body table-responsive" *ngIf="onlinePaymentAuditList?.length === 0">
              Details are not available
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="Failure Reason"
  [(visible)]="failureReasonDialog"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeFailureReason()"
>
  <div class="modal-body">
    <div *ngIf="selectedFailureDescription">
      <strong>{{ selectedFailureDescription }}</strong>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Add Transaction No"
  [(visible)]="transModal"
  [modal]="true"
  [style]="{ width: '30%' }"
  [draggable]="false"
  (onHide)="transactionModal()"
  [resizable]="false"
>
  <div class="form-group">
    <label style="margin-top: 10px">Transaction No: </label>
    <input
      type="text"
      name="transactionNo"
      id="transactionNo"
      [(ngModel)]="transactionNo"
      placeholder="Enter Transaction No"
      class="form-control"
    />
  </div>
  <ng-template pTemplate="footer">
    <div class="btnGroup text-center">
      <button
        class="btn btn-primary btnStyle"
        (click)="ConfirmonTransactionNumber()"
        style="margin-right: 10px"
        [disabled]="!transactionNo"
      >
        Add To Wallet
      </button>
      <button class="btn btn-danger btnStyle" (click)="transactionModal()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>
