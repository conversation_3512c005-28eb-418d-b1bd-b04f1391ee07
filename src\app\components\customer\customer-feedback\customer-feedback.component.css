.feedback-card {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.feedback-field {
  margin-bottom: 12px;
}

.feedback-field label {
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
  color: #333;
}

.feedback-message {
  margin: 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: 500;
  line-height: 1.5;
}

.feedback-excellent {
  color: #2e7d32;
  /* background-color: #e8f5e9; */
}

.feedback-good {
  color: #388e3c;
  /* background-color: #e0f2f1; */
}

.feedback-neutral {
  color: #f57f17;
  /* background-color: #fff9c4; */
}

.feedback-poor {
  color: #ef6c00;
  /* background-color: #fff3e0; */
}

.feedback-bad {
  color: #c62828;
  /* background-color: #ffebee; */
}

.feedback-none {
  color: #999;
  /* background-color: #f5f5f5; */
}

.rating-field {
  display: flex;
  align-items: center;
  gap: 10px;
}

.star-display {
  font-size: 4rem;
  display: inline-block;
  letter-spacing: 2px;
}

.star-display span {
  color: #ddd;
  transition: color 0.3s;
}

.star-display .filled {
  color: #f7b206;
}

.text-muted {
  color: #999;
}
