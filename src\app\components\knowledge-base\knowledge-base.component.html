<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchDataCountry" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="knowledgebaseName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchKnowledgeBase()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchKnowledgeBase()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearsearchKnowledgeBase()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCountry"
            aria-expanded="false"
            aria-controls="allDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Event Type</th>
                    <th>Category</th>
                    <th>Type</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let knowledgebase of knowledgebaseListData
                        | paginate
                          : {
                              id: 'knowledgebaseListData',
                              itemsPerPage: knowledgebaseitemsPerPage,
                              currentPage: currentPageKnowledgeBaseSlab,
                              totalItems: knowledgebaseTotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ knowledgebase.eventName }}</td>
                    <td>{{ knowledgebase.docType }}</td>
                    <td>{{ knowledgebase.documentFor }}</td>

                    <td class="btnAction" *ngIf="editAccess || deleteAccess">
                      <a
                        id="edit-button"
                        href="javascript:void(0)"
                        type="button"
                        *ngIf="editAccess"
                        title="Edit"
                        (click)="editKnowledgebase(knowledgebase.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        id="delete-button"
                        href="javascript:void(0)"
                        *ngIf="deleteAccess"
                        title="Delete"
                        (click)="deleteKnowledgeConfirmation(knowledgebase)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                      <a
                        id="upload-button"
                        href="javascript:void(0)"
                        title="View Document"
                        (click)="downloadDocument(knowledgebase)"
                      >
                        <img src="assets/img/pdf.png" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    id="knowledgebaseListData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedKnowledgebaseList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [(ngModel)]="knowledgebaseitemsPerPage"
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isKnowledgebaseEdit ? "Update" : "Upload" }} {{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCountry"
            aria-expanded="false"
            aria-controls="createDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isKnowledgebaseEdit">
          Sorry you have not privilege to create operation!
        </div>

        <div class="panel-body" *ngIf="createAccess || (isKnowledgebaseEdit && editAccess)">
          <form [formGroup]="knowledgeBaseFormGroup">
             <div *ngIf="mvnoId === 1">
              <label>{{ mvnoTitle }} List*</label>
              <p-dropdown
                id="mvnoId"
                [disabled]="isKnowledgebaseEdit"
                [options]="commonDropdownService.mvnoList"
                filter="true"
                filterBy="name"
                formControlName="mvnoId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select a mvno"
              ></p-dropdown>
              <div
                *ngIf="submitted && knowledgeBaseFormGroup.controls.mvnoId.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="submitted && knowledgeBaseFormGroup.controls.mvnoId.errors.required"
                  class="error text-danger"
                >
                  Mvno is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>Event Type*</label>
              <p-dropdown
                [options]="commonDropdownService.knowledgeEventList"
                optionValue="value"
                optionLabel="displayName"
                placeholder="Select a Event Type"
                formControlName="eventName"
                [ngClass]="{
                  'is-invalid': submitted && knowledgeBaseFormGroup.controls.eventName.errors
                }"
              ></p-dropdown>

              <div
                class="errorWrap text-danger"
                *ngIf="submitted && knowledgeBaseFormGroup.controls.eventName.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && knowledgeBaseFormGroup.controls.eventName.errors.required"
                >
                  Event Type is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>Select Category*</label>
              <p-dropdown
                [options]="commonDropdownService.knowledgeCategoryList"
                optionValue="value"
                optionLabel="displayName"
                placeholder="Select a Category"
                formControlName="docType"
                [ngClass]="{
                  'is-invalid': submitted && knowledgeBaseFormGroup.controls.docType.errors
                }"
              ></p-dropdown>

              <div
                class="errorWrap text-danger"
                *ngIf="submitted && knowledgeBaseFormGroup.controls.docType.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && knowledgeBaseFormGroup.controls.docType.errors.required"
                >
                  Select Category is required.
                </div>
              </div>
            </div>
            <br />
            <label>Select Type*</label>
            <div>
              <p-dropdown
                [options]="commonDropdownService.knowledgeTypeList"
                optionValue="value"
                optionLabel="displayName"
                placeholder="Select a Type"
                formControlName="documentFor"
                [ngClass]="{
                  'is-invalid': submitted && knowledgeBaseFormGroup.controls.documentFor.errors
                }"
              ></p-dropdown>
            </div>

            <div
              class="errorWrap text-danger"
              *ngIf="submitted && knowledgeBaseFormGroup.controls.documentFor.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && knowledgeBaseFormGroup.controls.documentFor.errors.required"
              >
                Select Type is required.
              </div>
            </div>
            <br />
            <div>
              <label>File*</label>
              <input
                (change)="onFileChange($event)"
                class="form-control"
                id="fileInput"
                multiple="multiple"
                placeholder="Select Attachment"
                style="padding: 2px; width: 100%"
                type="file"
              />
              <div
                *ngIf="submitted && knowledgeBaseFormGroup.controls.file.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="knowledgeBaseFormGroup.controls.file.errors.required"
                  class="error text-danger"
                >
                  File is required.
                </div>
              </div>
              <div *ngFor="let file of selectedFilePreviewd; let i = index">
                <div
                  style="
                    padding-left: 10px;
                    padding-right: 10px;
                    padding-top: 4px;
                    padding-bottom: 4px;
                    font-size: 10px;
                  "
                >
                  {{ file?.name }}
                  <button
                    type="button"
                    class="close"
                    (click)="
                      file.uniqueName
                        ? deleteConfirm(
                            viewKnowledgeBaseListData.id,
                            file.uniqueName,
                            file.name,
                            true
                          )
                        : deletSelectedFile(file?.name)
                    "
                  >
                    &times;
                  </button>
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>Remark*</label>
              <textarea
                class="form-control"
                name="remarks"
                formControlName="remarks"
                [ngClass]="{
                  'is-invalid': submitted && knowledgeBaseFormGroup.controls.remarks.errors
                }"
              ></textarea>

              <div
                *ngIf="submitted && knowledgeBaseFormGroup.controls.remarks.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="submitted && knowledgeBaseFormGroup.controls.remarks.errors.required"
                  class="error text-danger"
                >
                  Remark is required.
                </div>
              </div>
            </div>

            <div class="addUpdateBtn">
              <button
                *ngIf="!isKnowledgebaseEdit"
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditKnowledgebase('')"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ title }}
              </button>
              <button
                *ngIf="isKnowledgebaseEdit"
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditKnowledgebase(viewKnowledgeBaseListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update {{ title }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="View Document"
  [(visible)]="downloadDocumentId"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <p-table
      [value]="knowledgeBaseFileData?.fileDetails"
      [paginator]="true"
      [rows]="5"
      [rowsPerPageOptions]="[5, 10, 20]"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th>File Name</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-filedata>
        <tr>
          <td>{{ filedata?.filename }}</td>
          <td>
            <button
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
              id="download-button"
              (click)="
                downloadDoc(filedata.filename, knowledgeBaseFileData.id, filedata.uniqueName)
              "
              [disabled]="!filedata.filename"
              title="Download Document"
              class="btn btn-primary"
            >
              <img style="width: 25px; height: 25px; border-radius: 3px" src="assets/img/pdf.png" />
            </button>
            <button
              *ngIf="filedata.filename && !isWordFile(filedata.filename)"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
              id="view-button"
              [disabled]="!filedata.filename"
              title="View Document"
              class="btn btn-primary"
              (click)="
                showknowledgetDocData(
                  filedata.filename,
                  knowledgeBaseFileData.id,
                  filedata.uniqueName
                )
              "
            >
              <img
                style="width: 25px; height: 25px; border-radius: 3px"
                src="assets/img/eye-icon.png"
              />
            </button>
            <button
              style="border-radius: 5px; padding: 3px 8px; line-height: 1.5; margin-left: 5px"
              class="btn btn-primary"
              [disabled]="!filedata.filename"
              title="Remove File"
              id="delete-button"
              (click)="
                deleteConfirm(knowledgeBaseFileData.id, filedata.uniqueName, filedata.filename)
              "
            >
              <img src="assets/img/ioc02.jpg" />
            </button>
          </td>
        </tr>
      </ng-template>
    </p-table>
    <div class="modal-footer">
      <button type="button" class="btn btn-danger btn-sm" (click)="closeDownloadDocumentId()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Document Preview"
  [(visible)]="documentPreview"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeDocumentPreview()"
>
  <div class="modal-body" style="height: 600px; text-align: center">
    <ng-container [ngSwitch]="previewType">
      <iframe *ngSwitchCase="'pdf'" [src]="previewUrl" width="100%" height="100%"></iframe>

      <img
        *ngSwitchCase="'png'"
        [src]="previewUrl"
        alt="Image"
        style="max-width: 100%; max-height: 100%"
      />
      <img
        *ngSwitchCase="'jpg'"
        [src]="previewUrl"
        alt="Image"
        style="max-width: 100%; max-height: 100%"
      />
      <img
        *ngSwitchCase="'jpeg'"
        [src]="previewUrl"
        alt="Image"
        style="max-width: 100%; max-height: 100%"
      />

      <video *ngSwitchCase="'mp4'" [src]="previewUrl" controls width="100%" height="100%"></video>

      <div *ngSwitchDefault>
        <p>Unsupported file format for preview.</p>
      </div>
    </ng-container>
  </div>

  <div class="modal-footer">
    <button class="btn btn-default" (click)="closeDocumentPreview()" type="button">Close</button>
  </div>
</p-dialog>
