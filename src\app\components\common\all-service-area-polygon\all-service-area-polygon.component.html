<p-dialog
  header="Covered Area"
  [(visible)]="isAllPolygoneModelShow"
  [style]="{ height: '90%', width: '80%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="hidePolygonModel()"
>
  <div class="modal-body" style="height: 85% !important; overflow: auto !important">
    <div class="row">
      <div class="col-lg-7 col-md-6">
        <input
          type="searchLocationname"
          class="form-control"
          id="searchLocationname"
          placeholder="Enter Location Name"
          [(ngModel)]="location"
          ngx-google-places-autocomplete
          (onAddressChange)="handleAddressChange($event)"
        />
        <br />
      </div>
      <br />
    </div>
    <div class="row">
      <div class="col-lg-12 col-md-12">
        <agm-map
          [latitude]="lat"
          [longitude]="lng"
          [zoom]="zoom"
          (mapReady)="onAllPolygonMapReady($event)"
          [mapTypeId]="'hybrid'"
        >
          <agm-marker
            *ngFor="let m of markers; let i = index"
            [latitude]="m.lat"
            [longitude]="m.lng"
            [label]="m.label"
            [markerDraggable]="m.draggable"
          >
            >
          </agm-marker>
        </agm-map>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button type="button" class="btn btn-default" (click)="hidePolygonModel()">Close</button>
    </div>
  </div>
</p-dialog>
