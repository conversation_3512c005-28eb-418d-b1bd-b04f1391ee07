<div class="panel">
  <div class="panel-heading">
    <div class="displayflex">
      <button
        (click)="backButton.emit(custData.id)"
        class="btn btn-secondary backbtn"
        data-placement="bottom"
        data-toggle="tooltip"
        title="Go to Customer Details"
        type="button"
      >
        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
      </button>
      <h3 class="panel-title">
        {{ custData.title }}
        {{ custData.custname }} Charge Details
      </h3>
    </div>
    <div class="right">
      <button class="btn refreshbtn" type="reset" (click)="getCustChargeDetails('', custid)">
        <i class="fa fa-refresh"></i>
      </button>
      <button
        aria-controls="chargeDetailsCust"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#chargeDetailsCust"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div>
  <div class="panel-collapse collapse in" id="chargeDetailsCust">
    <button
      *ngIf="addChargeAccess"
      type="submit"
      class="btn btn-primary statusbtn"
      title="Add Charge"
      style="
        background-color: #f7b206 !important;
        font-size: 16px;
        padding: 3px 12px;
        margin: 10px 25px;
        border: none;
        color: white;
        font-weight: 400;
        border-radius: 14px;
      "
      (click)="getServiceSerialNumber()"
    >
      Add Charge
    </button>

    <div class="panel-collapse collapse in">
      <div class="panel-body table-responsive" *ngIf="ChargeCustList.length !== 0">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <table class="table">
              <thead>
                <tr>
                  <!-- <th>Connection No.</th> -->
                  <th>Charge Name</th>
                  <th>Charge Amount</th>
                  <!-- <th>Charge Type</th> -->
                  <th>Static IP</th>
                  <!-- <th>Billing Cycle</th> -->
                  <th>Plan Name</th>
                  <th>Validity</th>
                  <th>New Price</th>
                  <th>Installement Amount</th>
                  <th>Start Date</th>
                  <th>Expiry Date</th>
                  <th>Status</th>
                  <th>Installment Frequency</th>
                  <th>Next Installment Date</th>
                  <th>Last Installment Date</th>
                  <th>Installment Details</th>
                  <th>Action</th>
                  <!-- <th>Delete</th> -->
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let charge of ChargeCustList
                      | paginate
                        : {
                            id: 'chargeIdcustomer',
                            itemsPerPage: itemsCustChargePerPage,
                            currentPage: currentCustChargePageSlab,
                            totalItems: totalCustChargeRecords
                          };
                    index as i
                  "
                >
                  <!-- <td>{{ charge.connection_no }}</td> -->
                  <td>{{ charge.charge_name }}</td>
                  <td>{{ charge.actualprice | currency: currency }}</td>
                  <!-- <td>{{ charge.type }}</td> -->
                  <td *ngIf="charge.staticIPAdrress">
                    {{ charge.staticIPAdrress }}
                  </td>
                  <td *ngIf="!charge.staticIPAdrress">-</td>
                  <!-- <td>
                    <span *ngIf="charge.billingCycle">
                      {{ charge.billingCycle }}
                    </span>
                    <span *ngIf="!charge.billingCycle">0</span>
                  </td> -->
                  <td>
                    <span *ngFor="let list of planChageData; index as j">
                      <span *ngIf="i === j">{{ list.name }}</span>
                    </span>
                  </td>
                  <td>{{ charge.validity }} {{ charge.unitsOfValidity }}</td>
                  <td>{{ charge.price | currency: currency }}</td>
                  <td>{{ charge.amountPerInstallment }}</td>
                  <td>{{ charge.startdate | date: "dd-MM-yyyy HH:mm" }}</td>
                  <td>{{ charge.enddate | date: "dd-MM-yyyy HH:mm" }}</td>
                  <td>
                    <span *ngIf="charge.isDeleted == false">
                      <span class="badge badge-success">Active</span>
                    </span>
                    <span *ngIf="charge.isDeleted == true">
                      <span class="badge badge-danger">Inactive</span>
                    </span>
                  </td>
                  <td>
                    {{ charge.installmentFrequency }}
                  </td>
                  <td>
                    {{ charge.nextInstallmentDate | date: "dd-MM-yyyy HH:mm" }}
                  </td>
                  <td>
                    {{ charge.lastInstallmentDate | date: "dd-MM-yyyy HH:mm" }}
                  </td>
                  <td>
                    <ng-container
                      *ngIf="charge.installmentNo && charge.totalInstallments; else elseBlock"
                    >
                      {{ charge.installmentNo }} out of {{ charge.totalInstallments }}
                    </ng-container>
                    <ng-template #elseBlock> - </ng-template>
                  </td>
                  <td>
                    <a
                      *ngIf="charge.staticIPAdrress"
                      (click)="editStaticIP(charge)"
                      href="javascript:void(0)"
                      id="editbutton"
                      title="Edit Static IP"
                      type="button"
                    >
                      <img src="assets/img/ioc01.jpg" />
                    </a>
                  </td>
                  <!-- <td> -->
                  <!-- {{ this.todayDate }} > {{ charge.startdate }} &&
                    {{ this.todayDate }} > {{ charge.enddate }} -->
                  <!-- <button
                      type="button"
                      class="approve-btn"
                      [disabled]="
                        (this.todayDate > charge.startdate && this.todayDate > charge.enddate) ||
                        charge.isDeleted == true
                      "
                      (click)="deleteConfirmCharge(charge.id, charge.startdate, charge.enddate)"
                    >
                      <img src="assets/img/ioc02.jpg" width="32" height="32" />
                    </button>
                  </td> -->
                </tr>
              </tbody>
            </table>
            <br />
            <div class="pagination_Dropdown">
              <pagination-controls
                id="chargeIdcustomer"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedList($event)"
              ></pagination-controls>
              <div id="itemPerPageDropdown">
                <p-dropdown
                  [options]="pageLimitOptions"
                  optionLabel="value"
                  optionValue="value"
                  (onChange)="TotalItemCustChargePerPage($event)"
                ></p-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-body table-responsive" *ngIf="ChargeCustList.length === 0">
        Details are not available.
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="Create Charge"
  [(visible)]="createChargeVisible"
  [modal]="true"
  [style]="{ width: '90vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
  (onHide)="closeChargeModal()"
>
  <div class="modal-body">
    <div class="row">
      <div class="panel-body table-responsive">
        <!--    charge Details   -->
        <fieldset>
          <legend>Charge Details</legend>
          <div class="boxWhite">
            <div class="row">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Billable To</label>
                <br />
                <p-dropdown
                  [disabled]="true"
                  [options]="billableCusList"
                  [showClear]="true"
                  filter="true"
                  filterBy="name"
                  [(ngModel)]="billableCustomerId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a Billable"
                  styleClass="disableDropdown"
                ></p-dropdown>
                <button
                  type="button"
                  (click)="modalOpenParentCustomer()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                  (click)="removeSelParentCust()"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Payment Owner</label>
                <br />
                <!-- <p-dropdown
                      [options]="staffDataList"
                      optionLabel="displayLabel"
                      optionValue="id"
                      filterBy="displayLabel"
                      placeholder="Select a Payment Owner"
                      filter="true"
                      resetFilterOnHide="true"
                      [(ngModel)]="paymentOwnerId"
                    ></p-dropdown> -->
                <p-dropdown
                  [disabled]="true"
                  [options]="staffSelectList"
                  optionLabel="name"
                  optionValue="id"
                  filterBy="name"
                  placeholder="Select a staff"
                  [filter]="true"
                  [(ngModel)]="paymentOwnerId"
                  [showClear]="true"
                  styleClass="disableDropdown"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>

                <button
                  type="button"
                  (click)="modalOpenSelectStaff('paymentCharge')"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
                <button
                  [disabled]="paymentOwnerId == null"
                  type="button"
                  (click)="removeSelectStaff()"
                  class="btn btn-danger"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-trash"></i>
                </button>
                <div
                  *ngIf="chargesubmitted && chargeGroupForm.controls.paymentOwnerId.errors"
                  class="errorWrap text-danger"
                >
                  <div class="error text-danger">Payment Owner is required</div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" style="padding: 27px">
                <div class="form-group form-check inputcheckboxCenter">
                  <input
                    [(ngModel)]="isInstallemnt"
                    type="checkbox"
                    class="inputcheckbox"
                    (change)="onChangeInstallmentType()"
                    [disabled]="isDisableConn"
                  />
                  <label style="margin-left: 1rem; margin-bottom: 0"
                    >Do you want Installment?
                  </label>
                </div>
              </div>
            </div>
            <br />
            <table [formGroup]="chargeGroupForm" style="width: 100%; margin-top: 10px">
              <tr>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <p-dropdown
                    *ngIf="isShowConnection"
                    (onChange)="filterPlan($event)"
                    [options]="custServiceData"
                    optionValue="connection_no"
                    optionLabel="connection_no"
                    filter="true"
                    filterBy="connection_no"
                    placeholder="Select a Connection No. *"
                    formControlName="connection_no"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.connection_no.errors
                    }"
                  ></p-dropdown>
                  <p-dropdown
                    *ngIf="!isShowConnection"
                    (onChange)="filterPlan($event)"
                    [options]="serviceSerialNumbers"
                    optionValue="connection_no"
                    optionLabel="serialNumber"
                    filter="true"
                    filterBy="connection_no"
                    placeholder="Select a Serial No. *"
                    formControlName="connection_no"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.connection_no.errors
                    }"
                  ></p-dropdown>
                  <div></div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <p-dropdown
                    [options]="commondropdownService.chargeByTypeData"
                    optionValue="id"
                    optionLabel="name"
                    filter="true"
                    filterBy="name"
                    placeholder="Select a Charge"
                    formControlName="chargeid"
                    (onChange)="selectcharge($event)"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.chargeid.errors
                    }"
                  ></p-dropdown>
                  <div></div>
                </td>

                <td style="text-align: center; padding: 0 10px 0 0">
                  <input
                    class="form-control"
                    type="number"
                    min="0"
                    placeholder="Enter Actual Price"
                    name="actualprice"
                    id="actualprice        "
                    formControlName="actualprice"
                    *ngIf="selectchargeValueShow"
                    disabled
                  />

                  <input
                    class="form-control"
                    type="number"
                    min="0"
                    *ngIf="!selectchargeValueShow"
                    placeholder="Enter Actual Price"
                    name="actualprice"
                    id="actualprice        "
                    formControlName="actualprice"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.actualprice.errors
                    }"
                  />
                </td>

                <td
                  style="text-align: center; padding: 0 10px 0 0"
                  *ngIf="isStaticIPAdrress(chargeGroupForm.value.chargeid)"
                >
                  <input
                    class="form-control"
                    type="text"
                    placeholder="Enter Static IP"
                    name="staticIPAdrress"
                    id="staticIPAdrress"
                    formControlName="staticIPAdrress"
                  />
                </td>

                <!-- <td style="text-align: center; padding: 0 10px 0 0">
                      <p-dropdown
                        [options]="chargeType"
                        optionValue="label"
                        optionLabel="label"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Type"
                        formControlName="type"
                        (onChange)="selectTypecharge($event)"
                        [ngClass]="{
                          'is-invalid': chargesubmitted && chargeGroupForm.controls.type.errors
                        }"
                      ></p-dropdown>
                      <div></div>
                    </td> -->

                <!-- <td
                      style="text-align: center; padding: 0 10px 0 0"
                      *ngIf="chargeGroupForm.value.type == 'Recurring'"
                    >
                      <p-dropdown
                        [options]="billingCycle"
                        optionValue="label"
                        optionLabel="label"
                        filter="true"
                        filterBy="label"
                        placeholder="Select a Billing Cycle"
                        formControlName="billingCycle"
                        (onChange)="onBillingCycleChange($event)"
                        [ngClass]="{
                          'is-invalid': submitted && chargefromgroup.controls.billingCycle.errors
                        }"
                      ></p-dropdown>
                      <div></div>
                    </td> -->

                <td style="text-align: center; padding: 0 10px 0 0">
                  <p-dropdown
                    [options]="planByService"
                    optionValue="planId"
                    optionLabel="planName"
                    filter="true"
                    filterBy="name"
                    placeholder="Select a Plan *"
                    formControlName="planid"
                    (onChange)="getPlanValidityForChagre($event)"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.planid.errors
                    }"
                  ></p-dropdown>
                  <div></div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <!-- <div style="display: flex">
                        <div style="width: 40%">
                          <input
                            id="validity"
                            type="number"
                            min="1"
                            class="form-control"
                            placeholder="Enter Validity"
                            formControlName="validity"
                            readonly
                          />
                        </div>
                        <div style="width: 60%; height: 34px">
                          <p-dropdown
                            [options]="commondropdownService.validityUnitData"
                            optionValue="label"
                            optionLabel="label"
                            filter="true"
                            filterBy="label"
                            placeholder="Select a Unit"
                            formControlName="unitsOfValidity"
                            [disabled]="true"
                          ></p-dropdown>
                        </div>
                      </div> -->
                  <p-calendar
                    dateFormat="dd/mm/yy"
                    [showIcon]="true"
                    [showButtonBar]="true"
                    [hideOnDateTimeSelect]="true"
                    placeholder="Service Expiry Date"
                    formControlName="expiry"
                    [minDate]="dateTime"
                  ></p-calendar>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <input
                    class="form-control"
                    type="number"
                    min="0"
                    placeholder="Enter New Price"
                    name="price"
                    id="price"
                    formControlName="price"
                    [ngClass]="{
                      'is-invalid': chargesubmitted && chargeGroupForm.controls.price.errors
                    }"
                  />
                </td>
                <td style="text-align: left; padding: 0 10px 0 0">
                  <input
                    disabled
                    class="form-control"
                    formControlName="discount"
                    id="discount"
                    min="0"
                    name="discount"
                    placeholder="Enter discount"
                    type="number"
                  />
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <p-dropdown
                    [options]="commondropdownService.installmentTypeData"
                    optionValue="value"
                    optionLabel="text"
                    filter="true"
                    filterBy="text"
                    placeholder="Select a Installment Type"
                    formControlName="installmentFrequency"
                    [showClear]="true"
                    [disabled]="!isInstallemnt"
                    [ngClass]="{
                      'is-invalid':
                        chargesubmitted && chargeGroupForm.controls.installmentFrequency.errors
                    }"
                  ></p-dropdown>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <p-dropdown
                    [options]="totalInstallments"
                    optionValue="value"
                    optionLabel="text"
                    filter="true"
                    filterBy="text"
                    placeholder="Installments"
                    formControlName="totalInstallments"
                    [showClear]="true"
                    [disabled]="!isInstallemnt"
                    [ngClass]="{
                      'is-invalid':
                        chargesubmitted && chargeGroupForm.controls.totalInstallments.errors
                    }"
                  ></p-dropdown>
                  <!-- <input
                    type="number"
                    [readonly]="!isInstallemnt"
                    formControlName="totalInstallments"
                    class="form-control"
                    placeholder="Enter Installment No"
                    [ngClass]="{
                      'is-invalid':
                        chargesubmitted && chargeGroupForm.controls.totalInstallments.errors
                    }"
                  /> -->
                </td>
                <td style="text-align: center; width: 5%">
                  <button
                    style="object-fit: cover; padding: 5px 8px"
                    class="btn btn-primary"
                    (click)="onAddoverChargeListField()"
                  >
                    <i class="fa fa-plus-square" aria-hidden="true"></i>
                    Add
                  </button>
                </td>
              </tr>

              <tr>
                <td>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.connection_no.errors"
                  >
                    <div class="error text-danger">Connection No. is required.</div>
                  </div>
                </td>
                <td>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.chargeid.errors"
                  >
                    <div class="error text-danger">Charge is required.</div>
                  </div>
                </td>
                <td>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.actualprice.errors"
                  >
                    <div class="error text-danger">Charge Amount is required.</div>
                  </div>
                </td>
                <!-- <td
                      *ngIf="chargeGroupForm.value.type == 'Recurring'"
                      style="text-align: center; padding: 0 10px 0 0"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="chargesubmitted && chargeGroupForm.controls.billingCycle.errors"
                      >
                        <div class="error text-danger">Billing Cycle is required.</div>
                      </div>
                    </td> -->
                <td *ngIf="isStaticIPAdrress(chargeGroupForm.value.chargeid)">
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.staticIPAdrress.errors"
                  >
                    <div class="error text-danger">Static IP Address is required.</div>
                  </div>
                </td>
                <td>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.planid.errors"
                  >
                    <div class="error text-danger">Plan is required.</div>
                  </div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.expiry.errors"
                  >
                    <div class="error text-danger">Expiry Date is required.</div>
                  </div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.price.errors"
                  >
                    <div class="error text-danger">New Price is required.</div>
                  </div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0"></td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.installmentFrequency.errors"
                  >
                    <div class="error text-danger" *ngIf="isInstallemnt">
                      Install Type is required.
                    </div>
                  </div>
                </td>
                <td style="text-align: center; padding: 0 10px 0 0">
                  <div
                    class="errorWrap text-danger"
                    *ngIf="chargesubmitted && chargeGroupForm.controls.totalInstallments.errors"
                  >
                    <div class="error text-danger" *ngIf="isInstallemnt">
                      Installment No is required.
                    </div>
                  </div>
                </td>
                <td style="text-align: center; width: 5%"></td>
              </tr>
            </table>

            <div
              class="errorWrap text-danger"
              *ngIf="this.chargeGroupForm.value.price < this.chargeGroupForm.value.actualprice"
            >
              <div class="error text-danger">
                New Price must not be less than the actual charge price
              </div>
            </div>

            <table class="table coa-table" style="margin-top: 3rem">
              <thead>
                <tr>
                  <th style="text-align: center">Connection no.</th>
                  <th style="text-align: center">Charge Name</th>
                  <th style="text-align: center">Charge Amount</th>
                  <!-- <th style="text-align: center">Charge Type</th> -->
                  <!-- <th style="text-align: center">Billing Cycle</th> -->
                  <th style="text-align: center">Static IP</th>
                  <th style="text-align: center">Plan Name</th>
                  <!-- <th style="text-align: center">Plan Validity</th> -->
                  <th style="text-align: center">Service Expiry</th>
                  <th style="text-align: center">New Price</th>
                  <th style="text-align: center">Discount (%)</th>
                  <th style="text-align: center">Installment Type</th>
                  <th style="text-align: center">Installment No</th>
                  <th style="text-align: right; width: 5%; padding: 8px">Delete</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let row of overChargeListFromArray.controls
                      | paginate
                        : {
                            id: 'overChargeListFromArrayData',
                            itemsPerPage: overChargeListItemPerPage,
                            currentPage: currentPageoverChargeList,
                            totalItems: overChargeListtotalRecords
                          };
                    let index = index
                  "
                >
                  <td>
                    <p-dropdown
                      [options]="custServiceData"
                      optionValue="connection_no"
                      optionLabel="connection_no"
                      filter="true"
                      filterBy="connection_no"
                      placeholder="Select a Connection No."
                      [formControl]="row.get('connection_no')"
                      [disabled]="true"
                    ></p-dropdown>
                    <div></div>
                  </td>
                  <td style="padding-left: 8px">
                    <p-dropdown
                      [options]="commondropdownService.chargeByTypeData"
                      optionValue="id"
                      optionLabel="name"
                      filter="true"
                      filterBy="name"
                      [formControl]="row.get('chargeid')"
                      [disabled]="true"
                    ></p-dropdown>
                    <div></div>
                  </td>
                  <td>
                    <input
                      class="form-control"
                      type="number"
                      min="0"
                      placeholder="Enter Actual Price"
                      name="actualprice"
                      id="actualprice"
                      [formControl]="row.get('actualprice')"
                      readonly
                    />
                  </td>
                  <!-- <td style="padding-left: 8px">
                        <p-dropdown
                          [options]="chargeType"
                          optionValue="label"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          [formControl]="row.get('type')"
                          [disabled]="true"
                        ></p-dropdown>
                        <div></div>
                      </td> -->

                  <!-- <td>
                        <p-dropdown
                          [options]="billingCycle"
                          optionValue="label"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select a Billing Cycle"
                          [formControl]="row.get('billingCycle')"
                          [disabled]="true"
                          [ngClass]="{
                            'is-invalid': submitted && chargefromgroup.controls.billingCycle.errors
                          }"
                        ></p-dropdown>
                        <div></div>
                      </td> -->

                  <td>
                    <input
                      *ngIf="isStaticIPAdrress(row.value.chargeid)"
                      class="form-control"
                      type="text"
                      placeholder="Enter Static IP Address"
                      name="staticIPAdrress"
                      id="staticIPAdrress"
                      [formControl]="row.get('staticIPAdrress')"
                      readonly
                    />
                    <div *ngIf="!isStaticIPAdrress(row.value.chargeid)">-</div>
                  </td>
                  <td>
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Plan Name"
                      name="planName"
                      id="planName"
                      [formControl]="row.get('planName')"
                      readonly
                    />
                    <!-- <select
                          class="form-control"
                          style="width: 100%"
                          name="planId"
                          id="planId"
                          [formControl]="row.get('planid')"
                          disabled
                        >
                          <option value="">Select Plan</option>
                          <option
                            *ngFor="let item of commondropdownService.postpaidplanData"
                            value="{{ item.id }}"
                          >
                            {{ item.name }}
                          </option>
                        </select> -->
                  </td>
                  <td>
                    <div style="display: flex">
                      <!-- <div style="width: 40%">
                            <input
                              id="validity"
                              type="number"
                              min="1"
                              class="form-control"
                              placeholder="Enter Validity"
                              [formControl]="row.get('validity')"
                              readonly
                            />
                          </div> -->
                      <!-- <div style="width: 60%; height: 34px">
                            <p-dropdown
                              [options]="commondropdownService.validityUnitData"
                              optionValue="label"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Unit"
                              [formControl]="row.get('unitsOfValidity')"
                              [disabled]="true"
                            ></p-dropdown>
                          </div> -->
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Service Expiry"
                        name="expiry"
                        id="expiry"
                        [formControl]="row.get('expiryDate')"
                        readonly
                      />
                    </div>
                  </td>
                  <td>
                    <input
                      class="form-control"
                      type="number"
                      min="0"
                      placeholder="Enter Price"
                      name="price"
                      id="price"
                      [formControl]="row.get('price')"
                      readonly
                    />
                  </td>
                  <td>
                    <input
                      disabled
                      class="form-control"
                      [formControl]="row.get('discount')"
                      id="discount"
                      min="0"
                      name="discount"
                      placeholder="Enter discount"
                      type="number"
                    />
                  </td>
                  <td>
                    <p-dropdown
                      [options]="commondropdownService.installmentTypeData"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      placeholder="Select a Installment Type"
                      [formControl]="row.get('installmentFrequency')"
                      [showClear]="true"
                      [disabled]="true"
                    ></p-dropdown>
                  </td>
                  <td>
                    <input
                      class="form-control"
                      type="number"
                      min="0"
                      placeholder="Enter Installement No"
                      name="totalInstallments"
                      id="totalInstallments"
                      [formControl]="row.get('totalInstallments')"
                      disabled
                    />
                  </td>
                  <td style="text-align: right">
                    <a
                      id="deleteAtt"
                      href="javascript:void(0)"
                      (click)="deleteConfirmonChargeField(index, 'Charge')"
                    >
                      <img src="assets/img/ioc02.jpg" />
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>

            <div class="row">
              <div class="col-md-12">
                <pagination-controls
                  id="overChargeListFromArrayData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedOverChargeList($event)"
                ></pagination-controls>
              </div>
            </div>
            <br />
          </div>
        </fieldset>
      </div>
    </div>
    <!-- </div> -->
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        type="button"
        class="btn btn-primary"
        data-title="Update Discount Details"
        (click)="saveChargeData()"
        [disabled]="overChargeListFromArray.controls.length == 0"
      >
        Save
      </button>
    </div>
    <div class="addUpdateBtn" style="margin-left: 1.5rem">
      <button type="button" class="btn btn-danger btn-sm" (click)="closeChargeModal()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<div class="modal fade" id="deleteChargeId" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Delete Charge</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="panel-body table-responsive">
            <label>To Date</label>
            <div id="deletecharge">
              <p-calendar
                dateFormat="dd/mm/yy"
                [showIcon]="true"
                [showButtonBar]="true"
                [hideOnDateTimeSelect]="true"
                placeholder="Enter To Date"
                [(ngModel)]="endData"
                [minDate]="dateTime"
              ></p-calendar>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn">
          <button
            type="button"
            class="btn btn-primary"
            data-title="Delete Charge"
            (click)="deletechargeData('')"
            [disabled]="!endData"
            data-dismiss="modal"
          >
            Delete
          </button>
        </div>
        <div class="addUpdateBtn" style="margin-left: 1.5rem">
          <button
            type="button"
            class="btn btn-danger btn-sm"
            #closebutton
            data-dismiss="modal"
            (click)="deletecloseModel()"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Select Customer"
  [(visible)]="displaySelectParentCustomer"
  [style]="{ width: '60%' }"
  [modal]="true"
  [baseZIndex]="1000000"
>
  <ng-template pTemplate="content">
    <h5>Search Parent Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          filter="true"
          [filterBy]="'label'"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
          (onChange)="selParentSearchOption($event)"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption !== 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.CustomerStatusValue"
          filter="true"
          [filterBy]="'text'"
          optionValue="value"
          optionLabel="text"
          placeholder="Select a Status"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'serviceareaName'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.serviceAreaList"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Servicearea"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'plan'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.postpaidplanData"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Plan"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button (click)="searchParentCustomer()" class="btn btn-primary" type="button">
          <i class="fa fa-search"></i>
          Search
        </button>
        <button (click)="clearSearchParentCustomer()" class="btn btn-default" type="button">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Parent Customer</h5>
    <div style="overflow-x: auto">
      <p-table #dt [value]="prepaidParentCustomerList" [(selection)]="selectedParentCust">
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 5rem"></th>
            <th>Name</th>
            <th>User Name</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
          <tr>
            <td>
              <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
            </td>
            <td>{{ prepaidParentCustomerList.name }}</td>
            <td>{{ prepaidParentCustomerList.username }}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="summary">
          <p-paginator
            [rows]="parentCustomerListdataitemsPerPage"
            [totalRecords]="parentCustomerListdatatotalRecords"
            (onPageChange)="paginate($event)"
            [first]="newFirst"
          ></p-paginator>
        </ng-template>
      </p-table>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        [disabled]="selectedParentCust.length === 0"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </ng-template>
</p-dialog>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="updateStaticIP"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Update Charge</h4>
      </div>
      <div class="modal-body" *ngIf="staticIPCharge !== null">
        <!-- <div class="row mb-15">
          <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
            <label>Connection No.</label>
          </div>
          <div class="col-lg-9 col-md-8 col-sm-6 col-xs-12">
            <label>{{ staticIPCharge.connection_no }}</label>
            <br/>
          </div>
        </div> -->
        <div class="row mb-15">
          <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
            <label>Charge Name</label>
          </div>
          <div class="col-lg-9 col-md-8 col-sm-6 col-xs-12">
            <label>{{ staticIPCharge.charge_name }}</label>
            <br />
          </div>
        </div>
        <div class="row mb-15">
          <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
            <label>Charge Start Date</label>
          </div>
          <div class="col-lg-9 col-md-8 col-sm-6 col-xs-12">
            <label>{{ staticIPCharge.startdate | date: "dd-MM-yyyy HH:mm" }}</label>
            <br />
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
            <label>Static IP</label>
          </div>
          <div class="col-lg-9 col-md-8 col-sm-6 col-xs-12">
            <input
              class="form-control"
              type="text"
              placeholder="Enter Static IP"
              name="staticIPAdrress"
              id="staticIPAdrress"
              [(ngModel)]="staticIPCharge.staticIPAdrress"
            />
            <br />
          </div>
        </div>
        <div class="row mb-15">
          <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
            <label>Charge Expiry Date</label>
          </div>
          <div class="col-lg-9 col-md-8 col-sm-6 col-xs-12">
            <p-calendar
              dateFormat="dd-mm-yy"
              [showIcon]="true"
              [showButtonBar]="true"
              [hideOnDateTimeSelect]="true"
              placeholder="Service Expiry Date"
              [minDate]="staticIPCharge.startdate"
              [maxDate]="staticIPCharge.enddate"
              [(ngModel)]="staticIPExpiryDate"
            ></p-calendar>
            <br />
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button (click)="updateStaticIPAddress()" class="btn btn-primary" id="submit" type="submit">
          <i class="fa fa-check-circle"></i>
          Update
        </button>
        <button class="btn btn-danger" id="searchbtn" type="reset" data-dismiss="modal">
          <i class="fa fa-refresh"></i>
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<app-staff-select-model
  [selectedStaffCust]="selectedStaff"
  [isPaymentOwnerType]="true"
  *ngIf="showSelectStaffModel"
  (selectedStaffChange)="selectedStaffChange($event)"
  (closeSelectStaff)="closeSelectStaff()"
></app-staff-select-model>
