<div class="childComponent">
  <div class="row">
    <div class="col-md-12">
      <div class="panel top">
        <div class="panel-heading">
          <h3 class="panel-title">Staff Details</h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#searchstaff"
              aria-expanded="false"
              aria-controls="searchstaff"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div id="searchstaff" class="panel-collapse collapse in">
          <div class="panel-body no-padding panel-udata">
            <div class="col-md-3 pcol">
              <div class="dbox">
                <a class="curson_pointer" (click)="staffDetialsOpen(this.openStaffID)">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Staff Details</h5>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcol" *ngIf="receiptAccess">
              <div class="dbox">
                <a class="curson_pointer" (click)="openStaffStaffReceipt()">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Staff Receipt Management</h5>
                </a>
              </div>
            </div>

            <div class="col-md-3 pcol" *ngIf="profileWalletAccess">
              <div class="dbox">
                <a class="curson_pointer" (click)="openStaffWallet()">
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Payment Received</h5>
                </a>
              </div>
            </div>
            <div class="col-md-3 pcol" *ngIf="profileChangePassAccess">
              <div class="dbox">
                <a
                  class="curson_pointer"
                  data-title="Change Password"
                  title="Change Password"
                  data-target="#changePasswordModal"
                  data-toggle="modal"
                  data-backdrop="static"
                  data-keyboard="false"
                  (click)="getCustomerDataForPasswordChange(satffUserData)"
                >
                  <img src="../../../assets/img/i01.png" style="width: 32px" />
                  <h5>Change Password</h5>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="isStaffPersonalData">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div class="panel-heading">
          <h3 class="panel-title">
            <button
              (click)="navigateToRadiusStaff()"
              class="btn btn-secondary backbtn"
              title="Go to Customer List"
              type="button"
            >
              <i
                class="fa fa-arrow-circle-left"
                style="color: #f7b206 !important; font-size: 28px"
              ></i>
            </button>
            {{ satffUserData.firstname }} Staff
          </h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#staffdeatilsview"
              aria-expanded="false"
              aria-controls="staffdeatilsview"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>

        <div id="staffdeatilsview" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <!--    Basic Details    -->
            <fieldset style="margin-top: 0rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup"
                    style="display: flex; justify-content: center; align-items: center"
                  >
                    <img
                      src="../assets/img/user.png"
                      class="staggImg"
                      *ngIf="!satffUserData.profileImage"
                    />
                    <img
                      [src]="staffImg"
                      alt="Teacher"
                      class="staggImg"
                      *ngIf="satffUserData.profileImage"
                    />
                  </div>

                  <div class="col-lg-9 col-md-9 col-sm-6 col-xs-12 dataGroup">
                    <div class="row">
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Firstname :</label>
                        <span>{{ satffUserData.firstname }}</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl"> Lastname :</label>
                        <span>{{ satffUserData.lastname }}</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Username :</label>
                        <span>{{ satffUserData.username }}</span>
                      </div>

                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Email :</label>
                        <span>{{ satffUserData.email }}</span>
                      </div>

                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Mobile :</label>
                        <span>({{ satffUserData.countryCode }}) {{ satffUserData.phone }}</span>
                      </div>

                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Roles :</label>
                        <span *ngFor="let list of satffUserData.roleName; index as j">
                          {{ list }},&nbsp;
                        </span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Service area :</label>
                        <span
                          class="HoverEffect"
                          data-target="#serviceareaModal"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          title="Go To service Area List"
                        >
                          Click here
                        </span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Business Unit :</label>
                        <span
                          class="HoverEffect"
                          data-target="#bussinessModal"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          title="Go To Business List"
                        >
                          Click here
                        </span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Team :</label>

                        <span
                          *ngIf="
                            this.satffUserData.teamNameList != null &&
                            this.satffUserData.teamNameList.length > 0
                          "
                          class="HoverEffect"
                          data-target="#teamModal"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          title="Go To Team List"
                        >
                          Click here
                        </span>
                        <span
                          *ngIf="
                            this.satffUserData.teamNameList != null &&
                            this.satffUserData.teamNameList.length == 0
                          "
                        >
                          {{ "-" }}
                        </span>
                        <!-- <span *ngFor="let list of satffUserData.businessUnitNameList; index as j">
                        {{ list }},&nbsp;
                      </span> -->
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Parent Staff :</label>
                        <span *ngIf="satffUserData.parentstaffname">{{
                          satffUserData.parentstaffname
                        }}</span>
                        <span *ngIf="!satffUserData.parentstaffname">-</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Partner :</label>
                        <span>{{ satffUserData.partnerName }}</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">HRMS ID :</label>
                        <span>{{ satffUserData.hrmsId }}</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Branch Name :</label>
                        <span>{{ satffUserData.branchName }}</span>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                        <label class="datalbl">Status :</label>
                        <span
                          *ngIf="
                            satffUserData.status == 'ACTIVE' || satffUserData.status == 'Active'
                          "
                          class="badge badge-success"
                        >
                          Active
                        </span>
                        <span *ngIf="satffUserData.status == 'INACTIVE'" class="badge badge-danger">
                          Inactive
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="isStaffReceiptData">
  <div class="row">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div class="panel-heading">
          <div class="displayflex">
            <button
              type="button"
              class="btn btn-secondary backbtn"
              data-toggle="tooltip"
              data-placement="bottom"
              title="Go to Staff All Details"
              (click)="staffDetialsOpen(this.openStaffID)"
            >
              <i
                class="fa fa-arrow-circle-left"
                style="color: #f7b206 !important; font-size: 28px"
              ></i>
            </button>

            <h3 class="panel-title">{{ satffUserData.firstname }} Staff Receipt</h3>
          </div>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#staffdeatilsview"
              aria-expanded="false"
              aria-controls="staffdeatilsview"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>

        <div id="staffdeatilsview" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <button
              *ngIf="receiptMgmtAccess"
              type="button"
              class="approve-btn"
              style="
                border: none;
                background: #f7b206;
                padding: 5px 11px;
                border-radius: 19px;
                height: 36px;
                color: black;
                margin-bottom: 3rem;
              "
              data-target="#paymentReciptModal"
              data-toggle="modal"
              data-backdrop="static"
              data-keyboard="false"
              class="curson_pointer"
              (click)="addNewReceipt(satffUserData.id)"
            >
              Receipt Management
              <img
                class="icon"
                src="assets/img/23_Receipt-Management_Y.png"
                style="width: 25px; height: 25px"
              />
            </button>

            <table class="table">
              <thead>
                <tr>
                  <th>Prefix</th>
                  <th>From Receipt Number</th>
                  <th>To Receipt Number</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let staff of staffreciptMappingList
                      | paginate
                        : {
                            id: 'listing_staffdata',
                            itemsPerPage: itemsReceiptPerPage,
                            currentPage: currentReceiptPage,
                            totalItems: totalReceiptRecords
                          };
                    index as i
                  "
                >
                  <td>{{ staff.prefix }}</td>
                  <td>{{ staff.fromreceiptnumber }}</td>
                  <td>{{ staff.toreceiptnumber }}</td>
                </tr>
              </tbody>
            </table>
            <br />

            <div class="pagination_Dropdown">
              <pagination-controls
                id="listing_staffdata"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageReceiptChanged($event)"
              ></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- payment Recipt Modal Modal -->
<div class="modal fade" id="paymentReciptModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">New Receipt</h3>
      </div>
      <div class="modal-body">
        <form [formGroup]="paymentReciptForm">
          <div class="form-group">
            <label for="prefix">Prefix *:</label>
            <input
              type="text"
              class="form-control"
              id="Prefix"
              name="Prefix"
              formControlName="prefix"
            />
          </div>
          <div class="form-group">
            <label for="receiptFrom">Receipt From *:</label>
            <input
              type="number"
              class="form-control"
              id="receiptFrom "
              name="receiptFrom "
              formControlName="receiptFrom"
            />
          </div>
          <div class="form-group">
            <label for="receiptTo">Receipt To *:</label>
            <input
              type="number"
              class="form-control"
              id="receiptTo"
              name="receiptTo"
              formControlName="receiptTo"
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <button
            type="submit"
            class="btn btn-primary btn-sm"
            (click)="saveNewRecipt()"
            [disabled]="!paymentReciptForm.valid"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button>
          <button
            id="btn"
            type="button"
            class="btn btn-default btn-sm"
            (click)="clearpaymentReciptForm()"
          >
            <i class="fa fa-refresh"></i>
            Clear
          </button>
          <button
            type="button"
            class="btn btn-danger btn-sm"
            data-dismiss="modal"
            (click)="clearpaymentReciptForm()"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <!-- <button type="button" class="close" data-dismiss="modal">&times;</button> -->
        <h3 class="panel-title">Change Password</h3>
      </div>
      <div class="modal-body">
        <form [formGroup]="changePasswordForm">
          <div class="form-group">
            <label for="userName">User Name :</label>
            <input
              type="text"
              class="form-control"
              id="userName"
              name="userName"
              formControlName="userName"
              value="{{ userName }}"
              disabled
            />
            <!-- disabled="disabled" value="{{ loggedInUser }}" -->
          </div>
          <!-- <div class="form-group" [ngClass]="{
              'has-error':
                changePasswordForm.get('oldPassword').errors &&
                (changePasswordForm.get('oldPassword').touched ||
                  changePasswordForm.get('oldPassword').dirty)
            }">
            <label for="oldPassword">Old Password :</label>
            <div class="form-control displayflex">
              <div style="width: 95%;">
                <input [type]="_passwordOLDType" class="inputPassword" id="oldPassword"
                  placeholder="Enter Old Password " name="oldPassword" formControlName="oldPassword" />
                <span class="help-block" *ngIf="
                    (changePasswordForm.get('oldPassword').touched ||
                      changePasswordForm.get('oldPassword').dirty) &&
                    changePasswordForm.get('oldPassword').hasError('required')
                  ">
                  Old Password is mandatory
                </span>
              </div>
              <div style="width: 5%;">
                <div *ngIf="showOLDPassword">
                  <i class="fa fa-eye" (click)="
                      showOLDPassword = false; _passwordOLDType = 'password'
                    "></i>
                </div>
                <div *ngIf="!showOLDPassword">
                  <i class="fa fa-eye-slash" (click)="showOLDPassword = true; _passwordOLDType = 'text'"></i>
                </div>
              </div>
            </div>
          </div> -->
          <div
            class="form-group"
            *ngIf="!ifgenerateOtpField"
            [ngClass]="{
              'has-error':
                changePasswordForm.get('newPassword').errors &&
                (changePasswordForm.get('newPassword').touched ||
                  changePasswordForm.get('newPassword').dirty)
            }"
          >
            <label>New Password :</label>
            <div class="form-control displayflex">
              <div style="width: 95%">
                <input
                  [type]="_passwordNewType"
                  class="inputPassword"
                  id="newPassword"
                  placeholder="Enter New Password"
                  name="newPassword"
                  formControlName="newPassword"
                />
              </div>
              <div style="width: 5%">
                <div *ngIf="showNewPassword">
                  <i
                    class="fa fa-eye"
                    (click)="showNewPassword = false; _passwordNewType = 'password'"
                  ></i>
                </div>
                <div *ngIf="!showNewPassword">
                  <i
                    class="fa fa-eye-slash"
                    (click)="showNewPassword = true; _passwordNewType = 'text'"
                  ></i>
                </div>
              </div>
            </div>
            <div
              *ngIf="
                (changePasswordForm.get('newPassword').touched ||
                  changePasswordForm.get('newPassword').dirty) &&
                changePasswordForm.get('newPassword').hasError('required')
              "
              class="errorWrap text-danger"
            >
              <div class="error text-danger">New Password is mandatory</div>
            </div>

            <!-- <input
              type="password"
              class="form-control"
              id="newPassword"
              placeholder="Enter New Password"
              name="newPassword"
              formControlName="newPassword"
            /> -->
          </div>
        </form>
        <div *ngIf="ifgenerateOtpField" style="display: flex; justify-content: flex-end">
          <button type="submit" class="btn btn-primary btn-sm" (click)="genrateOtp()">
            <i class="fa fa-check-circle"></i>
            Generate Otp
          </button>
        </div>
        <div class="form-group" *ngIf="ifgenerateOtpField">
          <label for="OTP">OTP :</label>
          <input type="text" class="form-control" id="OTP" name="OTP" [(ngModel)]="staffOTPValue" />
          <!-- disabled="disabled" value="{{ loggedInUser }}" -->
        </div>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <button
            *ngIf="ifgenerateOtpField"
            type="submit"
            class="btn btn-primary btn-sm"
            (click)="ValidOtp()"
            [disabled]="!staffOTPValue"
          >
            <i class="fa fa-check-circle"></i>
            Valid OTP
          </button>
          <button
            *ngIf="!ifgenerateOtpField"
            type="submit"
            class="btn btn-primary btn-sm"
            (click)="changePassword()"
            [disabled]="!changePasswordForm.valid"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button>
          <button
            id="btn"
            type="button"
            class="btn btn-default btn-sm"
            (click)="clearChangePasswordForm()"
          >
            <i class="fa fa-refresh"></i>
            Clear
          </button>
          <button type="button" class="btn btn-danger btn-sm" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="ifWalletStaffShow" class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to Staff All Details"
            (click)="staffDetialsOpen(this.openStaffID)"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">{{ satffUserData.firstname }} Wallet</h3>
        </div>
        <div class="right">
          <button
            aria-controls="radiusStaffWallet"
            aria-expanded="false"
            class="btn-toggle-collapse"
            class="btn-toggle-collapse"
            data-target="#radiusStaffWallet"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-body table-responsive" id="radiusStaffWallet">
        <!-- *ngIf="this.WalletAmount > 0" -->
        <div class="row">
          <div class="col-lg-3 col-md-3 m-b-10">
            <p-dropdown
              (onChange)="searchDeatil = ''"
              [(ngModel)]="searchOption"
              [filter]="true"
              [options]="searchOptionSelect"
              placeholder="Select a Search Option"
            ></p-dropdown>
          </div>
          <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'Mode'">
            <p-dropdown
              [options]="paymentModes"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Payment Mode"
              [(ngModel)]="searchDeatil"
            ></p-dropdown>
          </div>
          <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'Status'">
            <input
              class="form-control"
              [(ngModel)]="searchDeatil"
              id="searchStatus"
              placeholder="Enter Status"
              type="text"
            />
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <button
              (click)="search()"
              class="btn btn-primary"
              id="searchbtn"
              type="button"
              [disabled]="!searchDeatil"
            >
              <i class="fa fa-search"></i>
              Search
            </button>
            <button (click)="clearSearch()" class="btn btn-default" id="searchbtn" type="reset">
              <i class="fa fa-refresh"></i>
              Clear
            </button>
          </div>
        </div>
        <!-- <div style="margin-bottom: 2rem">
          <button
            (click)="showWithdrawalAmountModel()"
            class="btn btn-primary"
            id="submit"
            type="submit"
            *ngIf="this.WalletAmount > 0"
          >
            <i class="fa fa-check-circle"></i>
            Withdrawal Amount
          </button>
        </div> -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup" style="margin-top: 10px">
          <label class="datalbl">Wallet Balance :</label>
          <span>{{ WalletAmount }}</span>
        </div>
        <table class="table myclass">
          <thead>
            <tr>
              <th class="myclass">Mode</th>
              <th class="myclass">Collection</th>
              <th class="myclass">Withdraw</th>
              <th class="myclass">Pending</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of additionalDetails">
              <td class="myclass">{{ data.mode }}</td>
              <td class="myclass">{{ data.credit }}</td>
              <td class="myclass">{{ data.withdraw }}</td>
              <td class="myclass">{{ data.credit - data.withdraw }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to Staff All Details"
            (click)="staffDetialsOpen(this.openStaffID)"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">{{ satffUserData.firstname }} Ledger</h3>
        </div>
        <div class="right">
          <button
            (click)="showWithdrawalAmountModel()"
            class="btn btn-primary"
            id="submit"
            type="submit"
            *ngIf="this.WalletAmount > 0 && settle_Amount_Access"
            style="
              font-size: 14px !important;
              color: #fff !important;
              background-color: #f7b206 !important;
              padding: 6px 20px !important;
              margin-bottom: 10px;
            "
          >
            <i
              style="font-size: 14px !important; color: #fff !important"
              class="fa fa-check-circle"
            ></i>
            Settle Amount
          </button>

          <button
            aria-controls="radiusStaffLeager"
            aria-expanded="false"
            class="btn-toggle-collapse"
            class="btn-toggle-collapse"
            data-target="#radiusStaffLeager"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-body table-responsive" id="radiusStaffLeager">
        <div class="panel-body table-responsive">
          <table class="table myclass">
            <thead>
              <tr>
                <th class="myclass">Action</th>
                <th class="myclass">Payment Mode</th>
                <th class="myclass">Debit</th>
                <th class="myclass">Credit</th>
                <th class="myclass">Bank Name</th>
                <th class="myclass">Date</th>
                <th class="myclass">Status</th>
                <th class="myclass">Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let staff of staffLegderData
                    | paginate
                      : {
                          id: 'listing_staffLegderdata',
                          itemsPerPage: itemsLegderPerPage,
                          currentPage: currentLegderPage,
                          totalItems: totalLegderRecords
                        };
                  index as i
                "
              >
                <td class="myclass">{{ staff.action }}</td>
                <td class="myclass">{{ staff.paymentMode }}</td>
                <td *ngIf="staff.transactionType == 'DR'" class="myclass">
                  <!-- {{ staff?.currency ? staff?.currency : this.currency }}{{ staff.amount }} -->
                  {{ staff?.amount | currency: staff?.currency || currency : "symbol" : "1.2-2" }}
                </td>

                <td class="myclass">-</td>
                <td *ngIf="staff.transactionType == 'CR'" class="myclass">
                  <!-- {{ staff?.currency ? staff?.currency : this.currency }} {{ staff.amount }} -->
                  {{ staff?.amount | currency: staff?.currency || currency : "symbol" : "1.2-2" }}
                </td>
                <td class="myclass">{{ staff.bankName != null ? staff.bankName : "-" }}</td>
                <td class="myclass">{{ staff.date }}</td>
                <td class="myclass">{{ staff.status }}</td>
                <td class="myclass">{{ staff.remarks }}</td>
              </tr>
            </tbody>
          </table>
          <br />

          <div class="pagination_Dropdown">
            <pagination-controls
              id="listing_staffLegderdata"
              maxSize="10"
              directionLinks="true"
              previousLabel=""
              nextLabel=""
              (pageChange)="pageLegderChanged($event)"
            ></pagination-controls>
            <div id="itemPerPageDropdown">
              <p-dropdown
                (onChange)="totalItemPerPageForLedger($event)"
                [options]="pageLimitOptionsLedger"
                optionLabel="value"
                optionValue="value"
              ></p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Wallet Modal Modal -->

<p-dialog
  header="Wallet"
  [(visible)]="staffWalletModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="radiusWalletGroupForm">
      <div class="form-group">
        <label for="date">Date :</label>
        <input type="date" class="form-control" id="date" name="date" formControlName="date" />
      </div>

      <div class="form-group">
        <label for="amount">Payment Mode :</label>
        <p-dropdown
          (onChange)="onPaymentModeChange($event)"
          formControlName="paymentMode"
          [options]="paymentModes"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Payment Mode"
        ></p-dropdown>
      </div>

      <div
        class="form-group"
        *ngIf="
          radiusWalletGroupForm.value.paymentMode != null &&
          radiusWalletGroupForm.value.paymentMode.toUpperCase() != 'CHEQUE'
        "
      >
        <label for="amount">Amount :</label>
        <input
          type="number"
          class="form-control"
          id="amount "
          name="amount "
          formControlName="amount"
          placeholder="Enter a Amount"
        />
      </div>

      <div
        class="row"
        *ngIf="
          radiusWalletGroupForm.value.paymentMode != null &&
          radiusWalletGroupForm.value.paymentMode.toUpperCase() == 'CHEQUE'
        "
      >
        <div class="col-lg-12 col-md-12">
          <p-table
            [value]="staffLegderChequeData"
            [(selection)]="selectedCheques"
            [scrollable]="true"
            scrollHeight="200px"
            dataKey="id"
            [tableStyle]="{ 'min-width': '50rem' }"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 4rem">
                  <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Cheque No</th>
                <th>Amount</th>
                <th>Cheque Date</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-product>
              <tr>
                <td style="width: 4rem">
                  <p-tableCheckbox [value]="product"></p-tableCheckbox>
                </td>
                <td class="p-text-left">{{ product.chequeno }}</td>
                <td class="p-text-left">{{ product.amount }}</td>
                <td class="p-text-left">{{ product.chequedate }}</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
      <div class="form-group">
        <label for="bank">Bank :</label>

        <p-dropdown
          [options]="bankDataList"
          optionValue="id"
          optionLabel="bankname"
          filter="true"
          filterBy="bankname"
          placeholder="Select a Bank"
          formControlName="bankId"
        >
          <ng-template let-data pTemplate="item">
            <div class="item-drop1">
              <span class="item-value1"> {{ data.bankname }} ( {{ data.accountnum }} ) </span>
            </div>
          </ng-template>
        </p-dropdown>
      </div>
      <div class="form-group">
        <label for="remarks">Remarks :</label>
        <textarea
          type="text"
          class="form-control"
          id="remarks"
          name="remarks"
          formControlName="remarks"
          placeholder="Enter a Remarks"
        ></textarea>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <!-- [disabled]="!radiusWalletGroupForm.valid" -->
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        (click)="saveManageBalance()"
        [disabled]="
          !radiusWalletGroupForm.valid ||
          (radiusWalletGroupForm.value.paymentMode == 'Cheque' &&
            (selectedCheques == null || selectedCheques.length == 0))
        "
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button
        id="btn"
        type="button"
        class="btn btn-default btn-sm"
        (click)="clearWalletStaffForm()"
      >
        <i class="fa fa-refresh"></i>
        Clear
      </button>
      <button type="button" class="btn btn-danger btn-sm" (click)="closeWalletForm()">Close</button>
    </div>
  </div>
</p-dialog>

<!-- service area list -->
<div class="modal fade" id="serviceareaModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h3 class="panel-title">{{ satffUserData.fullName }} Service Area List</h3>
      </div>
      <div class="modal-body" style="max-height: 45rem !important; overflow: auto !important">
        <div class="panel-body table-responsive" id="networkDeviceTabel">
          <table class="table">
            <tbody>
              <tr>
                <td><label class="networkLabel">Service Area :</label></td>
                <td>
                  <span
                    style="word-break: break-all"
                    *ngFor="let serviceName of satffUserData.serviceAreasNameList"
                  >
                    <span>
                      {{ serviceName }},
                      <br />
                    </span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- bussiness list -->
<div class="modal fade" id="bussinessModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h3 class="panel-title">{{ satffUserData.fullName }} Business List</h3>
      </div>
      <div class="modal-body" style="max-height: 45rem !important; overflow: auto !important">
        <div class="panel-body table-responsive" id="networkDeviceTabel">
          <table class="table">
            <tbody>
              <tr>
                <td><label class="networkLabel">Business List :</label></td>
                <td>
                  <span
                    style="word-break: break-all"
                    *ngFor="let name of satffUserData.businessUnitNamesList"
                  >
                    <span>
                      {{ name }},
                      <br />
                    </span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="teamModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h3 class="panel-title">{{ satffUserData.fullName }} Team List</h3>
      </div>
      <div class="modal-body" style="max-height: 45rem !important; overflow: auto !important">
        <div class="panel-body table-responsive" id="networkDeviceTabel">
          <table class="table">
            <tbody>
              <tr>
                <td><label class="networkLabel">Team List :</label></td>
                <td>
                  <span
                    style="word-break: break-all"
                    *ngFor="let name of satffUserData.teamNameList"
                  >
                    <span>
                      {{ name }},
                      <br />
                    </span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
