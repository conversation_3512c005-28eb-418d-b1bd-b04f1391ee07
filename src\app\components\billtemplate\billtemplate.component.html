<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Bill Template</h3>
        <div class="right">
          <!-- <button type="button" class="btn-toggle-collapse" 
          data-toggle="collapse"
          data-target="#searchPlanB"
          aria-expanded="false"
          aria-controls="searchPlanB">
            <i class="fa fa-minus-circle"></i>
          </button> -->
        </div>
      </div>

      <!-- <div class="panel-body">
                <div class="row">
                     <div class="col-lg-3 col-md-3">
                        <select class="form-control" name="searchName" id="searchName" style="width: 100%"
                            [(ngModel)]="searchName">
                            <option value="">
                                Select Name
                            </option>
                            <option *ngFor="let item of billTemplatesListDataselector" value="{{item.name}}">
                                {{item.name}}
                            </option>
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3">
                        <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchbillTemplates()">
                            <i class="fa fa-search"></i> Search
                        </button>
                        <button type="reset" class="btn btn-default" id="searchbtn" (click)="clearSearchbillTemplates()">
                            <i class="fa fa-refresh"></i> Clear
                        </button>
                    </div> 
                </div>
            </div> -->
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Bill Template</h3>
        <div class="right">
          <button class="btn refreshbtn" type="reset" (click)="getbillTemplatesList('')">
            <i class="fa fa-refresh"></i>
          </button>
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#billList"
            aria-expanded="false"
            aria-controls="billList"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="billList" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <!-- <th>XML file</th> -->
                    <th style="width: 18%">Status</th>
                    <th>ISP Name</th>
                    <th *ngIf="editAccess || deleteAccess">Action</th>
                    
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of billTemplatesListData
                        | paginate
                          : {
                              id: 'billTemplatesListpageData',
                              itemsPerPage: billTemplatesListdataitemsPerPage,
                              currentPage: currentPagebillTemplatesListdata,
                              totalItems: billTemplatesListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ data.templatename }}</td>
                    <td>{{ data.templatetype }}</td>
                    <!-- <td class="discInfo" title="{{data.jrxmlfile}}">
                                        {{ data.jrxmlfile }}
                                    </td> -->
                    <td *ngIf="data.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="data.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td>{{ data.mvnoName }}</td>
                    <td class="btnAction" *ngIf="editAccess || deleteAccess">
                      <a
                        id="editbutton"
                        type="button"
                        class="detailOnAnchorClick"
                        (click)="editbillTemplates(data.id)"
                        *ngIf="editAccess"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        id="delete-button"
                        class="detailOnAnchorClick"
                        *ngIf="deleteAccess"
                        (click)="deleteConfirmonbillTemplates(data.id)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <br />
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="billTemplatesListpageData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedbillTemplatesList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                   [(ngModel)]="billTemplatesListdataitemsPerPage"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title" *ngIf="!isbillTemplatesEdit">Create Bill</h3>
        <h3 class="panel-title" *ngIf="isbillTemplatesEdit">Update Bill</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createBill"
            aria-expanded="false"
            aria-controls="createBill"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createBill" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isbillTemplatesEdit">
          Sorry you have not privilege to create operation!
        </div>
        <div class="panel-body" *ngIf="createAccess || (isbillTemplatesEdit && editAccess)">
          <form [formGroup]="billTemplatesGroupForm">
            <div>
              <label>Template Name *</label>
              <input
                id="templatename"
                type="text"
                class="form-control"
                placeholder="Enter Template Name"
                formControlName="templatename"
                [ngClass]="{
                  'is-invalid': submitted && billTemplatesGroupForm.controls.templatename.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && billTemplatesGroupForm.controls.templatename.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && billTemplatesGroupForm.controls.templatename.errors.required"
                >
                  Template Name is required.
                </div>
              </div>
              <br />
            </div>
            <div>
              <label>Template Type *</label>
              <p-dropdown
                [options]="templatetypeData"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Enter Template Type"
                formControlName="templatetype"
                [(ngModel)]="chargeOptionname"
                [ngClass]="{
                  'is-invalid': submitted && billTemplatesGroupForm.controls.templatetype.errors
                }"
              ></p-dropdown>
              <div></div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && billTemplatesGroupForm.controls.templatetype.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && billTemplatesGroupForm.controls.templatetype.errors.required"
                >
                  Template Type is required.
                </div>
              </div>
              <br />
            </div>
            <div>
              <label>XML File *</label>
              <textarea
                id="jrxmlfile"
                type="text"
                class="form-control"
                placeholder="Enter Template Type"
                formControlName="jrxmlfile"
                [ngClass]="{
                  'is-invalid': submitted && billTemplatesGroupForm.controls.jrxmlfile.errors
                }"
              ></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && billTemplatesGroupForm.controls.jrxmlfile.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && billTemplatesGroupForm.controls.jrxmlfile.errors.required"
                >
                  XML Data is required.
                </div>
              </div>
              <!-- <div>
                            <input formControlName="jrxmlfile" id="jrxmlfile" type="file" accept="application/xml" (change)="onFileChange($event)">
                        </div>

                        <div *ngIf="submitted && billTemplatesGroupForm.controls.jrxmlfile.errors.invalid" class="alert alert-danger">
                            <div *ngIf="submitted && billTemplatesGroupForm.controls.jrxmlfile.errors.required">File is required.
                            </div>
                        </div> -->
              <!-- <input type="file" (change)="onChange($event.target.files)">
                        <pre> {{fileContent}} </pre> -->

              <br />
            </div>
            <div>
              <label>Status *</label>
              <p-dropdown
                [options]="statusOptions"
                optionValue="label"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                formControlName="status"
                [ngClass]="{
                  'is-invalid': submitted && billTemplatesGroupForm.controls.status.errors
                }"
              ></p-dropdown>

              <div
                class="errorWrap text-danger"
                *ngIf="submitted && billTemplatesGroupForm.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && billTemplatesGroupForm.controls.status.errors.required"
                >
                  Status is required.
                </div>
              </div>
            </div>
            <br />

            <div class="addUpdateBtn">
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="!isbillTemplatesEdit"
                id="submit"
                (click)="addEditbillTemplates('')"
              >
                <i class="fa fa-check-circle"></i>
                Add Bill
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="isbillTemplatesEdit"
                id="submit"
                (click)="addEditbillTemplates(viewbillTemplatesListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update Bill
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                *ngIf="isbillTemplatesEdit"
                (click)="cancelBillTemplates()"
              >
                <i class="fa fa-times"></i>
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
