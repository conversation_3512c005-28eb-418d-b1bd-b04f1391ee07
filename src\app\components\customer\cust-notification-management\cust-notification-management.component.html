<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }} {{ custData.lastname }} Notification Details
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="custStatus"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#custStatus"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="custStatuss">
        <div class="panel-body table-responsive">
          <div style="font-size: 2rem; margin-top: 20px">
            Status :
            <label style="width: 150px" class="switch">
              <input
                [disabled]="!notificationManagementAccess"
                class="switch-input"
                (click)="
                  getNotificationDisableEnable(custData.id, custData.isNotificationEnable, $event)
                "
                [(ngModel)]="!custData.isNotificationEnable"
                type="checkbox"
                ng-model="view"
                ng-true-value="Active"
                ng-false-value="Inactive"
                ng-checked="view == 'Active"
              />
              <div class="switch-button">
                <span class="switch-button-left">Active</span>
                <span class="switch-button-right">Inactive</span>
              </div>
            </label>
          </div>
          <div style="margin: 20px">
            <h3>Notification Audit</h3>
            <div class="table-responsive">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Notification Date</th>
                        <th>Event</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let notification of NotificationData
                            | paginate
                              : {
                                  id: 'searchNotificationPageData',
                                  itemsPerPage: NotificationitemsPerPage1,
                                  currentPage: currentPageNotificationSlab1,
                                  totalItems: NotificationtotalRecords1
                                };
                          index as i
                        "
                      >
                        <td style="width: 5%">
                          {{ notification.messageDate | date: "dd/MM/yyyy" }}
                        </td>
                        <td style="width: 5%">{{ notification.eventName }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <br />
                  <div class="pagination_Dropdown">
                    <pagination-controls
                      (pageChange)="pageChangedMasterNotificationList($event)"
                      directionLinks="true"
                      id="searchNotificationPageData"
                      maxSize="10"
                      nextLabel=""
                      previousLabel=""
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        (onChange)="TotalItemPerPageNotificationHistory($event)"
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
