<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            Search {{ custData.title }} {{ custData.firstname }} {{ custData.lastname }} CDRs
            Session
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="myCDRSearchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#myCDRSearchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="myCDRSearchPreCust">
        <div class="panel-body table-responsive">
          <div class="searchForm">
            <form [formGroup]="searchAcctCdrForm" class="form-auth-small">
              <div class="row">
                <div class="col-md-2" style="padding-left: 3%">
                  <input
                    class="form-control"
                    formControlName="framedIpAddress"
                    name="framedIpAddress"
                    placeholder="Enter Framed IP"
                    type="text"
                  />
                </div>
                <div class="col-md-2" style="padding-left: 0%">
                  <p-calendar
                    [hideOnDateTimeSelect]="true"
                    [showButtonBar]="true"
                    [showIcon]="true"
                    [style]="{ width: '100%' }"
                    dateFormat="dd/mm/yy"
                    formControlName="fromDate"
                    placeholder="Enter From Date"
                  ></p-calendar>
                </div>
                <div class="col-md-2" style="padding-left: 0%">
                  <p-calendar
                    [hideOnDateTimeSelect]="true"
                    [showButtonBar]="true"
                    [showIcon]="true"
                    [style]="{ width: '100%' }"
                    dateFormat="dd/mm/yy"
                    formControlName="toDate"
                    placeholder="Enter To Date"
                  ></p-calendar>
                </div>
                <div class="col-md-2" style="padding-left: 0%">
                  <select class="form-control" disabled name="customerid" style="width: 100%">
                    <option>
                      {{ custData.username }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="searchbtn">
                <button
                  (click)="searchGroupByNameCDR('')"
                  class="btn btn-primary"
                  title="Search CDRs"
                  type="submit"
                >
                  <i class="fa fa-search"></i>
                  Search
                </button>
                &nbsp;
                <button
                  (click)="clearSearchCDRForm()"
                  class="btn btn-default"
                  data-toggle="tooltip"
                  title="Clear"
                  type="reset"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
                &nbsp;
                <a
                  *ngIf="exportToExcelAccess"
                  (click)="exportExcel()"
                  class="curson_pointer"
                  data-toggle="tooltip"
                  title="Export to Excel"
                >
                  <img class="icon-ex" src="assets/img/icons-07.png" />
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">
          {{ custData.title }}
          {{ custData.firstname }}
          {{ custData.lastname }} CDRs Details
        </h3>
        <div class="right">
          <button
            aria-controls="cdrDetailsPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#cdrDetailsPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="cdrDetailsPreCust">
        <div class="panel-body table-responsive">
          <div class="row">
            <div *ngIf="groupDataCDR.length !== 0" class="col-lg-12 col-md-12">
              <table class="table" id="cdr-table">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>NAS IP</th>
                    <th>Frame IP</th>
                    <th>Upload(MB)</th>
                    <th>Download(MB)</th>
                    <th>Session Time</th>
                    <th>Created Date</th>
                    <th>Modified Date</th>
                    <!-- <th *ngIf="this.accessData.cdrs.deleteAccess">
                    Action
                  </th> -->
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let group of groupDataCDR
                        | paginate
                          : {
                              id: 'listing_groupdataCDR',
                              itemsPerPage: itemsPerPageCDR,
                              currentPage: currentPageCDR,
                              totalItems: totalCDRRecords
                            };
                      index as i
                    "
                  >
                    <td class="detailOnAnchorClick">
                      <a
                        (click)="getCdrDetail(group.cdrId, group.mvnoId)"
                        data-target="#cdrDetailModal"
                        data-toggle="modal"
                        title="Click To See CDRs Detail"
                      >
                        {{ group.userName }}
                      </a>
                    </td>
                    <td>{{ group.nasIpAddress }}</td>
                    <td>{{ group.framedIpAddress }}</td>
                    <td>
                      {{ group.acctInputOctets / 1024 / 1024 | number : "1.2-2" }}
                    </td>
                    <td>
                      {{ group.acctOutputOctets / 1024 / 1024 | number : "1.2-2" }}
                    </td>
                    <td>{{ group.acctSessionTime }}</td>
                    <td>
                      {{
                        group.createDate !== null
                          ? (group.createDate | date : "yyyy-MM-dd HH:mm:ss")
                          : ""
                      }}
                    </td>
                    <td>
                      {{
                        group.lastModificationDate !== null
                          ? (group.lastModificationDate | date : "yyyy-MM-dd HH:mm:ss")
                          : ""
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <br />
              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    (pageChange)="pageCDRChanged($event)"
                    [directionLinks]="true"
                    [maxSize]="10"
                    id="listing_groupdataCDR"
                    nextLabel=""
                    previousLabel=""
                  ></pagination-controls>

                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      (onChange)="TotalItemPerCDRPage($event)"
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="groupDataCDR.length === 0" class="col-lg-12 col-md-12">
              CDRs data not found
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
