<p-dialog
  header="Change Status"
  [(visible)]="changeStatusModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeChangeStatus(false)"
>
  <div class="modal-body">
    <label>Current Status: {{ custStatus }}</label>
    <p-dropdown
      [(ngModel)]="updatedStatus"
      [options]="commondropdownService.customerChangeStatusValue"
      filter="true"
      filterBy="text"
      id="status"
      optionLabel="text"
      optionValue="value"
      placeholder="Select New Status"
      appendTo="body"
    >
    </p-dropdown>
    <div>
      <label>
        Remark
            <span *ngIf="updatedStatus === 'Terminate' && !remark" >*</span>
      </label>
      <input [(ngModel)]="remark" class="form-control" placeholder="Enter Remark" />
    </div>
  </div>

  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="changeStatus(updatedStatus, remark)"
        [disabled]="!updatedStatus"
        class="btn btn-primary btn-sm"
        data-dismiss="modal"
        type="submit"
        [disabled]="updatedStatus == 'Terminate' && !remark"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button
        (click)="closeChangeStatus(false)"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        type="button"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
<!-- <div class="modal fade" id="changeStatusModal" role="dialog" data-backdrop="static">
  <div class="modal-dialog" style="width: 35%">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Change Status</h3>
      </div>
      <div class="modal-body">
        <label>Current Status: {{ custStatus }}</label>
        <p-dropdown
          [(ngModel)]="updatedStatus"
          [options]="commondropdownService.customerChangeStatusValue"
          filter="true"
          filterBy="text"
          id="status"
          optionLabel="text"
          optionValue="value"
          placeholder="Select New Status"
        >
        </p-dropdown>
        <div *ngIf="updatedStatus == 'Terminate'">
          <label>Remark *</label>
          <input [(ngModel)]="remark" class="form-control" placeholder="Enter Remark" />
        </div>
      </div>

      <div class="modal-footer">
        <div class="addUpdateBtn">
          <button
            (click)="changeStatus(updatedStatus, remark)"
            [disabled]="!updatedStatus"
            class="btn btn-primary btn-sm"
            data-dismiss="modal"
            type="submit"
            [disabled]="updatedStatus == 'Terminate' && !remark"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button>
          <button
            (click)="closeChangeStatus(false)"
            class="btn btn-danger btn-sm"
            data-dismiss="modal"
            type="button"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div> -->
