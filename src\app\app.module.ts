import { AgmCoreModule } from "@agm/core";
import { NgModule, APP_INITIALIZER } from "@angular/core";
import { BrowserModule, Title } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { ConfirmationPopoverModule } from "angular-confirmation-popover";
import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { BlankComponent } from "./components/blank/blank.component";
import { FooterComponent } from "./components/footer/footer.component";
import { AclGernericComponentComponent } from "./components/generic-component/acl/acl-gerneric-component/acl-gerneric-component.component";
import { HomeComponent } from "./components/home/<USER>";
import { MapsComponent } from "./components/maps/maps.component";
import { SidebarComponent } from "./components/sidebar/sidebar.component";
import { SharedModule } from "./shared/shared.module";

import { ConfirmationService, MessageService } from "primeng/api";
import { DeactivateService } from "./service/deactivate.service";
import { NavMasterComponent } from "./components/nav-master/nav-master.component";
import { VendorManagementComponent } from "./components/vendor-management/vendor-management.component";
import { LoginComponent } from "./components/login/login.component";
import { NgxCaptchaModule } from "@binssoft/ngx-captcha";
import { CacheInterceptor, CacheInterceptorProvider } from "./service/cache-interceptor";
import { HttpClientModule, HTTP_INTERCEPTORS } from "@angular/common/http";
import { InMemoryCacheProvider } from "./service/cache-in-memory";
import { GooglePlaceModule } from "ngx-google-places-autocomplete";
import * as RadiusConstants from "src/app/RadiusUtils/RadiusConstants";
import { FullCalendarModule } from "@fullcalendar/angular";
import { CustomerPayComponent } from "./components/customer-pay/customer-pay.component";
import { CustomerVerifyListComponent } from "./components/customer-verify-list/customer-verify-list.component";
import { ResetPasswordComponent } from "./components/reset-password/reset-password.component";
import { KeycloakService } from "./service/keycloak.service";
import { KeycloakBootstrapService } from "./service/keycloak-bootstrap.service";

// ✅ Use a more robust global flag
// const KEYCLOAK_INIT_KEY = "keycloak_initialized";

// export function initializeKeycloak(keycloakService: KeycloakService) {
//   return () => {
//     // ✅ Check if we've already initialized in this browser session
//     const alreadyInitialized = sessionStorage.getItem(KEYCLOAK_INIT_KEY);

//     if (alreadyInitialized === "true") {
//       console.log("🔄 Keycloak already initialized in this session");
//       // ✅ Still return the service's init to get current auth state
//       return keycloakService.init();
//     }

//     console.log("🚀 First-time Keycloak initialization");

//     return keycloakService
//       .init()
//       .then(result => {
//         if (result) {
//           // ✅ Mark as initialized only on successful auth
//           sessionStorage.setItem(KEYCLOAK_INIT_KEY, "true");
//         }
//         return result;
//       })
//       .catch(err => {
//         console.error("Keycloak initialization failed:", err);
//         return false;
//       });
//   };
// }

// ✅ Bootstrap function that runs BEFORE Angular starts routing
export function bootstrapKeycloak(bootstrapService: KeycloakBootstrapService) {
  return () => {
    console.log("🌟 APP_INITIALIZER: Starting Keycloak bootstrap");
    return bootstrapService
      .initializeKeycloak()
      .then(authenticated => {
        console.log("🌟 APP_INITIALIZER: Bootstrap complete, authenticated:", authenticated);
        return authenticated;
      })
      .catch(error => {
        console.error("🌟 APP_INITIALIZER: Bootstrap failed:", error);
        return false; // Don't block app startup
      });
  };
}
@NgModule({
  declarations: [
    LoginComponent,
    AclGernericComponentComponent,
    AppComponent,
    BlankComponent,
    FooterComponent,
    HomeComponent,
    MapsComponent,
    SidebarComponent,
    CustomerPayComponent,
    CustomerVerifyListComponent,
    ResetPasswordComponent
  ],
  imports: [
    NgxCaptchaModule,
    HttpClientModule,
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    // NpDatepickerModule,
    SharedModule,
    ConfirmationPopoverModule.forRoot({
      confirmButtonType: "danger"
    }),
    AgmCoreModule.forRoot({
      apiKey: RadiusConstants.GOOGLE_MAPS_API_KEY,
      libraries: ["places"]
    }),
    GooglePlaceModule,
    FullCalendarModule
  ],
  providers: [
    ConfirmationService,
    MessageService,
    DeactivateService,
    InMemoryCacheProvider,
    CacheInterceptorProvider,
    Title,
    KeycloakService,
    KeycloakBootstrapService,
    {
      provide: APP_INITIALIZER,
      useFactory: bootstrapKeycloak,
      deps: [KeycloakBootstrapService],
      multi: true
    }
    // {
    //   provide: APP_INITIALIZER,
    //   useFactory: initializeKeycloak,
    //   multi: true,
    //   deps: [KeycloakService]
    // }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {}
