<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Task Management</h3>
        <div class="right">
          <button
            aria-controls="searchTicketM"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchTicketM"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchTicketM">
        <div *ngIf="viewTicket" class="panel-body">
          <div class="row" [formGroup]="this.searchGroupForm">
            <div class="col-lg-3 col-md-3 m-b-10">
              <p-dropdown
                [(ngModel)]="searchOption"
                formControlName="searchOption"
                [filter]="true"
                [options]="searchOptionSelect"
                filterBy="label"
                optionLabel="text"
                optionValue="value"
                placeholder="Select a Search Option"
                [ngClass]="{
                  'is-invalid':
                    this.searchSubmitted && this.searchGroupForm.controls.searchOption.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="this.searchSubmitted && searchGroupForm.controls.searchOption.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="searchGroupForm.controls.searchOption.errors.required"
                >
                  Please select any search option.
                </div>
              </div>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption != 'TAT_BREATCH' && searchOption != 'RESPONSE_TIME_BREACH'"
            >
              <input
                formControlName="searchValue"
                class="form-control"
                id="username"
                placeholder="Enter Search Detail"
                type="text"
                (keydown.enter)="searchTicket()"
                [ngClass]="{
                  'is-invalid':
                    this.searchSubmitted && this.searchGroupForm.controls.searchValue.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="this.searchSubmitted && this.searchGroupForm.controls.searchValue.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="searchGroupForm.controls.searchValue.errors.required"
                >
                  Please enter value.
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button (click)="searchTicket()" class="btn btn-primary" id="searchbtn" type="button">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                (click)="clearSearchTicket()"
                class="btn btn-default"
                id="searchbtn"
                type="reset"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>

        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="createTicketFun()" class="curson_pointer" *ngIf="createAccess">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Task</h5>
                <!-- <p>Create Task</p> -->
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="searchTicketFun()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search Task</h5>
                <!-- <p>Search Task</p> -->
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div *ngIf="this.viewTicket" class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <!-- <button
            (click)="getTicket(size)"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer List"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button> -->
          <h3 class="panel-title">Task</h3>
        </div>

        <div class="right">
          <button
            aria-controls="TicketMList"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#TicketMList"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <!-- <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)=" getTicket(size)"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer List"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ customerLedgerDetailData.title }}
            {{ customerLedgerDetailData.custname }} Details
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="precustDetails"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#precustDetails"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div> -->

      <div class="panel-collapse collapse in" id="TicketMList">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <p-tabView
                styleClass="x-tab-view"
                (onChange)="handleChange($event)"
                class="first-tabview"
              >
                <p-tabPanel class="header" header="All" [headerStyle]="{ width: '25%' }">
                  <div class="row">
                    <div style="margin: 2rem 12px">
                      <button
                        *ngIf="changeStatusAccess"
                        type="submit"
                        class="btn btn-primary"
                        title="Change Status"
                        data-target="#createChangeStatus"
                        data-toggle="modal"
                        data-backdrop="static"
                        data-keyboard="false"
                        (click)="changeStatusModalOpen('', 'mTicket')"
                        [disabled]="
                          chakedTicketData.length == 0 ||
                          chakedTicketData.caseStatus == 'Done' ||
                          chakedTicketData.caseStatus == 'Discarded'
                        "
                      >
                        Change Status
                      </button>

                      <button
                        *ngIf="bulkReassignAccess"
                        style="margin: 2rem 12px"
                        type="submit"
                        class="btn btn-primary"
                        title="Bulk Reassign"
                        data-target="#reassignTicketModal"
                        data-toggle="modal"
                        data-backdrop="static"
                        data-keyboard="false"
                        (click)="getbulkChange('', 'mTicket')"
                        [disabled]="
                          chakedTicketData.length == 0 ||
                          chakedTicketData.caseStatus == 'Rejected' ||
                          chakedTicketData.caseStatus == 'Done' ||
                          chakedTicketData.caseStatus == 'Discarded'
                        "
                      >
                        Bulk Reassign
                      </button>
                    </div>
                    <div class="panel-collapse collapse in" id="TicketMList">
                      <div class="panel-body table-responsive">
                        <div class="row">
                          <div class="col-lg-12 col-md-12">
                            <table class="table">
                              <thead>
                                <tr>
                                  <th class="widthCheckboxColom">
                                    <div class="centerCheckbox">
                                      <p-checkbox
                                        name="allChecked"
                                        [(ngModel)]="isTicketChecked"
                                        [binary]="true"
                                        (onChange)="allSelectTicket($event)"
                                        [disabled]="true"
                                      ></p-checkbox>
                                    </div>
                                  </th>
                                  <th>Issue</th>
                                  <th>Number</th>
                                  <th>Type</th>
                                  <!-- <th>Customer</th> -->
                                  <th>Assignee</th>
                                  <th>Status</th>
                                  <th>Incident Start Date</th>
                                  <th>Incident End Date</th>
                                  <th>Incident Duration</th>
                                  <!-- <th>Followup Date & Time</th> -->
                                  <th>Remain Time</th>
                                  <th>ISP Name</th>
                                  <th>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr
                                  *ngFor="
                                    let ticket of ticketData
                                      | paginate
                                        : {
                                            id: 'ticketpageData',
                                            itemsPerPage: ticketConfigitemsPerPage,
                                            currentPage: currentPageTicketConfig,
                                            totalItems: ticketConfigtotalRecords
                                          };
                                    index as i
                                  "
                                  [ngClass]="
                                    ticket?.caseStatus != 'Closed' &&
                                    ticket?.nextFollowupDate < currentDate
                                      ? 'red-bg'
                                      : ''
                                  "
                                >
                                  <td>
                                    <div class="centerCheckbox">
                                      <p-checkbox
                                        class="p-field-checkbox"
                                        [value]="ticket.isSingleTicketChecked"
                                        [inputId]="ticket.caseId"
                                        [(ngModel)]="ticket.isSingleTicketChecked"
                                        (onChange)="addTicketChecked(ticket.caseId, $event)"
                                        [binary]="true"
                                        [disabled]="
                                          ticket.parentId != currentLoginUserId ||
                                          ticket.caseStatus == 'Closed'
                                        "
                                      ></p-checkbox>
                                    </div>
                                  </td>
                                  <td>
                                    <a
                                      (click)="openTicketDetail(ticket.caseId)"
                                      href="javascript:void(0)"
                                      style="color: #f7b206"
                                    >
                                      {{ ticket.caseTitle }}
                                    </a>
                                  </td>
                                  <td>{{ ticket.caseNumber }}</td>
                                  <td>{{ ticket.caseType }}</td>
                                  <!-- <td>
                                    <a
                                      (click)="openModal('custmerDetailModal', ticket.customersId)"
                                      href="javascript:void(0)"
                                      style="color: #f7b206"
                                    >
                                      {{ ticket.userName }}
                                    </a>
                                  </td> -->

                                  <td *ngIf="ticket.currentAssigneeId">
                                    <a
                                      (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                                      href="javascript:void(0)"
                                      style="color: #f7b206"
                                    >
                                      {{ ticket.currentAssigneeName }}
                                    </a>
                                  </td>
                                  <td *ngIf="!ticket.currentAssigneeId">
                                    {{ ticket.currentAssigneeName }}
                                  </td>
                                  <td>
                                    <div *ngIf="ticket.caseStatus == 'Open'">
                                      <span class="badge badge-success">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Done'">
                                      <span class="badge badge-success">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Discarded'">
                                      <span class="badge badge-danger">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Closed'">
                                      <span class="badge badge-danger">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Rejected'">
                                      <span class="badge badge-danger">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Pending'">
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Cancelled'">
                                      <span class="badge badge-danger">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div
                                      *ngIf="
                                        ticket.caseStatus == 'Re-Open' ||
                                        ticket.caseStatus == 'Re Open'
                                      "
                                    >
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div
                                      *ngIf="
                                        ticket.caseStatus == 'In-Progress' ||
                                        ticket.caseStatus == 'In Progress'
                                      "
                                    >
                                      <span class="badge badge-success">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Resolved'">
                                      <span class="badge badge-success">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div
                                      *ngIf="
                                        ticket.caseStatus == 'On-Hold' ||
                                        ticket.caseStatus == 'On Hold'
                                      "
                                    >
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Completed'">
                                      <span class="badge badge-success">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Out of domain'">
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                    <div *ngIf="ticket.caseStatus == 'Follow Up'">
                                      <span class="badge badge-info">
                                        {{ ticket.caseStatus }}
                                      </span>
                                    </div>
                                  </td>
                                  <td>
                                    {{ ticket.incidentStartDate || "-" }}
                                  </td>
                                  <td>
                                    {{ ticket.incidentEndDate || "-" }}
                                  </td>
                                  <td>
                                    {{ ticket.incidentDuration || "-" }}
                                  </td>
                                  <!-- <td>
                                    {{
                                      ticket?.nextFollowupDate !== null &&
                                      ticket?.caseStatus !== "Closed"
                                        ? (ticket?.nextFollowupDate + " " + ticket?.nextFollowupTime
                                          | date: "dd-MM-yyyy hh:mm a")
                                        : "-"
                                    }}
                                  </td> -->
                                  <td>
                                    <span
                                      *ngIf="
                                        !(
                                          ticket.caseStatus == 'Done' ||
                                          ticket.caseStatus == 'Discarded'
                                        )
                                      "
                                    >
                                      {{ ticket.remainTime }}
                                    </span>
                                  </td>
                                  <td>{{ ticket.mvnoName }}</td>
                                  <td class="btnAction">
                                    <button
                                      *ngIf="editAccess"
                                      class="approve-btn"
                                      style="
                                        border: none;
                                        background: transparent;
                                        padding: 0;
                                        margin-right: 3px;
                                      "
                                      title="Edit"
                                      type="button"
                                      [disabled]="
                                        ticket.caseStatus == 'Open' ||
                                        ticket.caseStatus == 'Closed' ||
                                        ticket.caseStatus == 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                      (click)="editTicket(ticket.caseId)"
                                      href="javascript:void(0)"
                                      id="edit-button"
                                      type="button"
                                    >
                                      <img src="assets/img/ioc01.jpg" />
                                    </button>
                                    <!-- <a
                                      (click)="
                                        assignTicket(ticket.caseId, ticket.serviceAreaId, ticket.caseStatus)
                                      "
                                      *ngIf="editAccess && ticket.caseStatus != 'Closed'"
                                      href="javascript:void(0)"
                                      id="assign-button"
                                      title="Assign Task"
                                    >
                                      <img src="assets/img/icons-02.png" style="width: 30px" />
                                    </a>
                                    <button
                                      *ngIf="this.editAccess"
                                      (click)="approveTicket(ticket)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.status === 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Approve"
                                      type="button"
                                    >
                                      <img src="assets/img/assign.jpg" />
                                    </button>
                                    <button
                                      *ngIf="this.editAccess"
                                      (click)="rejectTicket(ticket)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.caseStatus == 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Reject"
                                      type="button"
                                    >
                                      <img src="assets/img/reject.jpg" />
                                    </button>
                                    <a
                                      (click)="
                                        followupTicketModalOpen(
                                          ticket.caseId,
                                          ticket.customersId,
                                          ticket.currentAssigneeId
                                        )
                                      "
                                      *ngIf="this.editAccess && ticket.currentAssigneeId != null"
                                      href="javascript:void(0)"
                                      title="Followup"
                                    >
                                      <img src="assets/img/followup.png" />
                                    </a>
                                    <a
                                      (click)="changeStatusModalOpen(ticket, 'pTicket')"
                                      *ngIf="this.editAccess && ticket.currentAssigneeId == currentLoginUserId"
                                      href="javascript:void(0)"
                                      title="Change Status"
                                    >
                                      <img src="assets/img/09_Ticket-change-status_Y.png" />
                                    </a> -->

                                    <!-- <button
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Reject"
                                      type="button"
                                      (click)="opechangePriorityMadel(ticket)"
                                      title="Change Priority"
                                      [disabled]="
                                        ticket.caseStatus == 'Closed' ||
                                        ticket.currentAssigneeId != currentLoginUserId
                                      "
                                    >
                                      <img src="assets/img/18_Ticket-change-priority_Y.png" />
                                    </button> -->
                                    <button
                                      class="approve-btn"
                                      style="
                                        border: none;
                                        background: transparent;
                                        padding: 0;
                                        margin-right: 3px;
                                      "
                                      title="Reject"
                                      type="button"
                                      [disabled]="
                                        ticket.caseStatus == 'Closed' ||
                                        ticket.caseStatus == 'Raise and Close' ||
                                        ticket.caseStatus == 'Rejected' ||
                                        ticket.caseStatus == 'Resolved' ||
                                        ticket.caseStatus == 'Done' ||
                                        ticket.caseStatus == 'Discarded' ||
                                        ticket.currentAssigneeId != null
                                      "
                                      (click)="pickModalOpen(ticket)"
                                      href="javascript:void(0)"
                                      title="Pick"
                                    >
                                      <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                                    </button>
                                    <!-- <a
                                      *ngIf="this.editAccess"
                                      (click)="getLinkableTickets(ticket)"
                                      href="javascript:void(0)"
                                      title="Link Task"
                                    >
                                      <img src="assets/img/10_Link-ticket-to-other-ticket_Y.png" />
                                    </a>
                                    <a
                                      *ngIf="this.editAccess"
                                      (click)="uploadDocument(ticket)"
                                      data-placement="top"
                                      data-toggle="tooltip"
                                      id="editbutton"
                                      title="Upload Documents"
                                      type="button"
                                      href="javascript:void(0)"
                                    >
                                      <img height="32" src="assets/img/up.jpg" width="32" />
                                    </a>
                                    <a
                                      *ngIf="this.editAccess"
                                      (click)="checkChangeProblemDomain(ticket)"
                                      data-placement="top"
                                      data-toggle="tooltip"
                                      id="changeproblemdomain"
                                      title="Change Problem Domain"
                                      type="button"
                                      href="javascript:void(0)"
                                    >
                                      <img
                                        height="32"
                                        src="assets/img/10_Link-ticket-to-other-ticket_Y.png"
                                        width="32"
                                      />
                                    </a>
                                    <button
                                      *ngIf="this.editAccess"
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="ETR Task"
                                      type="button"
                                      (click)="openETRModal(ticket)"
                                      href="javascript:void(0)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.status === 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                    >
                                      <img src="assets/img/19_Promise-to-Pay_Y.png" />
                                    </button> -->
                                    <button
                                      *ngIf="slaCounterAccess"
                                      class="approve-btn"
                                      style="
                                        border: none;
                                        background: transparent;
                                        padding: 0;
                                        margin-right: 3px;
                                      "
                                      title="Counter"
                                      type="button"
                                      [disabled]="
                                        ticket.caseStatus == 'Closed' ||
                                        ticket.caseStatus == 'Raise and Close' ||
                                        ticket.caseStatus == 'Rejected' ||
                                        ticket.caseStatus == 'Follow Up' ||
                                        ticket.caseStatus == 'On-Hold' ||
                                        ticket.caseStatus == 'Pending' ||
                                        ticket.caseStatus == 'Out of domain' ||
                                        ticket.caseStatus == 'On Hold'
                                      "
                                      (click)="SlaCounterModelOpen(ticket)"
                                      href="javascript:void(0)"
                                      title="SLA Time Counter"
                                    >
                                      <!--                        assets/img/E_Status_Y.png-->
                                      <img src="assets/img/E_Status_Y.png" />
                                    </button>
                                  </td>
                                </tr>
                              </tbody>
                            </table>

                            <div class="pagination_Dropdown">
                              <pagination-controls
                                (pageChange)="pageChangedTicketConfig($event)"
                                [directionLinks]="true"
                                [maxSize]="10"
                                id="ticketpageData"
                                nextLabel=""
                                previousLabel=""
                              ></pagination-controls>
                              <div id="itemPerPageDropdown">
                                <p-dropdown
                                  (onChange)="TotalItemPerPage($event)"
                                  [options]="pageLimitOptions"
                                  optionLabel="value"
                                  optionValue="value"
                                ></p-dropdown>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel class="header" header="Dashboard" [headerStyle]="{ width: '20%' }">
                  <p-tabView
                    *ngIf="showDashboardTabs"
                    (onChange)="onDashboardTabChange($event)"
                    class="card-tabview"
                  >
                    <p-tabPanel
                      *ngFor="let tab of allTaskCounts"
                      [header]="tab.label + ' ' + tab.value"
                    >
                      <div class="row">
                        <div style="margin: 2rem 12px">
                          <!-- <button *ngIf="changeStatusAccess" type="submit" class="btn btn-primary"
                                                title="Change Status" data-target="#createChangeStatus"
                                                data-toggle="modal" data-backdrop="static" data-keyboard="false"
                                                (click)="changeStatusModalOpen('', 'mTicket')" [disabled]="
                          chakedTicketData.length == 0 ||
                          chakedTicketData.caseStatus == 'Done' ||
                          chakedTicketData.caseStatus == 'Discarded'
                        ">
                                                Change Status
                                            </button>

                                            <button *ngIf="bulkReassignAccess" style="margin: 2rem 12px" type="submit"
                                                class="btn btn-primary" title="Bulk Reassign"
                                                data-target="#reassignTicketModal" data-toggle="modal"
                                                data-backdrop="static" data-keyboard="false"
                                                (click)="getbulkChange('', 'mTicket')" [disabled]="
                          chakedTicketData.length == 0 ||
                          chakedTicketData.caseStatus == 'Rejected' ||
                          chakedTicketData.caseStatus == 'Done' ||
                          chakedTicketData.caseStatus == 'Discarded'
                        ">
                                                Bulk Reassign
                                            </button> -->
                        </div>
                        <div class="panel-collapse collapse in" id="TicketMList">
                          <div class="panel-body table-responsive">
                            <div class="row">
                              <div class="col-lg-12 col-md-12">
                                <table class="table">
                                  <thead>
                                    <tr>
                                      <th class="widthCheckboxColom">
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            name="allChecked"
                                            [(ngModel)]="isTicketChecked"
                                            [binary]="true"
                                            (onChange)="allSelectTicket($event)"
                                            [disabled]="true"
                                          ></p-checkbox>
                                        </div>
                                      </th>
                                      <th>Issue</th>
                                      <th>Number</th>
                                      <th>Type</th>
                                      <!-- <th>Customer</th> -->
                                      <th>Assignee</th>
                                      <th>Status</th>
                                      <th>Incident Start Date</th>
                                      <th>Incident End Date</th>
                                      <th>Incident Duration</th>
                                      <!-- <th>Followup Date & Time</th> -->
                                      <th>Remain Time</th>
                                      <th>ISP Name</th>
                                      <th>Action</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr
                                      *ngFor="
                                        let ticket of caseStatusListData
                                          | paginate
                                            : {
                                                id: 'caseStatusPageData',
                                                itemsPerPage: casestatusitemPerPage,
                                                currentPage: currentPageCaseStatus,
                                                totalItems: caseStatusTotalRecords
                                              };
                                        index as i
                                      "
                                      [ngClass]="
                                        ticket?.caseStatus != 'Closed' &&
                                        ticket?.nextFollowupDate < currentDate
                                          ? 'red-bg'
                                          : ''
                                      "
                                    >
                                      <td>
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            class="p-field-checkbox"
                                            [value]="ticket.isSingleTicketChecked"
                                            [inputId]="ticket.caseId"
                                            [(ngModel)]="ticket.isSingleTicketChecked"
                                            (onChange)="addTicketChecked(ticket.caseId, $event)"
                                            [binary]="true"
                                            [disabled]="
                                              ticket.parentId != currentLoginUserId ||
                                              ticket.caseStatus == 'Closed'
                                            "
                                          ></p-checkbox>
                                        </div>
                                      </td>
                                      <td>
                                        <a
                                          (click)="openTicketDetail(ticket.caseId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.caseTitle }}
                                        </a>
                                      </td>
                                      <td>{{ ticket.caseNumber }}</td>
                                      <td>{{ ticket.caseType }}</td>
                                      <!-- <td>
                                    <a
                                      (click)="openModal('custmerDetailModal', ticket.customersId)"
                                      href="javascript:void(0)"
                                      style="color: #f7b206"
                                    >
                                      {{ ticket.userName }}
                                    </a>
                                  </td> -->

                                      <td *ngIf="ticket.currentAssigneeId">
                                        <a
                                          (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.currentAssigneeName }}
                                        </a>
                                      </td>
                                      <td *ngIf="!ticket.currentAssigneeId">
                                        {{ ticket.currentAssigneeName }}
                                      </td>
                                      <td>
                                        <div *ngIf="ticket.caseStatus == 'Open'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Done'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Discarded'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Closed'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Rejected'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Pending'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Cancelled'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'Re-Open' ||
                                            ticket.caseStatus == 'Re Open'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'In-Progress' ||
                                            ticket.caseStatus == 'In Progress'
                                          "
                                        >
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Resolved'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Completed'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Out of domain'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Follow Up'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                      </td>
                                      <td>
                                        {{ ticket.incidentStartDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentEndDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentDuration || "-" }}
                                      </td>
                                      <!-- <td>
                                    {{
                                      ticket?.nextFollowupDate !== null &&
                                      ticket?.caseStatus !== "Closed"
                                        ? (ticket?.nextFollowupDate + " " + ticket?.nextFollowupTime
                                          | date: "dd-MM-yyyy hh:mm a")
                                        : "-"
                                    }}
                                  </td> -->
                                      <td>
                                        <span
                                          *ngIf="
                                            !(
                                              ticket.caseStatus == 'Done' ||
                                              ticket.caseStatus == 'Discarded'
                                            )
                                          "
                                        >
                                          {{ ticket.remainTime }}
                                        </span>
                                      </td>
                                      <td>{{ ticket.mvnoName }}</td>
                                      <td class="btnAction">
                                        <button
                                          *ngIf="editAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Edit"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Open' ||
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.currentAssigneeId != this.currentLoginUserId
                                          "
                                          (click)="editTicket(ticket.caseId)"
                                          href="javascript:void(0)"
                                          id="edit-button"
                                          type="button"
                                        >
                                          <img src="assets/img/ioc01.jpg" />
                                        </button>
                                        <!-- <a
                                      (click)="
                                        assignTicket(ticket.caseId, ticket.serviceAreaId, ticket.caseStatus)
                                      "
                                      *ngIf="editAccess && ticket.caseStatus != 'Closed'"
                                      href="javascript:void(0)"
                                      id="assign-button"
                                      title="Assign Task"
                                    >
                                      <img src="assets/img/icons-02.png" style="width: 30px" />
                                    </a>
                                    <button
                                      *ngIf="this.editAccess"
                                      (click)="approveTicket(ticket)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.status === 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Approve"
                                      type="button"
                                    >
                                      <img src="assets/img/assign.jpg" />
                                    </button>
                                    <button
                                      *ngIf="this.editAccess"
                                      (click)="rejectTicket(ticket)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.caseStatus == 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Reject"
                                      type="button"
                                    >
                                      <img src="assets/img/reject.jpg" />
                                    </button>
                                    <a
                                      (click)="
                                        followupTicketModalOpen(
                                          ticket.caseId,
                                          ticket.customersId,
                                          ticket.currentAssigneeId
                                        )
                                      "
                                      *ngIf="this.editAccess && ticket.currentAssigneeId != null"
                                      href="javascript:void(0)"
                                      title="Followup"
                                    >
                                      <img src="assets/img/followup.png" />
                                    </a>
                                    <a
                                      (click)="changeStatusModalOpen(ticket, 'pTicket')"
                                      *ngIf="this.editAccess && ticket.currentAssigneeId == currentLoginUserId"
                                      href="javascript:void(0)"
                                      title="Change Status"
                                    >
                                      <img src="assets/img/09_Ticket-change-status_Y.png" />
                                    </a> -->

                                        <!-- <button
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="Reject"
                                      type="button"
                                      (click)="opechangePriorityMadel(ticket)"
                                      title="Change Priority"
                                      [disabled]="
                                        ticket.caseStatus == 'Closed' ||
                                        ticket.currentAssigneeId != currentLoginUserId
                                      "
                                    >
                                      <img src="assets/img/18_Ticket-change-priority_Y.png" />
                                    </button> -->
                                        <button
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Reject"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Resolved' ||
                                            ticket.caseStatus == 'Done' ||
                                            ticket.caseStatus == 'Discarded' ||
                                            ticket.currentAssigneeId != null
                                          "
                                          (click)="pickModalOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="Pick"
                                        >
                                          <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                                        </button>
                                        <!-- <a
                                      *ngIf="this.editAccess"
                                      (click)="getLinkableTickets(ticket)"
                                      href="javascript:void(0)"
                                      title="Link Task"
                                    >
                                      <img src="assets/img/10_Link-ticket-to-other-ticket_Y.png" />
                                    </a>
                                    <a
                                      *ngIf="this.editAccess"
                                      (click)="uploadDocument(ticket)"
                                      data-placement="top"
                                      data-toggle="tooltip"
                                      id="editbutton"
                                      title="Upload Documents"
                                      type="button"
                                      href="javascript:void(0)"
                                    >
                                      <img height="32" src="assets/img/up.jpg" width="32" />
                                    </a>
                                    <a
                                      *ngIf="this.editAccess"
                                      (click)="checkChangeProblemDomain(ticket)"
                                      data-placement="top"
                                      data-toggle="tooltip"
                                      id="changeproblemdomain"
                                      title="Change Problem Domain"
                                      type="button"
                                      href="javascript:void(0)"
                                    >
                                      <img
                                        height="32"
                                        src="assets/img/10_Link-ticket-to-other-ticket_Y.png"
                                        width="32"
                                      />
                                    </a>
                                    <button
                                      *ngIf="this.editAccess"
                                      class="approve-btn"
                                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                                      title="ETR Task"
                                      type="button"
                                      (click)="openETRModal(ticket)"
                                      href="javascript:void(0)"
                                      [disabled]="
                                        ticket.status === 'approved' ||
                                        ticket.status === 'rejected' ||
                                        ticket.status === 'Closed' ||
                                        ticket.status === 'Raise and Close' ||
                                        ticket.currentAssigneeId != this.currentLoginUserId
                                      "
                                    >
                                      <img src="assets/img/19_Promise-to-Pay_Y.png" />
                                    </button> -->
                                        <button
                                          *ngIf="slaCounterAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Counter"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Follow Up' ||
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'Pending' ||
                                            ticket.caseStatus == 'Out of domain' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                          (click)="SlaCounterModelOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="SLA Time Counter"
                                        >
                                          <!--                        assets/img/E_Status_Y.png-->
                                          <img src="assets/img/E_Status_Y.png" />
                                        </button>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>

                                <div class="pagination_Dropdown">
                                  <pagination-controls
                                    (pageChange)="pageChangedCaseStatusConfig($event)"
                                    [directionLinks]="true"
                                    [maxSize]="10"
                                    id="caseStatusPageData"
                                    nextLabel=""
                                    previousLabel=""
                                  ></pagination-controls>
                                  <div id="itemPerPageDropdown">
                                    <p-dropdown
                                      (onChange)="TotalCaseStatusItemPerPage($event)"
                                      [options]="pageLimitOptions"
                                      optionLabel="value"
                                      optionValue="value"
                                    ></p-dropdown>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </p-tabPanel>
                  </p-tabView>
                </p-tabPanel>

                <p-tabPanel class="header" header="Unpicked Task" [headerStyle]="{ width: '25%' }">
                  <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <div class="row">
                        <div style="margin: 2rem 12px">
                          <button
                            *ngIf="changeStatusAccess"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#createChangeStatus"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="changeStatusModalOpen('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Change Status
                          </button>

                          <button
                            *ngIf="bulkReassignAccess"
                            style="margin: 2rem 12px"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#reassignTicketModal"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="getbulkChange('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Rejected' ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Bulk Reassign
                          </button>
                        </div>
                        <div class="panel-collapse collapse in" id="TicketMList">
                          <div class="panel-body table-responsive">
                            <div class="row">
                              <div class="col-lg-12 col-md-12">
                                <table class="table">
                                  <thead>
                                    <tr>
                                      <th class="widthCheckboxColom">
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            name="allChecked"
                                            [(ngModel)]="isTicketChecked"
                                            [binary]="true"
                                            (onChange)="allSelectUnpickTicket($event)"
                                            [disabled]="true"
                                          ></p-checkbox>
                                        </div>
                                      </th>
                                      <th>Issue</th>
                                      <th>Number</th>
                                      <th>Type</th>
                                      <!-- <th>Customer</th> -->
                                      <th>Assignee</th>
                                      <th>Status</th>
                                      <th>Incident Start Date</th>
                                      <th>Incident End Date</th>
                                      <th>Incident Duration</th>
                                      <!-- <th>Followup Date & Time</th> -->
                                      <th>Remain Time</th>
                                      <th>ISP Name</th>
                                      <th>Action</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr
                                      *ngFor="
                                        let ticket of UnpickTicketDetails
                                          | paginate
                                            : {
                                                id: 'ticketpageData',
                                                itemsPerPage: ticketConfigitemsPerPage,
                                                currentPage: currentPageTicketConfig,
                                                totalItems: ticketConfigtotalRecords
                                              };
                                        index as i
                                      "
                                      [ngClass]="
                                        ticket?.caseStatus != 'Closed' &&
                                        ticket?.nextFollowupDate < currentDate
                                          ? 'red-bg'
                                          : ''
                                      "
                                    >
                                      <td>
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            class="p-field-checkbox"
                                            [value]="ticket.isSingleTicketChecked"
                                            [inputId]="ticket.caseId"
                                            [(ngModel)]="ticket.isSingleTicketChecked"
                                            (onChange)="
                                              addUnpickTicketChecked(ticket.caseId, $event)
                                            "
                                            [binary]="true"
                                            [disabled]="
                                              ticket.parentId != currentLoginUserId ||
                                              ticket.caseStatus == 'Closed'
                                            "
                                          ></p-checkbox>
                                        </div>
                                      </td>
                                      <td>
                                        <a
                                          (click)="openTicketDetail(ticket.caseId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.caseTitle }}
                                        </a>
                                      </td>
                                      <td>{{ ticket.caseNumber }}</td>
                                      <td>{{ ticket.caseType }}</td>
                                      <!-- <td>
                                        <a
                                          (click)="
                                            openModal('custmerDetailModal', ticket.customersId)
                                          "
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.customerName }}
                                        </a>
                                      </td> -->

                                      <td *ngIf="ticket.currentAssigneeId">
                                        <a
                                          (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.currentAssigneeName }}
                                        </a>
                                      </td>
                                      <td *ngIf="!ticket.currentAssigneeId">
                                        {{ ticket.currentAssigneeName }}
                                      </td>
                                      <td>
                                        <div *ngIf="ticket.caseStatus == 'Open'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Done'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Cancelled'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Discarded'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Closed'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Rejected'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Pending'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'Re-Open' ||
                                            ticket.caseStatus == 'Re Open'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'In-Progress' ||
                                            ticket.caseStatus == 'In Progress'
                                          "
                                        >
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Resolved'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Completed'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Out of domain'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Follow Up'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                      </td>
                                      <td>
                                        {{ ticket.incidentStartDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentEndDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentDuration || "-" }}
                                      </td>
                                      <!-- <td>
                                        {{
                                          ticket?.nextFollowupDate + " " + ticket?.nextFollowupTime
                                            | date: "dd-MM-yyyy hh:mm a"
                                        }}
                                      </td> -->
                                      <td>
                                        <span
                                          *ngIf="
                                            !(
                                              ticket.caseStatus == 'Done' ||
                                              ticket.caseStatus == 'Discarded'
                                            )
                                          "
                                        >
                                          {{ ticket.remainTime }}
                                        </span>
                                      </td>
                                      <td>{{ ticket.mvnoName }}</td>
                                      <td class="btnAction">
                                        <button
                                          *ngIf="editAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Edit"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Open' ||
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.currentAssigneeId != this.currentLoginUserId
                                          "
                                          (click)="editTicket(ticket.caseId)"
                                          href="javascript:void(0)"
                                          id="edit-button"
                                          type="button"
                                        >
                                          <img src="assets/img/ioc01.jpg" />
                                        </button>
                                        <button
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Reject"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Done' ||
                                            ticket.caseStatus == 'Discarded' ||
                                            ticket.currentAssigneeId != null
                                          "
                                          (click)="pickModalOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="Pick"
                                        >
                                          <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                                        </button>

                                        <button
                                          *ngIf="slaCounterAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Counter"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Follow Up' ||
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'Pending' ||
                                            ticket.caseStatus == 'Out of domain' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                          (click)="SlaCounterModelOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="SLA Time Counter"
                                        >
                                          <!--                        assets/img/E_Status_Y.png-->
                                          <img src="assets/img/E_Status_Y.png" />
                                        </button>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>

                                <div class="pagination_Dropdown">
                                  <pagination-controls
                                    (pageChange)="pageChangedUnpicked($event)"
                                    [directionLinks]="true"
                                    [maxSize]="10"
                                    id="ticketpageData"
                                    nextLabel=""
                                    previousLabel=""
                                  ></pagination-controls>
                                  <div id="itemPerPageDropdown">
                                    <p-dropdown
                                      (onChange)="ItemPerPageUnpicked($event)"
                                      [options]="pageLimitOptions"
                                      optionLabel="value"
                                      optionValue="value"
                                    ></p-dropdown>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </p-tabPanel>
                <p-tabPanel
                  class="header"
                  header="Assigned to my team"
                  (onChange)="getAssignToTeamTicketDetails($event)"
                  [headerStyle]="{ width: '25%' }"
                >
                  <div class="row">
                    <div class="row">
                      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="row">
                          <div style="margin: 2rem 12px">
                            <button
                              *ngIf="changeStatusAccess"
                              type="submit"
                              class="btn btn-primary"
                              title="Change Status"
                              data-target="#createChangeStatus"
                              data-toggle="modal"
                              data-backdrop="static"
                              data-keyboard="false"
                              (click)="changeStatusModalOpen('', 'mTicket')"
                              [disabled]="
                                chakedTicketData.length == 0 ||
                                chakedTicketData.caseStatus == 'Done' ||
                                chakedTicketData.caseStatus == 'Discarded'
                              "
                            >
                              Change Status
                            </button>

                            <button
                              *ngIf="bulkReassignAccess"
                              style="margin: 2rem 12px"
                              type="submit"
                              class="btn btn-primary"
                              title="Change Status"
                              data-target="#reassignTicketModal"
                              data-toggle="modal"
                              data-backdrop="static"
                              data-keyboard="false"
                              (click)="getbulkChange('', 'mTicket')"
                              [disabled]="
                                chakedTicketData.length == 0 ||
                                chakedTicketData.caseStatus == 'Rejected' ||
                                chakedTicketData.caseStatus == 'Done' ||
                                chakedTicketData.caseStatus == 'Discarded'
                              "
                            >
                              Bulk Reassign
                            </button>
                          </div>
                          <div class="panel-collapse collapse in" id="TicketMList">
                            <div class="panel-body table-responsive">
                              <div class="row">
                                <div class="col-lg-12 col-md-12">
                                  <table class="table">
                                    <thead>
                                      <tr>
                                        <th class="widthCheckboxColom">
                                          <div class="centerCheckbox">
                                            <p-checkbox
                                              name="allChecked"
                                              [(ngModel)]="isTicketChecked"
                                              [binary]="true"
                                              (onChange)="allSelectTicket($event)"
                                              [disabled]="true"
                                            ></p-checkbox>
                                          </div>
                                        </th>
                                        <th>Issue</th>
                                        <th>Number</th>
                                        <th>Type</th>
                                        <!-- <th>Customer</th> -->
                                        <th>Assignee</th>
                                        <th>Status</th>
                                        <th>Incident Start Date</th>
                                        <th>Incident End Date</th>
                                        <th>Incident Duration</th>
                                        <!-- <th>Followup Date & Time</th> -->
                                        <th>Remain Time</th>
                                        <th>ISP Name</th>
                                        <th>Action</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr
                                        *ngFor="
                                          let ticket of AssignToTeamDetails
                                            | paginate
                                              : {
                                                  id: 'ticketpageData',
                                                  itemsPerPage: ticketConfigitemsPerPage,
                                                  currentPage: currentPageTicketConfig,
                                                  totalItems: ticketConfigtotalRecords
                                                };
                                          index as i
                                        "
                                        [ngClass]="
                                          ticket?.caseStatus != 'Closed' &&
                                          ticket?.nextFollowupDate < currentDate
                                            ? 'red-bg'
                                            : ''
                                        "
                                      >
                                        <td>
                                          <div class="centerCheckbox">
                                            <p-checkbox
                                              class="p-field-checkbox"
                                              [value]="ticket.isSingleTicketChecked"
                                              [inputId]="ticket.caseId"
                                              [(ngModel)]="ticket.isSingleTicketChecked"
                                              (onChange)="addTicketChecked(ticket.caseId, $event)"
                                              [binary]="true"
                                              [disabled]="
                                                ticket.parentId != currentLoginUserId ||
                                                ticket.caseStatus == 'Closed'
                                              "
                                            ></p-checkbox>
                                          </div>
                                        </td>
                                        <td>
                                          <a
                                            (click)="openTicketDetail(ticket.caseId)"
                                            href="javascript:void(0)"
                                            style="color: #f7b206"
                                          >
                                            {{ ticket.caseTitle }}
                                          </a>
                                        </td>
                                        <td>{{ ticket.caseNumber }}</td>
                                        <td>{{ ticket.caseType }}</td>
                                        <!-- <td>
                                          <a
                                            (click)="
                                              openModal('custmerDetailModal', ticket.customersId)
                                            "
                                            href="javascript:void(0)"
                                            style="color: #f7b206"
                                          >
                                            {{ ticket.customerName }}
                                          </a>
                                        </td> -->

                                        <td *ngIf="ticket.currentAssigneeId">
                                          <a
                                            (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                                            href="javascript:void(0)"
                                            style="color: #f7b206"
                                          >
                                            {{ ticket.currentAssigneeName }}
                                          </a>
                                        </td>
                                        <td *ngIf="!ticket.currentAssigneeId">
                                          {{ ticket.currentAssigneeName }}
                                        </td>
                                        <td>
                                          <div *ngIf="ticket.caseStatus == 'Open'">
                                            <span class="badge badge-success">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Done'">
                                            <span class="badge badge-success">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Cancelled'">
                                            <span class="badge badge-danger">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Discarded'">
                                            <span class="badge badge-danger">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Closed'">
                                            <span class="badge badge-danger">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Rejected'">
                                            <span class="badge badge-danger">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Pending'">
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div
                                            *ngIf="
                                              ticket.caseStatus == 'Re-Open' ||
                                              ticket.caseStatus == 'Re Open'
                                            "
                                          >
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div
                                            *ngIf="
                                              ticket.caseStatus == 'In-Progress' ||
                                              ticket.caseStatus == 'In Progress'
                                            "
                                          >
                                            <span class="badge badge-success">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Resolved'">
                                            <span class="badge badge-success">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div
                                            *ngIf="
                                              ticket.caseStatus == 'On-Hold' ||
                                              ticket.caseStatus == 'On Hold'
                                            "
                                          >
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Completed'">
                                            <span class="badge badge-success">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Out of domain'">
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                          <div *ngIf="ticket.caseStatus == 'Follow Up'">
                                            <span class="badge badge-info">
                                              {{ ticket.caseStatus }}
                                            </span>
                                          </div>
                                        </td>
                                        <td>
                                          {{ ticket.incidentStartDate || "-" }}
                                        </td>
                                        <td>
                                          {{ ticket.incidentEndDate || "-" }}
                                        </td>
                                        <td>
                                          {{ ticket.incidentDuration || "-" }}
                                        </td>
                                        <!-- <td>
                                          {{
                                            ticket?.nextFollowupDate +
                                              " " +
                                              ticket?.nextFollowupTime | date: "dd-MM-yyyy hh:mm a"
                                          }}
                                        </td> -->
                                        <td>
                                          <span
                                            *ngIf="
                                              !(
                                                ticket.caseStatus == 'Done' ||
                                                ticket.caseStatus == 'Discarded'
                                              )
                                            "
                                          >
                                            {{ ticket.remainTime }}
                                          </span>
                                        </td>
                                        <td>{{ ticket.mvnoName }}</td>
                                        <td class="btnAction">
                                          <button
                                            *ngIf="editAccess"
                                            class="approve-btn"
                                            style="
                                              border: none;
                                              background: transparent;
                                              padding: 0;
                                              margin-right: 3px;
                                            "
                                            title="Edit"
                                            type="button"
                                            [disabled]="
                                              ticket.caseStatus == 'Open' ||
                                              ticket.caseStatus == 'Closed' ||
                                              ticket.caseStatus == 'Raise and Close' ||
                                              ticket.currentAssigneeId != this.currentLoginUserId
                                            "
                                            (click)="editTicket(ticket.caseId)"
                                            href="javascript:void(0)"
                                            id="edit-button"
                                            type="button"
                                          >
                                            <img src="assets/img/ioc01.jpg" />
                                          </button>
                                          <button
                                            class="approve-btn"
                                            style="
                                              border: none;
                                              background: transparent;
                                              padding: 0;
                                              margin-right: 3px;
                                            "
                                            title="Reject"
                                            type="button"
                                            [disabled]="
                                              ticket.caseStatus == 'Closed' ||
                                              ticket.caseStatus == 'Raise and Close' ||
                                              ticket.caseStatus == 'Rejected' ||
                                              ticket.caseStatus == 'Done' ||
                                              ticket.caseStatus == 'Discarded' ||
                                              ticket.currentAssigneeId != null
                                            "
                                            (click)="pickModalOpen(ticket)"
                                            href="javascript:void(0)"
                                            title="Pick"
                                          >
                                            <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                                          </button>

                                          <button
                                            *ngIf="slaCounterAccess"
                                            class="approve-btn"
                                            style="
                                              border: none;
                                              background: transparent;
                                              padding: 0;
                                              margin-right: 3px;
                                            "
                                            title="Counter"
                                            type="button"
                                            [disabled]="
                                              ticket.caseStatus == 'Closed' ||
                                              ticket.caseStatus == 'Raise and Close' ||
                                              ticket.caseStatus == 'Rejected' ||
                                              ticket.caseStatus == 'Follow Up' ||
                                              ticket.caseStatus == 'On-Hold' ||
                                              ticket.caseStatus == 'Pending' ||
                                              ticket.caseStatus == 'Out of domain' ||
                                              ticket.caseStatus == 'On Hold'
                                            "
                                            (click)="SlaCounterModelOpen(ticket)"
                                            href="javascript:void(0)"
                                            title="SLA Time Counter"
                                          >
                                            <!--                        assets/img/E_Status_Y.png-->
                                            <img src="assets/img/E_Status_Y.png" />
                                          </button>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>

                                  <div class="pagination_Dropdown">
                                    <pagination-controls
                                      (pageChange)="pageChangedAssignToTeam($event)"
                                      [directionLinks]="true"
                                      [maxSize]="10"
                                      id="ticketpageData"
                                      nextLabel=""
                                      previousLabel=""
                                    ></pagination-controls>
                                    <div id="itemPerPageDropdown">
                                      <p-dropdown
                                        (onChange)="ItemPerPageAssignToTeam($event)"
                                        [options]="pageLimitOptions"
                                        optionLabel="value"
                                        optionValue="value"
                                      ></p-dropdown>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </p-tabPanel>

                <p-tabPanel class="header" header="My Child Staff">
                  <div class="row" *ngIf="!showAssignedToTeam">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <div class="row">
                        <div style="margin: 2rem 12px">
                          <!-- <button
                            *ngIf="changeStatusAccess"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#createChangeStatus"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="changeStatusModalOpen('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Change Status
                          </button>

                          <button
                            *ngIf="bulkReassignAccess"
                            style="margin: 2rem 12px"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#reassignTicketModal"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="getbulkChange('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Rejected' ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Bulk Reassign
                          </button> -->
                        </div>
                        <div class="panel-collapse collapse in" id="TicketMList">
                          <div class="panel-body table-responsive">
                            <div class="row">
                              <div class="col-lg-12 col-md-12">
                                <table class="table">
                                  <thead>
                                    <tr>
                                      <th>Username</th>
                                      <th>Email</th>
                                      <th>Firstname</th>
                                      <!-- <th>Customer</th> -->
                                      <th>Lastname</th>
                                      <!-- <th>Status</th>
                                      <th>Incident Start Date</th>
                                      <th>Incident End Date</th>
                                      <th>Incident Duration</th>
                                      <th>Followup Date & Time</th>
                                      <th>Remain Time</th>
                                      <th>ISP Name</th>
                                      <th>Action</th> -->
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr
                                      *ngFor="
                                        let ticket of staffListParentList
                                          | paginate
                                            : {
                                                id: 'ticketpageData',
                                                itemsPerPage: ticketConfigitemsPerPage,
                                                currentPage: currentPageTicketConfig,
                                                totalItems: ticketConfigtotalRecords
                                              };
                                        index as i
                                      "
                                      [ngClass]="
                                        ticket?.caseStatus != 'Closed' &&
                                        ticket?.nextFollowupDate < currentDate
                                          ? 'red-bg'
                                          : ''
                                      "
                                    >
                                      <td>
                                        <a
                                          (click)="
                                            openStaffById(ticketConfigitemsPerPage, ticket.id)
                                          "
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.username }}
                                        </a>
                                      </td>
                                      <td>{{ ticket.email }}</td>
                                      <td>{{ ticket.firstname }}</td>
                                      <td>{{ ticket.lastname }}</td>
                                      <!-- <td>
                                        <a
                                          (click)="
                                            openModal('custmerDetailModal', ticket.customersId)
                                          "
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.customerName }}
                                        </a>
                                      </td> -->
                                    </tr>
                                  </tbody>
                                </table>

                                <div class="pagination_Dropdown">
                                  <pagination-controls
                                    (pageChange)="pageChangedMystaff($event)"
                                    [directionLinks]="true"
                                    [maxSize]="10"
                                    id="ticketpageData"
                                    nextLabel=""
                                    previousLabel=""
                                  ></pagination-controls>
                                  <div id="itemPerPageDropdown">
                                    <p-dropdown
                                      (onChange)="ItemPerPageStaff($event)"
                                      [options]="pageLimitOptions"
                                      optionLabel="value"
                                      optionValue="value"
                                    ></p-dropdown>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row" *ngIf="showAssignedToTeam">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <div class="row">
                        <div style="margin: 2rem 12px">
                          <button
                            *ngIf="changeStatusAccess"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#createChangeStatus"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="changeStatusModalOpen('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Change Status
                          </button>

                          <button
                            *ngIf="bulkReassignAccess"
                            style="margin: 2rem 12px"
                            type="submit"
                            class="btn btn-primary"
                            title="Change Status"
                            data-target="#reassignTicketModal"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="getbulkChange('', 'mTicket')"
                            [disabled]="
                              chakedTicketData.length == 0 ||
                              chakedTicketData.caseStatus == 'Rejected' ||
                              chakedTicketData.caseStatus == 'Done' ||
                              chakedTicketData.caseStatus == 'Discarded'
                            "
                          >
                            Bulk Reassign
                          </button>

                          <button
                            style="margin: 2rem 12px"
                            type="submit"
                            class="btn btn-primary"
                            data-target="#reassignTicketModal"
                            data-toggle="modal"
                            data-backdrop="static"
                            data-keyboard="false"
                            (click)="onBackToChildStaff()"
                            title="Back To My Child"
                          >
                            Back
                          </button>
                        </div>
                        <div class="panel-collapse collapse in" id="TicketMList">
                          <div class="panel-body table-responsive">
                            <div class="row">
                              <div class="col-lg-12 col-md-12">
                                <table class="table">
                                  <thead>
                                    <tr>
                                      <th class="widthCheckboxColom">
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            name="allChecked"
                                            [(ngModel)]="isTicketChecked"
                                            [binary]="true"
                                            (onChange)="allSelectTicket($event)"
                                            [disabled]="true"
                                          ></p-checkbox>
                                        </div>
                                      </th>
                                      <th>Issue</th>
                                      <th>Number</th>
                                      <th>Type</th>
                                      <!-- <th>Customer</th> -->
                                      <th>Assignee</th>
                                      <th>Status</th>
                                      <th>Incident Start Date</th>
                                      <th>Incident End Date</th>
                                      <th>Incident Duration</th>
                                      <!-- <th>Followup Date & Time</th> -->
                                      <th>Remain Time</th>
                                      <th>ISP Name</th>
                                      <th>Action</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr
                                      *ngFor="
                                        let ticket of staffListParentList
                                          | paginate
                                            : {
                                                id: 'ticketpageData',
                                                itemsPerPage: ticketConfigitemsPerPage,
                                                currentPage: currentPageTicketConfig,
                                                totalItems: ticketConfigtotalRecords
                                              };
                                        index as i
                                      "
                                      [ngClass]="
                                        ticket?.caseStatus != 'Closed' &&
                                        ticket?.nextFollowupDate < currentDate
                                          ? 'red-bg'
                                          : ''
                                      "
                                    >
                                      <td>
                                        <div class="centerCheckbox">
                                          <p-checkbox
                                            class="p-field-checkbox"
                                            [value]="ticket.isSingleTicketChecked"
                                            [inputId]="ticket.caseId"
                                            [(ngModel)]="ticket.isSingleTicketChecked"
                                            (onChange)="addTicketChecked(ticket.caseId, $event)"
                                            [binary]="true"
                                            [disabled]="
                                              ticket.parentId != currentLoginUserId ||
                                              ticket.caseStatus == 'Closed'
                                            "
                                          ></p-checkbox>
                                        </div>
                                      </td>
                                      <td>
                                        <a
                                          (click)="openTicketDetail(ticket.caseId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.caseTitle }}
                                        </a>
                                      </td>
                                      <td>{{ ticket.caseNumber }}</td>
                                      <td>{{ ticket.caseType }}</td>
                                      <!-- <td>
                                          <a
                                            (click)="
                                              openModal('custmerDetailModal', ticket.customersId)
                                            "
                                            href="javascript:void(0)"
                                            style="color: #f7b206"
                                          >
                                            {{ ticket.customerName }}
                                          </a>
                                        </td> -->

                                      <td *ngIf="ticket.currentAssigneeId">
                                        <a
                                          (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                                          href="javascript:void(0)"
                                          style="color: #f7b206"
                                        >
                                          {{ ticket.currentAssigneeName }}
                                        </a>
                                      </td>
                                      <td *ngIf="!ticket.currentAssigneeId">
                                        {{ ticket.currentAssigneeName }}
                                      </td>
                                      <td>
                                        <div *ngIf="ticket.caseStatus == 'Open'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Done'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Cancelled'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Discarded'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Closed'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Rejected'">
                                          <span class="badge badge-danger">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Pending'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'Re-Open' ||
                                            ticket.caseStatus == 'Re Open'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'In-Progress' ||
                                            ticket.caseStatus == 'In Progress'
                                          "
                                        >
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Resolved'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div
                                          *ngIf="
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                        >
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Completed'">
                                          <span class="badge badge-success">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Out of domain'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                        <div *ngIf="ticket.caseStatus == 'Follow Up'">
                                          <span class="badge badge-info">
                                            {{ ticket.caseStatus }}
                                          </span>
                                        </div>
                                      </td>
                                      <td>
                                        {{ ticket.incidentStartDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentEndDate || "-" }}
                                      </td>
                                      <td>
                                        {{ ticket.incidentDuration || "-" }}
                                      </td>
                                      <!-- <td>
                                          {{
                                            ticket?.nextFollowupDate +
                                              " " +
                                              ticket?.nextFollowupTime | date: "dd-MM-yyyy hh:mm a"
                                          }}
                                        </td> -->
                                      <td>
                                        <span
                                          *ngIf="
                                            !(
                                              ticket.caseStatus == 'Done' ||
                                              ticket.caseStatus == 'Discarded'
                                            )
                                          "
                                        >
                                          {{ ticket.remainTime }}
                                        </span>
                                      </td>
                                      <td>{{ ticket.mvnoName }}</td>
                                      <td class="btnAction">
                                        <button
                                          *ngIf="editAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Edit"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Open' ||
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.currentAssigneeId != this.currentLoginUserId
                                          "
                                          (click)="editTicket(ticket.caseId)"
                                          href="javascript:void(0)"
                                          id="edit-button"
                                          type="button"
                                        >
                                          <img src="assets/img/ioc01.jpg" />
                                        </button>
                                        <button
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Reject"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Done' ||
                                            ticket.caseStatus == 'Discarded' ||
                                            ticket.currentAssigneeId != null
                                          "
                                          (click)="pickModalOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="Pick"
                                        >
                                          <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                                        </button>

                                        <button
                                          *ngIf="slaCounterAccess"
                                          class="approve-btn"
                                          style="
                                            border: none;
                                            background: transparent;
                                            padding: 0;
                                            margin-right: 3px;
                                          "
                                          title="Counter"
                                          type="button"
                                          [disabled]="
                                            ticket.caseStatus == 'Closed' ||
                                            ticket.caseStatus == 'Raise and Close' ||
                                            ticket.caseStatus == 'Rejected' ||
                                            ticket.caseStatus == 'Follow Up' ||
                                            ticket.caseStatus == 'On-Hold' ||
                                            ticket.caseStatus == 'Pending' ||
                                            ticket.caseStatus == 'Out of domain' ||
                                            ticket.caseStatus == 'On Hold'
                                          "
                                          (click)="SlaCounterModelOpen(ticket)"
                                          href="javascript:void(0)"
                                          title="SLA Time Counter"
                                        >
                                          <!--                        assets/img/E_Status_Y.png-->
                                          <img src="assets/img/E_Status_Y.png" />
                                        </button>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>

                                <div class="pagination_Dropdown">
                                  <pagination-controls
                                    (pageChange)="pageChangedAssignToStaff($event)"
                                    [directionLinks]="true"
                                    [maxSize]="10"
                                    id="ticketpageData"
                                    nextLabel=""
                                    previousLabel=""
                                  ></pagination-controls>
                                  <div id="itemPerPageDropdown">
                                    <p-dropdown
                                      (onChange)="ItemPerPageAssignToStaff($event)"
                                      [options]="pageLimitOptions"
                                      optionLabel="value"
                                      optionValue="value"
                                    ></p-dropdown>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </p-tabPanel>
              </p-tabView>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="createTicket" class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isTicketEdit ? "Update" : "Create" }} Task</h3>
        <div class="right">
          <button
            aria-controls="ticketManagementCreate"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#ticketManagementCreate"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="ticketManagementCreate">
        <div class="panel-body">
          <form [formGroup]="ticketGroupForm">
            <div class="row">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Task Title *</label>
                <input
                  [ngClass]="{
                    'is-invalid': submitted && ticketGroupForm.controls.caseTitle.errors
                  }"
                  class="form-control"
                  formControlName="caseTitle"
                  placeholder="Enter Task Title"
                  type="text"
                />
                <div
                  *ngIf="submitted && ticketGroupForm.controls.caseTitle.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.caseTitle.errors.required"
                    class="error text-danger"
                  >
                    Task Title is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Team*</label>
                <div>
                  <!-- <p-dropdown
                      (onChange)="selCustomer($event)"
                      [filter]="true"
                      [ngClass]="{
                        'is-invalid': submitted && ticketGroupForm.controls.customersId.errors
                      }"
                      [options]="customerData"
                      filterBy="username"
                      formControlName="customersId"
                      optionLabel="username"
                      optionValue="id"
                      placeholder="Select a Customer"
                    ></p-dropdown> -->
                  <!--  -->
                  <!-- <p-dropdown
                    [disabled]="true"
                    [options]="parentCustList"
                    [showClear]="true"
                    [filter]="true"
                    filterBy="name"
                    formControlName="customersId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Team"
                    styleClass="disableDropdown"
                  >
                    <ng-template let-data pTemplate="item">
                      <div class="item-drop1">
                        <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                 
                  <button
                    type="button"
                    [disabled]="isTicketEdit"
                    (click)="modalOpenParentCustomer()"
                    class="btn btn-primary"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-plus-square"></i>
                  </button> -->

                  <p-dropdown
                    [disabled]="isTicketEdit"
                    [options]="teamListData"
                    [showClear]="true"
                    formControlName="teamId"
                    [filter]="true"
                    filterBy="name"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Team"
                    (onChange)="onSelectTeam($event)"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.teamId.errors
                    }"
                  >
                    <!-- <ng-template let-data pTemplate="item">
                      <div class="item-drop1">
                        <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                      </div>
                    </ng-template> -->
                  </p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.teamId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.teamId.errors.required"
                    class="error text-danger"
                  >
                    Team is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Staff</label>
                <!-- <div>
                  <p-dropdown
                    [disabled]="true"
                    [options]="parentCustList"
                    [showClear]="true"
                    [filter]="true"
                    filterBy="name"
                    formControlName="customersId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Staff"
                    styleClass="disableDropdown"
                  >
                    <ng-template let-data pTemplate="item">
                      <div class="item-drop1">
                        <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                  <button
                    type="button"
                    [disabled]="isTicketEdit"
                    (click)="modalOpenParentCustomer()"
                    class="btn btn-primary"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-plus-square"></i>
                  </button>
                </div> -->
                <div>
                  <!-- (onChange)="onSelectStaff($event)" -->
                  <p-dropdown
                    [disabled]="isTicketEdit"
                    [options]="staffDataList"
                    formControlName="currentAssigneeId"
                    [filter]="true"
                    filterBy="username"
                    optionLabel="username"
                    optionValue="id"
                    placeholder="Select a Staff"
                    (onChange)="onSelectStaff($event)"
                  >
                  </p-dropdown>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Type*</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.caseType.errors
                    }"
                    [options]="caseTypeData"
                    filterBy="text"
                    formControlName="caseType"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select Type"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.caseType.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.caseType.errors.required"
                    class="error text-danger"
                  >
                    Type is required.
                  </div>
                </div>
              </div>
              <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Services *</label>
                <p-multiSelect
                  [options]="customerServiceData"
                  optionValue="id"
                  optionLabel="serviceName"
                  [filter]="true"
                  filterBy="serviceName"
                  placeholder="Select a Service"
                  formControlName="ticketServicemappingList"
                  resetFilterOnHide="true"
                  [ngClass]="{
                    'is-invalid':
                      submitted && ticketGroupForm.controls.ticketServicemappingList.errors
                  }"
                  (onChange)="onSelectService($event.value)"
                  [disabled]="isTicketEdit"
                ></p-multiSelect>

                <div></div>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && ticketGroupForm.controls.ticketServicemappingList.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="
                      submitted && ticketGroupForm.controls.ticketServicemappingList.errors.required
                    "
                  >
                    Please select service.
                  </div>
                </div>
                <br />
              </div> -->
              <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Registered Mobile No.</label>
                <input
                  class="form-control"
                  placeholder="Enter Registered Mobile number"
                  readonly
                  formControlName="mobile"
                  type="text"
                />
              </div> -->
            </div>
            <br />

            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Additional Mobile Number</label>
                <input
                  class="form-control"
                  formControlName="customerAdditionalMobileNumber"
                  placeholder="Enter Customer Additional Mobile Number"
                  onkeypress="if(this.value.length == 10)return false"
                  type="number"
                />
                // this line is not in this group
                 <div
                    *ngIf="
                      submitted && ticketGroupForm.controls.customerAdditionalMobileNumber.errors
                    "
                    class="errorWrap text-danger"
                  >
                    <div
                      *ngIf="
                        submitted &&
                        ticketGroupForm.controls.customerAdditionalMobileNumber.errors.required
                      "
                      class="error text-danger"
                    >
                      Additional Mobile Number is required.
                    </div>
                  </div>
                  // -->

            <!-- </div> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Registered Email</label>
                <input
                  readonly
                  class="form-control"
                  placeholder="Enter Registered Email"
                  class="form-control"
                  formControlName="email"
                  type="text"
                />
              </div> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Serial Number</label>
                <p-multiSelect
                  [options]="this.serialNumbers"
                  optionValue="serialNumber"
                  optionLabel="serialNumber"
                  [filter]="true"
                  filterBy="serialNumbers"
                  placeholder="Select a Serial Number"
                  formControlName="serialNumber"
                  resetFilterOnHide="true"
                  (onChange)="getTicketReasonCategory($event.value)"
                ></p-multiSelect>
                <div></div> -->
            <!-- <div
                    class="errorWrap text-danger"
                    *ngIf="submitted && ticketGroupForm.controls.serialNumber.errors"
                  >
                    <div
                      class="error text-danger"
                      *ngIf="submitted && ticketGroupForm.controls.serialNumber.errors.required"
                    >
                      Please select Serial Number.
                    </div>
                  </div> -->
            <!-- <br />
              </div> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Additional Email </label>
                <input
                  class="form-control"
                  formControlName="customerAdditionalEmail"
                  placeholder="Enter Customer Additional Email"
                  type="text"
                />
                <div
                  *ngIf="
                    (ticketGroupForm.controls.customerAdditionalEmail.touched ||
                      ticketGroupForm.controls.customerAdditionalEmail.dirty ||
                      submitted) &&
                    ticketGroupForm.controls.customerAdditionalEmail.errors
                  "
                  class="errorWrap text-danger"
                >
                  Additional Email is not valid.
                </div>
              </div> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Username*</label>
                <input
                  [ngClass]="{
                    'is-invalid': submitted && ticketGroupForm.controls.userName.errors
                  }"
                  class="form-control"
                  formControlName="userName"
                  placeholder="Enter Username"
                  type="text"
                />
                <div
                  *ngIf="submitted && ticketGroupForm.controls.userName.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.userName.errors.required"
                    class="error text-danger"
                  >
                    Username is required.
                  </div>
                </div>
              </div> -->
            <!-- <br /> -->

            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Service Area*</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.serviceAreaName.errors
                    }"
                    [options]="commondropdownService.serviceAreaList"
                    filterBy="name"
                    formControlName="serviceAreaName"
                    optionLabel="name"
                    optionValue="name"
                    placeholder="Select a Service Area Name"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.serviceAreaName.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.serviceAreaName.errors.required"
                    class="error text-danger"
                  >
                    Service Area is required.
                  </div>
                </div>
              </div> -->
            <div class="row">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Task Category*</label>
                <div style="width: 100%; display: flex">
                  <div style="width: 80%">
                    <p-dropdown
                      (onChange)="onParentChange($event)"
                      [filter]="true"
                      [ngClass]="{
                        'is-invalid': submitted && ticketGroupForm.controls.caseCategoryId.errors
                      }"
                      [options]="parentTRCData"
                      filterBy="categoryName"
                      formControlName="caseCategoryId"
                      optionLabel="categoryName"
                      optionValue="categoryId"
                      placeholder="Select Task Category"
                      [disabled]="isTicketEdit"
                    >
                      <ng-template let-data pTemplate="item">
                        <div class="item-drop1">
                          <span class="item-value1"> {{ data.categoryName }} </span>
                        </div>
                      </ng-template>
                    </p-dropdown>
                  </div>
                  <div style="width: 14%">
                    <button
                      (click)="getTATDetails(this.ticketGroupForm.controls.caseCategoryId.value)"
                      class="btn btn-primary"
                      style="
                        border-radius: 5px;
                        padding: 5px 10px;
                        line-height: 1.5;
                        margin-left: 5px;
                      "
                      [disabled]="!ticketGroupForm.value.caseCategoryId || isTicketEdit"
                    >
                      <i class="fa fa-eye"></i>
                    </button>
                  </div>
                </div>

                <div
                  *ngIf="submitted && ticketGroupForm.controls.caseCategoryId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.caseCategoryId.errors.required"
                    class="error text-danger"
                  >
                    Task Category is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Task Sub Category*</label>
                <!-- <div style="width: 100%; display: flex"> -->
                <p-dropdown
                  [filter]="true"
                  [ngClass]="{
                    'is-invalid': submitted && ticketGroupForm.controls.caseSubCategoryId.errors
                  }"
                  [options]="childTRCData"
                  filterBy="subCategoryName"
                  formControlName="caseSubCategoryId"
                  optionLabel="subCategoryName"
                  optionValue="subCategoryId"
                  placeholder="Select Task Sub Category"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.subCategoryName }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <!-- </div> -->
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Priority*</label>
                <div>
                  <p-dropdown
                    [disabled]="true"
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.priority.errors
                    }"
                    [options]="priorityTicketData"
                    filterBy="text"
                    formControlName="priority"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select a Priority"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.priority.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.priority.errors.required"
                    class="error text-danger"
                  >
                    Priority is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" *ngIf="!showStatus">
                <label>Status*</label>
                <div>
                  <div>
                    <p-dropdown
                      [options]="createStatusList"
                      optionLabel="text"
                      optionValue="value"
                      formControlName="caseStatus"
                      placeholder="Select a Status"
                      (onChange)="getResolutionReasons($event)"
                      [ngClass]="{
                        'is-invalid': submitted && ticketGroupForm.controls.caseStatus.errors
                      }"
                    ></p-dropdown>
                  </div>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.caseStatus.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.caseStatus.errors.required"
                    class="error text-danger"
                  >
                    Status is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" *ngIf="showStatus">
                <label>Status*</label>
                <input
                  value="{{ ticketDeatailData.caseStatus }}"
                  class="form-control"
                  placeholder="Enter Status"
                  type="text"
                  [readOnly]="true"
                />
              </div>
            </div>
            <br />
            <div class="row">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>
                  Incident Start Date
                  <span *ngIf="!isTicketUpdate">*</span>
                </label>
                <p-calendar
                  [hideOnDateTimeSelect]="true"
                  [showButtonBar]="true"
                  [showIcon]="true"
                  [style]="{ width: '100%' }"
                  dateFormat="dd/mm/yy"
                  [minDate]="dateTime"
                  [showTime]="true"
                  formControlName="incidentStartDate"
                  placeholder="Enter Start Date"
                  [ngClass]="{
                    'is-invalid': submitted && ticketGroupForm.controls.incidentStartDate?.errors
                  }"
                ></p-calendar>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.incidentStartDate?.errors?.required"
                  class="errorWrap text-danger"
                >
                  <div class="error text-danger">Incident Start Date is required.</div>
                </div>
              </div>

              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Incident End Date</label>
                <p-calendar
                  [hideOnDateTimeSelect]="true"
                  [showButtonBar]="true"
                  [showTime]="true"
                  [showIcon]="true"
                  [style]="{ width: '100%' }"
                  dateFormat="dd/mm/yy"
                  [minDate]="dateTime"
                  formControlName="incidentEndDate"
                  placeholder="Enter End Date"
                ></p-calendar>
              </div>
              <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label> Task Type *</label>
                <div>
                  <p-dropdown
                    (onChange)="selectDeparment($event)"
                    [filter]="true"
                    [options]="departmentTypeData"
                    filterBy="text"
                    formControlName="department"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select a Task Type"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.department.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.department.errors.required"
                    class="error text-danger"
                  >
                    Task Type is required.
                  </div>
                </div>
              </div> -->
              <!-- </div>
            <br />
            <div class="row"> -->
              <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Task Problem Domain*</label>
                <div>
                  <p-dropdown
                    (onChange)="selReasonCategory($event)"
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid':
                        submitted && ticketGroupForm.controls.ticketReasonCategoryId.errors
                    }"
                    [options]="filteredReasonCategoryList"
                    filterBy="categoryName"
                    formControlName="ticketReasonCategoryId"
                    optionLabel="categoryName"
                    optionValue="id"
                    placeholder="Select Problem Domain"
                  >
                  </p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.ticketReasonCategoryId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      submitted && ticketGroupForm.controls.ticketReasonCategoryId.errors.required
                    "
                    class="error text-danger"
                  >
                    Problem Domain is required.
                  </div>
                </div>
                <br />
              </div> -->
              <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Task Reason</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.groupReasonId.errors
                    }"
                    [options]="groupReasonData"
                    filterBy="reason"
                    formControlName="groupReasonId"
                    optionLabel="reason"
                    optionValue="id"
                    placeholder="Select a Task Reason"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.groupReasonId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.groupReasonId.errors.required"
                    class="error text-danger"
                  >
                    Task Reason is required.
                  </div>
                </div>
                <br />
              </div> -->

              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Select Attatchement</label>
                <input
                  (change)="onFileChange($event)"
                  class="form-control"
                  formControlName="file"
                  id="txtSelectDocument"
                  multiple="multiple"
                  placeholder="Select Attachment"
                  style="padding: 2px; width: 100%"
                  type="file"
                />
                <div *ngFor="let file of selectedFilePreview; let i = index">
                  <div
                    style="
                      padding-left: 10px;
                      padding-right: 10px;
                      padding-top: 4px;
                      padding-bottom: 4px;
                      font-size: 10px;
                    "
                  >
                    {{ file?.name }}
                    <button type="button" class="close" (click)="deletSelectedFile(file?.name)">
                      &times;
                    </button>
                  </div>
                </div>
              </div>
              <div
                class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                *ngIf="ticketGroupForm.value.caseStatus == 'Follow Up'"
              >
                <label>Followup Date *</label>
                <input
                  class="form-control"
                  placeholder="Followup Date"
                  type="date"
                  formControlName="nextFollowupDate"
                  [min]="currentDt"
                />
                <div
                  *ngIf="submitted && ticketGroupForm.controls.nextFollowupDate.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.nextFollowupDate.errors.required"
                    class="error text-danger"
                  >
                    Followup Date is required.
                  </div>
                </div>
              </div>
              <div
                class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                *ngIf="ticketGroupForm.value.caseStatus == 'Follow Up'"
              >
                <label>Followup Time *</label>
                <p-calendar
                  id="fromTime"
                  formControlName="nextFollowupTime"
                  appendTo="body"
                  placeholder="Followup Time"
                  [timeOnly]="true"
                  [minDate]="minTime"
                ></p-calendar>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.nextFollowupTime.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.nextFollowupTime.errors.required"
                    class="error text-danger"
                  >
                    Followup Time is required.
                  </div>
                </div>
              </div>
              <div
                *ngIf="
                  ticketGroupForm.controls.caseStatus.enable &&
                  ticketGroupForm.value.caseStatus !== 'Follow Up'
                "
                class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
              >
                <label *ngIf="ticketGroupForm.controls.caseStatus.value != 'Resolved'"
                  >Remark*</label
                >
                <!-- <label *ngIf="ticketGroupForm.controls.caseStatus.value == 'Resolved'"
                  >Root Cause Description*</label
                > -->
                <!--  ticketGroupForm.controls.caseStatus.value != 'Resolved'
                        ? 'Remark'
                        : 'Please enter root cause description.'
                    " -->
                <div>
                  <textarea
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.firstRemark.errors
                    }"
                    placeholder="Remark"
                    class="form-control"
                    formControlName="firstRemark"
                  ></textarea>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.firstRemark.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.firstRemark.errors.required"
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
              <div
                *ngIf="
                  ticketGroupForm.controls.caseStatus.enable &&
                  ticketGroupForm.value.caseStatus == 'Follow Up'
                "
                class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
              >
                <label *ngIf="ticketGroupForm.controls.caseStatus.value != 'Resolved'"
                  >Remark*</label
                >
                <!-- <label *ngIf="ticketGroupForm.controls.caseStatus.value == 'Resolved'"
                  >Root Cause Description*</label
                > -->
                <!-- "
                      ticketGroupForm.controls.caseStatus.value != 'Resolved'
                        ? 'Remark'
                        : 'Please enter root cause description.'
                    " -->
                <div>
                  <textarea
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.firstRemark.errors
                    }"
                    placeholder="Remark"
                    class="form-control"
                    formControlName="firstRemark"
                  ></textarea>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.firstRemark.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.firstRemark.errors.required"
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="row"> -->

            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label
                  >Root Cause
                  {{
                    ticketGroupForm.controls.caseStatus.value == "Raise and Close" ? "*" : ""
                  }}</label
                >
                <div>
                  <p-dropdown
                    (onChange)="getResolutionRootCause($event.value)"
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.finalResolutionId.errors
                    }"
                    [options]="resolutionReasonData"
                    filterBy="text"
                    formControlName="finalResolutionId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select Root Cause"
                    [disabled]="ticketGroupForm.controls.caseStatus.value !== 'Raise and Close'"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.finalResolutionId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.finalResolutionId.errors.required"
                    class="error text-danger"
                  >
                    Root Cause is required.
                  </div>
                </div>
                <br />
              </div> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Resolution *</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [ngClass]="{
                      'is-invalid': submitted && ticketGroupForm.controls.rootCauseReasonId.errors
                    }"
                    [options]="rootCauseReasonData"
                    filterBy="rootCauseReason"
                    formControlName="rootCauseReasonId"
                    optionLabel="rootCauseReason"
                    optionValue="id"
                    placeholder="Select Resolution "
                    [disabled]="ticketGroupForm.controls.caseStatus.value !== 'Raise and Close'"
                  ></p-dropdown>
                </div>
                <div
                  *ngIf="submitted && ticketGroupForm.controls.rootCauseReasonId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && ticketGroupForm.controls.rootCauseReasonId.errors.required"
                    class="error text-danger"
                  >
                    Resolution is required
                  </div>
                </div>
                <br />
              </div> -->

            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label>Source</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [options]="ticketSourceTypeData"
                    filterBy="text"
                    formControlName="source"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select a Task Source"
                  ></p-dropdown>
                </div>
              </div> -->
            <!-- </div> -->
            <!-- <div class="row"> -->
            <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                <label> Sub Source</label>
                <div>
                  <p-dropdown
                    [filter]="true"
                    [options]="ticketSourceTypeData"
                    filterBy="text"
                    formControlName="subSource"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select a Task Source"
                  ></p-dropdown>
                </div>
                <br />
              </div> -->

            <!-- </div>
            <div class="row">
             
            </div> -->
            <br />
            <div class="addUpdateBtn" style="margin-top: 1.5rem">
              <button
                (click)="addEditTicket('')"
                *ngIf="!isTicketEdit"
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Add Task
              </button>
              <button
                (click)="addEditTicket(viewTicketData.caseId)"
                *ngIf="isTicketEdit"
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Update Task
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="detailTicket" class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="searchTicketFun()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Task Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">Task Detail</h3>
        </div>
        <div class="right">
          <button
            aria-controls="deatisTicketData"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#deatisTicketData"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="deatisTicketData">
        <div class="panel-body">
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Action Button</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <div class="btnAction">
                    <!-- ticketDeatailData.serviceAreaId, -->
                    <!-- ticketDeatailData.caseStatus === 'Follow Up' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' ||
                        ticketDeatailData.caseStatus != 'Closed' &&
                          ticketDeatailData.caseStatus != 'Raise and Close') -->
                    <button
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      (click)="assignTicket(ticketDeatailData.caseId, ticketDeatailData.caseStatus)"
                      *ngIf="
                        assignAccess &&
                        (currentLoginUserId === assignStaffParentId ||
                          currentLoginUserId === ticketDeatailData.currentAssigneeId)
                      "
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == null ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                      id="assign-button"
                      title="Assign Task"
                    >
                      <img src="assets/img/icons-02.png" style="width: 30px" />
                    </button>
                    <!-- <button
                      (click)="approveTicket(ticketDeatailData)"
                      [disabled]="
                        ticketDeatailData.status === 'approved' ||
                        ticketDeatailData.caseStatus === 'rejected' ||
                        ticketDeatailData.status === 'Closed' ||
                        ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.status === 'Raise and Close' ||
                        ticketDeatailData.currentAssigneeId != this.currentLoginUserId
                      "
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Approve"
                      type="button"
                    >
                      <img src="assets/img/assign.jpg" />
                    </button> -->
                    <!-- <button
                      (click)="rejectTicket(ticketDeatailData)"
                      [disabled]="
                        ticketDeatailData.status === 'approved' ||
                        ticketDeatailData.caseStatus === 'rejected' ||
                        ticketDeatailData.status === 'Closed' ||
                        ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.caseStatus == 'Raise and Close' ||
                        ticketDeatailData.case_order == 1 ||
                        ticketDeatailData.currentAssigneeId != this.currentLoginUserId
                      "
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Reject"
                      type="button"
                    >
                      <img src="assets/img/reject.jpg" />
                    </button> -->
                    <!-- <button
                      (click)="
                        followupTicketModalOpen(
                          ticketDeatailData.caseId,
                          ticketDeatailData.customersId,
                          ticketDeatailData.currentAssigneeId
                        )
                      "
                      *ngIf="followUpAccess && ticketDeatailData.currentAssigneeId != null"
                      href="javascript:void(0)"
                      title="Followup"
                      data-toggle="modal"
                      data-target="#scheduleFollowup"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      type="button"
                      [disabled]="
                        ticketDeatailData.status === 'approved' ||
                        ticketDeatailData.caseStatus === 'rejected' ||
                        ticketDeatailData.status === 'Closed' ||
                        ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.caseStatus == 'Raise and Close'
                      "
                    >
                      <img src="assets/img/followup.png" />
                    </button> -->
                    <!-- ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.status === 'Closed' ||
                        ticketDeatailData.caseStatus === 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' ||
                        
                         *ngIf="
                        (changeStatusAccess && currentLoginUserId === assignStaffParentId) ||
                        (ticketDeatailData.caseStatus == 'Resolved' &&
                          ticketDeatailData.finalClosedById != 'null')
                      "
                      
                       (ticketDeatailData.finalResolvedById != null &&
                          ticketDeatailData.finalResolvedById != this.currentLoginUserId)-->
                    <button
                      (click)="changeStatusModalOpen(ticketDeatailData, 'pTicket')"
                      *ngIf="
                        changeStatusAccess &&
                        (currentLoginUserId === assignStaffParentId ||
                          currentLoginUserId === ticketDeatailData.currentAssigneeId)
                      "
                      [disabled]="
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Change Status"
                    >
                      <img src="assets/img/09_Ticket-change-status_Y.png" />
                    </button>
                    <!-- ||
                        !(
                          ticketDeatailData.caseStatus === 'In-Progress' &&
                          currentLoginUserId === assignStaffParentId
                        ) -->
                    <!-- ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.caseStatus == 'Raise and Close' ||
                        ticketDeatailData.caseStatus === 'Resolved' ||
                        ticketDeatailData.caseStatus === 'Rejected' || -->
                    <button
                      *ngIf="changePriorityAccess"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Reject"
                      type="button"
                      (click)="opechangePriorityMadel(ticketDeatailData)"
                      title="Change Priority"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == 'null' ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Resolved' ||
                        ticketDeatailData.caseStatus === 'Cancelled' ||
                        currentLoginUserId !== assignStaffParentId
                      "
                    >
                      <img src="assets/img/18_Ticket-change-priority_Y.png" />
                    </button>
                    <!-- ticketDeatailData.caseStatus === 'In Progress' && -->
                    <button
                      *ngIf="linkTicketAccess"
                      (click)="openLinkTicketDialog(ticketDeatailData)"
                      href="javascript:void(0)"
                      title="Link Task"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      type="button"
                      [disabled]="
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                    >
                      <img src="assets/img/10_Link-ticket-to-other-ticket_Y.png" />
                    </button>
                    <!-- ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' ||
                        ticketDeatailData.caseStatus === 'Rejected' || -->
                    <button
                      *ngIf="uploadDocAccess"
                      (click)="uploadDocument(ticketDeatailData)"
                      data-placement="top"
                      data-toggle="tooltip"
                      id="editbutton"
                      title="Upload Documents"
                      type="button"
                      href="javascript:void(0)"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == 'null' ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    >
                      <img height="32" src="assets/img/up.jpg" width="32" />
                    </button>
                    <!-- ticketDeatailData.teamHierarchyMappingId == null || -->
                    <!-- (ticketDeatailData.caseStatus === 'In-Progress' &&
                          currentLoginUserId !== assignStaffParentId )-->
                    <!--  (ticketDeatailData.caseStatus === 'In Progress' &&
                      ticketDeatailData.caseStatus == 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Resolved' || -->
                    <button
                      *ngIf="changePBDomainAccess"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == 'null' ||
                        currentLoginUserId !== assignStaffParentId ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Resolved' ||
                        ticketDeatailData.caseStatus === 'Cancelled'
                      "
                      (click)="checkChangeProblemDomain(ticketDeatailData)"
                      data-placement="top"
                      data-toggle="tooltip"
                      id="changeproblemdomain"
                      title="Change Category"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      type="button"
                    >
                      <img
                        height="32"
                        src="assets/img/10_Link-ticket-to-other-ticket_Y.png"
                        width="32"
                      />
                    </button>
                    <!-- ticketDeatailData.caseStatus === 'approved' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' || -->
                    <button
                      *ngIf="etrAccess"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="ETR Task"
                      type="button"
                      (click)="openETRModal(ticketDeatailData)"
                      href="javascript:void(0)"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId != this.currentLoginUserId ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                    >
                      <img src="assets/img/19_Promise-to-Pay_Y.png" />
                    </button>
                    <!-- ticketDeatailData.caseStatus == 'Open' ||
                        ticketDeatailData.caseStatus === 'approved' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' || -->
                    <button
                      *ngIf="remarksAccess"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Task Remark"
                      type="button"
                      (click)="
                        ticketRemarkModalOpen(
                          ticketDeatailData,
                          ticketDeatailData.caseId,
                          ticketDeatailData.customersId,
                          ticketDeatailData.currentAssigneeId
                        )
                      "
                      href="javascript:void(0)"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == null ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                    >
                      <img src="assets/img/followup.png" />
                    </button>
                    <!-- Task Conversation Button-->
                    <!-- ticketDeatailData.caseStatus == 'Open' ||
                        ticketDeatailData.caseStatus === 'approved' ||
                        ticketDeatailData.caseStatus === 'Rejected' ||
                        ticketDeatailData.caseStatus === 'Closed' ||
                        ticketDeatailData.caseStatus === 'Raise and Close' || -->
                    <button
                      *ngIf="conversationAccess && ticketDeatailData.caseOrigin === 'Email'"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      title="Task Conversation"
                      type="button"
                      (click)="ticketConversationModalOpen(ticketDeatailData.caseId)"
                      href="javascript:void(0)"
                      [disabled]="
                        ticketDeatailData.currentAssigneeId == 'null' ||
                        ticketDeatailData.caseStatus === 'Done' ||
                        ticketDeatailData.caseStatus === 'Discarded'
                      "
                    >
                      <img src="assets/img/05_inventory-to-customer_Y.png" />
                    </button>
                    <!-- *ngIf="downloadDocumentsAccess" -->
                    <button
                      (click)="downloadResolveDocument(ticketDeatailData)"
                      data-placement="top"
                      data-toggle="tooltip"
                      id="editbutton"
                      title="Download Documents"
                      type="button"
                      href="javascript:void(0)"
                      class="approve-btn"
                      style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    >
                      <img height="32" src="assets/img/pdf.png" width="32" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Basic Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Name :</label>
                  <span>{{ ticketDeatailData?.caseTitle }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Number :</label>
                  <span>{{ ticketDeatailData?.caseNumber }}</span>
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Customer Name :</label>
                  <span>{{ ticketDeatailData.userName }}</span>
                </div>
              </div>
              <div class="row"> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">User Name :</label>
                  <span>{{ ticketDeatailData.userName }}</span>
                </div> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">service Area Name :</label>
                  <span>{{ ticketDeatailData.serviceAreaName }}</span>
                </div> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                                                                <label class="datalbl">OLT: </label>
                                                                <span>{{ticketDeatailData.oltName}}</span>

                                                            </div>
                                                            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                                                                <label class="datalbl">Slot: </label>
                                                                <span>{{ticketDeatailData.slotName}}</span>
                                                            </div> -->
                <!-- </div>
                                                              <div class="row">
                                                            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                                                                <label class="datalbl">Port: </label>
                                                                <span>{{ticketDeatailData.portName}}</span>
                                                            </div> -->
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Task Type :</label>
                  <span>{{ ticketDeatailData?.caseType }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Priority :</label>
                  <span>{{ ticketDeatailData?.priority }}</span>
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Followup Date & Time:</label>
                  <span>
                    {{
                      ticketDeatailData?.nextFollowupDate && ticketDeatailData?.nextFollowupTime
                        ? (ticketDeatailData?.nextFollowupDate +
                            " " +
                            ticketDeatailData?.nextFollowupTime | date: "dd-MM-yyyy hh:mm a")
                        : " "
                    }}
                  </span>
                </div> -->
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Task Status :</label>
                  <span>{{ ticketDeatailData?.caseStatus }}</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Category :</label>
                  <span>{{ ticketDeatailData?.caseCategoryName }}</span>
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Sub Problem Domain :</label>
                  <span>{{ ticketDeatailData.caseReasonSubCategory }}</span>
                </div> -->
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Sub Category :</label>
                  <span>{{ ticketDeatailData?.caseSubCategoryName }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Create Date :</label>
                  <span>{{ ticketDeatailData?.createdate }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Last Modified Date :</label>
                  <span>{{ ticketDeatailData?.updatedate }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Helper Name :</label>
                  <span *ngIf="ticketDeatailData?.helperName">
                    <a (click)="openHelperModel()" href="javascript:void(0)" style="color: #f7b206"
                      >Helper Name
                    </a></span
                  >
                  <span *ngIf="!ticketDeatailData?.helperName">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Tat name :</label>
                  <span *ngIf="caseInfo?.name">
                    <a (click)="openTATModel()" href="javascript:void(0)" style="color: #f7b206"
                      >{{ caseInfo?.name }} ,
                    </a></span
                  >
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Source :</label>
                  <span *ngIf="ticketDeatailData.source">{{ ticketDeatailData.source }}</span>
                  <span *ngIf="!ticketDeatailData.source">-</span>
                </div> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Sub Source :</label>
                  <span *ngIf="ticketDeatailData.subSource">{{ ticketDeatailData.subSource }}</span>
                  <span *ngIf="!ticketDeatailData.subSource">-</span>
                </div> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Serial Number :</label>
                  <span *ngIf="ticketDeatailData.serialNumber">{{
                    ticketDeatailData.serialNumber
                  }}</span>
                  <span *ngIf="!ticketDeatailData.serialNumber">-</span>
                </div> -->
                <!-- <div
                  *ngIf="ticketDeatailData?.parentTicketId"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                >
                  <label class="datalbl">Parent Task Details :</label>
                  <span>
                    <a
                      (click)="openParentTicketModel(ticketDeatailData?.parentTicketId)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                      >Click here
                    </a></span
                  >
                </div>
                <div
                  *ngIf="ticketDeatailData?.parentTicketId"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                >
                  <label class="datalbl">Child Task Details :</label>
                  <span>
                    <a
                      (click)="openChildTicketModel(ticketDeatailData?.caseId)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                      >Click here
                    </a></span
                  >
                </div> -->
              </div>
            </div>
          </fieldset>
          <!-- <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Feedback Details *</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table
                    *ngIf="ticketDeatailData.department == 'Technical'"
                    class="table table-hover"
                  >
                    <thead>
                      <tr>
                        <th style="width: 7%">Task Id</th>
                        <th>Create Date</th>
                        <th>Problem Type</th>
                        <th style="width: 18%">Reason</th>
                        <th>Behaviour Professionalism</th>
                        <th>Service Experience</th>
                        <th style="text-align: center">Rating</th>
                        <th>Remark</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let caseUpdate of feedbackDetails">
                        <td>{{ caseUpdate.ticketid }}</td>
                        <td>{{ caseUpdate.created_date | date: "dd-MM-yyyy hh:mm a" }}</td>
                        <td>{{ caseUpdate.problem_type }}</td>
                        <td>{{ caseUpdate.reason }}</td>
                        <td>{{ caseUpdate.behaviour_professionalism }}</td>
                        <td>{{ caseUpdate.service_experience }}</td>
                        <td>
                          <p-rating
                            [cancel]="false"
                            [ngModel]="caseUpdate.overall_rating"
                            [readonly]="true"
                          ></p-rating>
                        </td>
                        <td>{{ caseUpdate.general_remarks }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <table *ngIf="ticketDeatailData.department == 'Sales'" class="table table-hover">
                    <thead>
                      <tr>
                        <th style="width: 7%">Task Id</th>
                        <th>Create Date</th>
                        <th>Sales Support</th>
                        <th>Staff Behaviour</th>
                        <th>Behaviour</th>
                        <th>Payment Mode</th>
                        <th>Current Bandwidth Feedback</th>
                        <th>Current Price Feedback</th>
                        <th>Referal Information</th>
                        <th style="text-align: center; width: 12%">Rating</th>
                        <th>Remark</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let caseUpdate of feedbackDetails">
                        <td>{{ caseUpdate.ticketid }}</td>
                        <td>{{ caseUpdate.created_date | date: "dd-MM-yyyy hh:mm a" }}</td>
                        <td>{{ caseUpdate.support_type }}</td>
                        <td>
                          {{
                            caseUpdate.behaviour_professionalism
                              ? caseUpdate.behaviour_professionalism
                              : "-"
                          }}
                        </td>
                        <td>{{ caseUpdate.staff_behavior }}</td>
                        <td>{{ caseUpdate.payment_mode }}</td>
                        <td>{{ caseUpdate.infoOfPaymentMode }}</td>
                        <td>{{ caseUpdate.current_bandwidth_feedback }}</td>
                        <td>{{ caseUpdate.referal_information }}</td>
                        <td>
                          <p-rating
                            [cancel]="false"
                            [ngModel]="caseUpdate.overall_rating"
                            [readonly]="true"
                          ></p-rating>
                        </td>
                        <td>{{ caseUpdate.general_remarks }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset> -->
          <!-- <td>{{ caseUpdate.service_experience }}</td> -->
          <!-- <td>{{ caseUpdate.service_experience }}</td> -->

          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Task History</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table
                    *ngIf="ticketDeatailData?.caseUpdateList !== undefined"
                    class="table table-hover"
                  >
                    <thead>
                      <tr>
                        <!-- <th>Comment By</th> -->
                        <th>Create By</th>
                        <th>Create Date</th>
                        <th>Update Date</th>
                        <th>Update By</th>
                        <th>View Progress Details</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let caseUpdate of ticketDeatailData?.caseUpdateList">
                        <!-- <td>{{caseUpdate.commentBy}}</td> -->
                        <td>{{ caseUpdate?.createdByName }}</td>
                        <td>{{ caseUpdate?.createdate }}</td>
                        <td>{{ caseUpdate?.updatedate }}</td>
                        <td>{{ caseUpdate?.lastModifiedByName }}</td>
                        <td>
                          <a
                            (click)="viewProgressDetails(caseUpdate)"
                            href="javascript:void(0)"
                            id="assign-button"
                            title="View Progress Details"
                          >
                            <img src="assets/img/icons-03.png" style="width: 30px" />
                          </a>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
          <!-- <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Task Followup Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Task Number</th>
                        <th>FollowUp Name</th>
                        <th>FollowUp Date & Time</th>
                        <th width="25%">Remarks</th>
                        <th width="10%">Status</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <ng-container
                        *ngFor="
                          let followUpDetails of cafFollowupList
                            | paginate
                              : {
                                  id: 'cafFollowupItemsPerPage',
                                  itemsPerPage: cafFollowupItemsPerPage,
                                  currentPage: cafFollowupPage,
                                  totalItems: followupListTotalRecordsForUserAndTeam
                                };
                          index as i
                        "
                      >
                        <tr
                          [ngStyle]="{
                            'background-color': checkFollowUpDatetimeOutDate(followUpDetails)
                              ? '#ffff0096'
                              : ''
                          }"
                        >
                          <td>{{ followUpDetails?.caseNumber }}</td>
                          <td>{{ followUpDetails?.followUpName }}</td>
                          <td>
                            {{ followUpDetails?.followUpDatetime | date: "dd/MM/yyyy HH:mm:ss" }}
                          </td>
                          <td>{{ followUpDetails?.remarks }}</td>
                          <td>
                            <span
                              *ngIf="
                                followUpDetails?.status == 'Closed' ||
                                followUpDetails?.status == 'closed'
                              "
                              class="badge badge-info"
                              >Closed</span
                            >
                            <span
                              *ngIf="
                                followUpDetails?.status == 'Pending' ||
                                followUpDetails?.status == 'pending'
                              "
                              class="badge badge-danger"
                              >Pending</span
                            >
                            <span
                              *ngIf="
                                followUpDetails?.status == 'ReSchedule' ||
                                followUpDetails?.status == 'reschedule'
                              "
                              class="badge badge-info"
                              >ReSchedule</span
                            >
                          </td>
                          <td class="btnAction">
                            <button
                              [disabled]="
                                followUpDetails?.status == 'Closed' ||
                                followUpDetails?.status == 'closed'
                              "
                              type="button"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              title="Reschedule FollowUp"
                              (click)="rescheduleFollowUp(followUpDetails)"
                            >
                              <img
                                style="width: 25px; height: 25px"
                                src="assets/img/D_Extend-Expiry-Date_Y.png"
                              />
                            </button>
                            <button
                              [disabled]="
                                followUpDetails?.status == 'Closed' ||
                                followUpDetails?.status == 'closed'
                              "
                              type="button"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              title="Close FollowUp"
                              (click)="closeFollowUp(followUpDetails)"
                            >
                              <img style="width: 25px; height: 25px" src="assets/img/reject.jpg" />
                            </button>
                            <button
                              type="button"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              title="Remark FollowUp"
                              (click)="remarkFollowUp(followUpDetails)"
                            >
                              <img
                                style="width: 25px; height: 25px"
                                src="assets/img/icons-03.png"
                              />
                            </button>
                            <button
                              type="button"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              title="Call"
                              (click)="makeACall()"
                            >
                              <img
                                style="width: 25px; height: 25px"
                                src="assets/img/phone-icon.png"
                              />
                            </button>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>
                  <div style="display: flex">
                    <pagination-controls
                      (pageChange)="pageChangedCafFollowup($event)"
                      [directionLinks]="true"
                      id="cafFollowupItemsPerPage"
                      [maxSize]="10"
                      nextLabel=""
                      previousLabel=""
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        (onChange)="totalCafFollowupItems($event)"
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset> -->
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Internal Remarks</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <!-- <th>Comment By</th> -->
                        <th>Task Name</th>
                        <!-- <th>Customer Name</th> -->
                        <th>Staff Name</th>
                        <th>Remark Date</th>
                        <th>Remark</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let ticketRemark of ticketRemarkListData">
                        <!-- <td>{{caseUpdate.commentBy}}</td> -->
                        <td>{{ ticketRemark?.caseTitle }}</td>
                        <!-- <td>{{ ticketRemark?.customersName }}</td> -->
                        <td>{{ ticketRemark?.staffUserName }}</td>
                        <td>{{ ticketRemark?.remarkDate | date: "dd-MM-yyyy hh:mm a" }}</td>
                        <td>{{ ticketRemark?.remark }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset>
            <legend>Task Assign Audit</legend>
            <div class="boxWhite">
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Task Number</th>
                          <th>Action</th>
                          <th>Staff name</th>
                          <th>Remark</th>
                          <th>Action Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let data of workflowAuditData1
                              | paginate
                                : {
                                    id: 'searchMasterPageData',
                                    itemsPerPage: MasteritemsPerPage1,
                                    currentPage: currentPageMasterSlab1,
                                    totalItems: MastertotalRecords1
                                  };
                            index as i
                          "
                        >
                          <td>
                            <div *ngIf="data.entityName">
                              {{ data?.entityName }}
                            </div>
                            <div *ngIf="data.planName">
                              {{ data?.planName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data?.action }}
                            </div>
                          </td>

                          <td
                            class="curson_pointer"
                            (click)="openTeamDetailModel(data)"
                            style="color: #f7b206"
                          >
                            <div>{{ data?.actionByName }}</div>
                          </td>

                          <td>
                            <div>
                              {{ data?.remark }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data?.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        (pageChange)="pageChangedMasterList($event)"
                        [directionLinks]="true"
                        [maxSize]="10"
                        id="searchMasterPageData"
                        nextLabel=""
                        previousLabel=""
                      ></pagination-controls>
                      <div id="itemPerPageDropdown"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset>
            <legend>Task Attachments</legend>
            <div class="boxWhite">
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>File Name</th>
                          <th>Status</th>
                          <th>Uploaded By</th>
                          <th>Action Date</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let data of ticketDeatailData?.caseDocDetails">
                          <td>
                            <a
                              (click)="
                                attachementDownloadAccess
                                  ? downloadDoc(data?.filename, data?.docId, data?.ticketId)
                                  : null
                              "
                              href="javascript:void(0)"
                              [style.color]="attachementDownloadAccess ? 'blue' : 'gray'"
                            >
                              {{ data?.filename }}
                            </a>
                          </td>
                          <td>
                            <div>
                              {{ data?.docStatus }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data?.createdByName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data?.createdate }}
                            </div>
                          </td>
                          <td>
                            <div>
                              <button
                                *ngIf="attachementAccess"
                                (click)="showticketDocData(data)"
                                class="btn btn-primary"
                                style="
                                  border-radius: 5px;
                                  padding: 5px 10px;
                                  line-height: 1.5;
                                  margin-left: 5px;
                                "
                              >
                                <i class="fa fa-eye"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>ETR Report</legend>
            <div class="boxWhite">
              <div style="display: flex; justify-content: flex-start; align-items: center">
                <label>ETR Excel Sheet : </label>
                <button
                  *ngIf="etrExcelDownloadAccess"
                  class="approve-btn"
                  style="border: none; background: transparent; padding: 0; margin-right: 3px"
                  type="button"
                  title="Download to ETR Excel Sheet"
                  data-toggle="tooltip"
                  (click)="ticketETRXMLDownload()"
                  [disabled]="ticketETRDetailData.length == 0"
                >
                  <img
                    class="icon-ex"
                    src="assets/img/icons-07.png"
                    style="height: 32px; width: 32px"
                  />
                </button>
              </div>
              <div class="row" style="margin-top: 1.5rem">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <!-- <th>customer User Name</th> -->
                        <th>staff Person Name</th>
                        <th>Task Number</th>
                        <th>Message Mode</th>
                        <th>Notification Mode</th>
                        <th>Notification Time</th>
                        <th>Notification Date</th>
                        <th>Notification Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let Data of ticketETRDetailData">
                        <!-- <td>{{ Data.custUserName }}</td> -->
                        <td>{{ Data.staffPersonName }}</td>
                        <td>{{ Data.caseNumber }}</td>
                        <td>{{ Data.messageMode }}</td>
                        <td>{{ Data.notificationMode }}</td>
                        <td>{{ Data.notificationSentTime }}</td>
                        <td>{{ Data.notificationSentDate }}</td>
                        <td>{{ Data.notificationStatus }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>TAT Report</legend>
            <div class="boxWhite">
              <div class="row" style="margin-top: 1.5rem">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Tat Action</th>
                        <th>Tat Time</th>
                        <th>SLA Time</th>
                        <!-- <th>Tat Start Time</th> -->
                        <th>Tat Message</th>
                        <!-- <th>Assign Staff</th> -->
                        <th>Task Level</th>
                        <th>Notification For</th>

                        <th>Is Tat Breached</th>
                        <th>Message Mode</th>
                        <th>Message Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let Data of ticketTATDetailData">
                        <td>{{ Data.tatAction }}</td>
                        <td>{{ Data.tatTime }} {{ " " }} {{ Data.tatUnit }}</td>
                        <td>{{ Data.slaTime }} {{ " " }} {{ Data.slaUnit }}</td>
                        <td>
                          <span *ngIf="Data.tatMessage">
                            <a
                              (click)="openTATMessageDataModal(Data)"
                              href="javascript:void(0)"
                              style="color: #f7b206"
                              >show more
                            </a></span
                          >
                          <div *ngIf="!Data.tatMessage">{{ "-" }}</div>
                        </td>
                        <!-- <td>{{ Data.staffName }}</td> -->
                        <td>{{ Data.caseLevel }}</td>
                        <td>{{ Data.notificationFor }}</td>
                        <td>{{ Data.isTatBreached }}</td>
                        <td>{{ Data.messageMode }}</td>
                        <td>{{ Data.messageStatus }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem" *ngIf="linkTicketData.length > 0">
            <legend>Link Ticket</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup m-0">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Task Number</th>
                        <th>Task Status</th>
                        <th>Customer Details</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let caseUpdate of linkTicketData">
                        <td>{{ caseUpdate?.caseNumber }}</td>
                        <td>{{ caseUpdate?.caseStatus }}</td>
                        <td>
                          <button
                            (click)="showSavedCustomerDetails(caseUpdate.customerId)"
                            class="btn btn-primary"
                            style="
                              border-radius: 5px;
                              padding: 5px 10px;
                              line-height: 1.5;
                              margin-left: 4px;
                            "
                            [disabled]="!caseUpdate.customerId"
                          >
                            <i class="fa fa-eye"></i>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Customer Details"
  [(visible)]="displaySavedCustomerDialog"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  (onHide)="closeCustomerDetailsModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel" *ngIf="savedCustomerDetails">
        <div id="trcDataModel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-6 col-md-6 dataGroup">
                    <label class="datalbl">User Name :</label>
                    <span>{{ savedCustomerDetails.username }}</span>
                  </div>
                  <div class="col-lg-6 col-md-6 dataGroup">
                    <label class="datalbl">Name :</label>
                    <span>{{ savedCustomerDetails.name }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-6 col-md-6 dataGroup">
                    <label class="datalbl">Mobile :</label>
                    <span>{{ savedCustomerDetails.mobile }}</span>
                  </div>
                  <div class="col-lg-6 col-md-6 dataGroup">
                    <label class="datalbl">Address Type :</label>
                    <span>{{ savedCustomerDetails.selectedCustAddressType }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-6 col-md-6 dataGroup">
                    <label class="datalbl">Plan Name :</label>
                    <span>{{ savedCustomerDetails.planName }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-12 col-md-12 dataGroup">
                    <label class="datalbl">Customer Address :</label>
                    <span>{{ savedCustomerDetails.customerAddress }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeCustomerDetailsModel()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Assign Task"
  [(visible)]="assignTicketModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="assignStaffTicketForm">
      <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Team*</label>
          <div>
            <p-dropdown
              [ngClass]="{
                'is-invalid': staffsubmmitted && assignStaffTicketForm.controls.teamId.errors
              }"
              [options]="teamListData"
              [showClear]="true"
              formControlName="teamId"
              [filter]="true"
              filterBy="name"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Team"
              (onChange)="onSelectTeam($event)"
              appendTo="body"
            ></p-dropdown>
          </div>
          <div
            *ngIf="staffsubmmitted && assignStaffTicketForm.controls.teamId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && assignStaffTicketForm.controls.teamId.errors.required"
              class="error text-danger"
            >
              Team is required.
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Staff</label>
          <div>
            <!--   [ngClass]="{
                'is-invalid': staffsubmmitted && assignStaffTicketForm.controls.staffId.errors
              }" -->
            <p-dropdown
              [options]="staffDataList"
              formControlName="staffId"
              [filter]="true"
              filterBy="name"
              optionLabel="fullName"
              optionValue="id"
              placeholder="Select a Staff"
              appendTo="body"
            ></p-dropdown>
            <!-- (onChange)="onSelectStaff($event)" -->
          </div>
          <!-- <div
            *ngIf="staffsubmmitted && assignStaffTicketForm.controls.staffId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && assignStaffTicketForm.controls.staffId.errors.required"
              class="error text-danger"
            >
              Staff is required.
            </div>
          </div> -->
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': staffsubmmitted && assignStaffTicketForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="staffsubmmitted && assignStaffTicketForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && assignStaffTicketForm.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="assignTicketSubmit()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Assign Staff
    </button>
    <button class="btn btn-default" data-dismiss="modal" type="button" (click)="hideAssignTickt()">
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Rating Task"
  [(visible)]="ratingTicketModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="ratingTicketForm">
      <div class="form-group">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p-rating [cancel]="false" formControlName="rating"></p-rating>
          </div>
          <div
            *ngIf="ratingSubmmitted && ratingTicketForm.controls.rating.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="ratingSubmmitted && ratingTicketForm.controls.rating.errors.required"
              class="error text-danger"
            >
              Rating is required.
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>Feedback*</label>
            <textarea
              [ngClass]="{
                'is-invalid': ratingSubmmitted && ratingTicketForm.controls.customerFeedback.errors
              }"
              class="form-control"
              formControlName="customerFeedback"
              name="remark"
              rows="5"
            ></textarea>
            <div
              *ngIf="ratingSubmmitted && ratingTicketForm.controls.customerFeedback.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  ratingSubmmitted && ratingTicketForm.controls.customerFeedback.errors.required
                "
                class="error text-danger"
              >
                Feedback is required.
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="ratingTicket()" class="btn btn-primary" type="submit">
      <i class="fa fa-check-circle"></i>
      Submit
    </button>
    <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
  </div>
</p-dialog>

<p-dialog
  header="Task Remark"
  [(visible)]="followUpModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="followupForm">
      <div class="form-group">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p-dropdown
              [(ngModel)]="selectedRemarkType"
              formControlName="remarkType"
              [options]="remarkTypeOption"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Remark Type"
              [ngClass]="{
                'is-invalid': followupSubmmitted && followupForm.controls.remarkType.errors
              }"
            ></p-dropdown>
            <div
              class="errorWrap text-danger"
              *ngIf="followupSubmmitted && followupForm.controls.remarkType.errors"
            >
              <div
                class="error text-danger"
                *ngIf="followupSubmmitted && followupForm.controls.remarkType.errors.required"
              >
                Please select any remark type.
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <br />
            <label>Remark*</label>
            <textarea
              [ngClass]="{
                'is-invalid': followupSubmmitted && followupForm.controls.remark.errors
              }"
              class="form-control"
              formControlName="remark"
              name="remark"
              rows="5"
            ></textarea>
            <div
              *ngIf="followupSubmmitted && followupForm.controls.remark.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="followupSubmmitted && followupForm.controls.remark.errors.required"
                class="error text-danger"
              >
                Remark is required.
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="followupTicket()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Submit
    </button>
    <button class="btn btn-default" type="button" (click)="closeFollowupTicket()">Close</button>
  </div>
</p-dialog>
<p-dialog
  header="Staff Details"
  [(visible)]="staffDetailModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Basic Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Name :</label>
            <span>{{ staffData.fullName }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Email :</label>
            <span>{{ staffData.email }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Phone :</label>
            <span>{{ staffData.phone }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Username :</label>
            <span>{{ staffData.username }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Role Name :</label>
            <span *ngIf="staffData.roleName">{{ staffData.roleName?.join(", ") }}</span>
            <span *ngIf="staffData.roleName == null">-</span>
          </div>
          <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Service Area :</label>
            <a
              data-target="#serviceAreaDetail"
              (click)="onClickServiceArea()"
              href="javascript:void(0)"
              style="color: blue"
              >click here</a
            >
          </div> -->
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Parent Staff Name :</label>
            <span>{{ staffData.parentstaffname }}</span>
          </div>
        </div>
        <!-- <div class="row">
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Team name List :</label>
              </div>
            </div> -->
      </div>
      <fieldset class="boxWhite">
        <legend>Team List :</legend>
        <div class="row">
          <div
            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
            *ngFor="let data of staffData.teamNameList"
          >
            <span>{{ data }}</span>
          </div>
        </div>
      </fieldset>
    </fieldset>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeStaffDetailModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Task Progress Details"
  [(visible)]="caseUpdateDetailsModel"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <table *ngIf="caseUpdateDetails !== undefined" class="table table-hover">
      <thead>
        <tr>
          <th>Entity Type</th>
          <th>Operation</th>
          <th>Old Value</th>
          <th>New Value</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let caseUpdate of caseUpdateDetails">
          <td>{{ caseUpdate.entitytype }}</td>
          <td>{{ caseUpdate.operation }}</td>
          <td>{{ caseUpdate.oldvalue }}</td>
          <td>{{ caseUpdate.newvalue }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Modal footer -->
  <div class="modal-footer">
    <button (click)="closeCaseUpdatePopup()" class="btn btn-danger" type="button">Close</button>
  </div>
</p-dialog>

<p-dialog
  header="Change Status"
  [(visible)]="changeStatusModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="chnageStatusForm">
      <div class="form-group">
        <div class="row">
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="this.changeStatusSingleMultiple == 'pTicket'"
          >
            <label>Old Status</label>
            <div>
              <p-dropdown
                [disabled]="true"
                [options]="statusData"
                formControlName="oldStatus"
                optionLabel="text"
                optionValue="value"
              ></p-dropdown>
            </div>
            <br />
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>New Status*</label>
            <div *ngIf="this.changeStatusSingleMultiple == 'pTicket'">
              <p-dropdown
                (onChange)="getResolutionReasonsChangeStatus($event.value)"
                [filter]="true"
                [ngClass]="{
                  'is-invalid': changeStausSubmitted && chnageStatusForm.controls.newStatus.errors
                }"
                [options]="ChangestatusList"
                filterBy="text"
                formControlName="newStatus"
                optionLabel="text"
                optionValue="value"
                placeholder="Select a Task Status"
              ></p-dropdown>
            </div>
            <div *ngIf="this.changeStatusSingleMultiple != 'pTicket'">
              <p-dropdown
                (onChange)="getResolutionReasonsChangeStatusbulk($event.value)"
                [filter]="true"
                [ngClass]="{
                  'is-invalid': changeStausSubmitted && chnageStatusForm.controls.newStatus.errors
                }"
                [options]="ChangestatusList"
                filterBy="text"
                formControlName="newStatus"
                optionLabel="text"
                optionValue="value"
                placeholder="Select a Task Status"
              ></p-dropdown>
            </div>

            <div
              *ngIf="changeStausSubmitted && chnageStatusForm.controls.newStatus.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="changeStausSubmitted && chnageStatusForm.controls.newStatus.errors.required"
                class="error text-danger"
              >
                Task Status is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="showIncidentEndDateCalendar && chnageStatusForm.get('incidentEndDate')"
          >
            <label>Incident End Date*</label>
            <p-calendar
              [hideOnDateTimeSelect]="true"
              [showButtonBar]="true"
              [showIcon]="true"
              [style]="{ width: '100%' }"
              dateFormat="dd/mm/yy"
              [showTime]="true"
              [minDate]="dateTime"
              formControlName="incidentEndDate"
              placeholder="Enter End Date"
              [ngClass]="{
                'is-invalid':
                  changeStausSubmitted && chnageStatusForm.controls.incidentEndDate?.errors
              }"
            ></p-calendar>

            <div
              *ngIf="
                changeStausSubmitted && chnageStatusForm.controls.incidentEndDate?.errors?.required
              "
              class="errorWrap text-danger"
            >
              <div class="error text-danger">Incident End Date is required.</div>
            </div>
          </div>

          <br />
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="isCall">
            <label>Call Details :</label>
            &nbsp;&nbsp;
            <input
              (change)="onCallDisconnected('true')"
              formControlName="call_status"
              name="call_status"
              type="radio"
              value="true"
            />
            &nbsp;&nbsp;Connected&nbsp;&nbsp;&nbsp;&nbsp;
            <input
              (change)="onCallDisconnected('false')"
              class="ml-3"
              formControlName="call_status"
              name="call_status"
              type="radio"
              value="false"
            />
            &nbsp;&nbsp;Disconnected
            <br />
            <br />
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="isCallDisconnected">
            <label>Select Reason:</label>
            <p-dropdown
              [options]="reasonForCallDisconnect"
              filter="true"
              filterBy="label"
              formControlName="deacivate_reason"
              [(ngModel)]="searchOption"
              placeholder="Select a Call Disconnet reason"
            >
            </p-dropdown>
            <br />
            <br />
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="isticket">
            <label>Task Closed :</label>
            &nbsp;&nbsp;
            <input formControlName="is_closed" name="is_closed" type="radio" value="true" />
            &nbsp;&nbsp;Yes&nbsp;&nbsp;&nbsp;&nbsp;
            <input
              class="ml-3"
              formControlName="is_closed"
              name="is_closed"
              type="radio"
              value="false"
            />
            &nbsp;&nbsp;No
            <br /><br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="this.chnageStatusForm.value.newStatus == 'Resolved'"
          >
            <label>Root Cause</label>
            <div>
              <p-dropdown
                (onChange)="getResolutionRootCause($event.value)"
                [filter]="true"
                [options]="resolutionReasonData"
                filterBy="text"
                formControlName="finalResolutionId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Root Cause"
              ></p-dropdown>
            </div>

            <!-- <div
                  *ngIf="changeStausSubmitted && chnageStatusForm.controls.finalResolutionId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      changeStausSubmitted &&
                      chnageStatusForm.controls.finalResolutionId.errors.required
                    "
                    class="error text-danger"
                  >
                    Root Cause is required when ticket mark as closed.
                  </div>
                </div> -->
            <!-- <br />
          </div> -->
            <!-- <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="this.chnageStatusForm.value.newStatus == 'Resolved'"
          >
            <label>Resolution </label>
            <div>
              <p-dropdown
                [filter]="true"
                [options]="rootCauseReasonData"
                filterBy="rootCauseReason"
                formControlName="rootCauseReasonId"
                optionLabel="rootCauseReason"
                optionValue="id"
                placeholder="Select Resolution"
              ></p-dropdown>
            </div> -->
            <!-- <div
                  *ngIf="changeStausSubmitted && chnageStatusForm.controls.rootCauseReasonId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      changeStausSubmitted &&
                      chnageStatusForm.controls.rootCauseReasonId.errors.required
                    "
                    class="error text-danger"
                  >
                    Resolution is required when ticket mark as resolved.
                  </div>
                </div> -->
            <br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="this.chnageStatusForm.value.newStatus == 'Follow Up'"
          >
            <label>Followup Date *</label>
            <input
              class="form-control"
              placeholder="Followup Date"
              type="date"
              formControlName="nextFollowupDate"
            />
            <div
              *ngIf="changeStausSubmitted && chnageStatusForm.controls.nextFollowupDate.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  changeStausSubmitted && chnageStatusForm.controls.nextFollowupDate.errors.required
                "
                class="error text-danger"
              >
                Followup Date is required.
              </div>
            </div>

            <br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="this.chnageStatusForm.value.newStatus == 'Follow Up'"
          >
            <label>Followup Time *</label>

            <p-calendar
              id="fromTime"
              formControlName="nextFollowupTime"
              placeholder="Followup Time"
              [timeOnly]="true"
            ></p-calendar>
            <div></div>
            <div
              *ngIf="changeStausSubmitted && chnageStatusForm.controls.nextFollowupTime.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  changeStausSubmitted && chnageStatusForm.controls.nextFollowupTime.errors.required
                "
                class="error text-danger"
              >
                Followup Time is required.
              </div>
            </div>

            <br />
          </div>
          <!-- <div
                class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                *ngIf="
                  this.chnageStatusForm.value.newStatus == 'Resolved' ||
                  this.chnageStatusForm.value.newStatus == 'Closed'
                "
              >
                <label>Service Area</label>
                <div>
                  <input
                    readonly
                    [(ngModel)]="nameOfService"
                    placeholder="Select helper name"
                    class="form-control"
                    formControlName="serviceAreaValue"
                  />
                </div>
                <br />
              </div> -->

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="
              this.chnageStatusForm.value.newStatus == 'Resolved' ||
              this.chnageStatusForm.value.newStatus == 'Closed'
            "
          >
            <label>Helper Name</label>
            <div>
              <p-multiSelect
                [options]="staffList"
                optionValue="username"
                optionLabel="username"
                filter="true"
                placeholder="Please enter helper name"
                formControlName="helperName"
                resetFilterOnHide="true"
              >
              </p-multiSelect>
            </div>
            <br />
          </div>
          <br />
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label *ngIf="chnageStatusForm.controls.newStatus.value != 'Resolved'">Remark</label>
            <label *ngIf="chnageStatusForm.controls.newStatus.value == 'Resolved'"
              >Resolution Description</label
            >
            <textarea
              [ngClass]="{
                'is-invalid': changeStausSubmitted && chnageStatusForm.controls.remark.errors
              }"
              class="form-control"
              formControlName="remark"
              name="remark"
              rows="5"
            ></textarea>
            <div
              *ngIf="changeStausSubmitted && chnageStatusForm.controls.remark.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="changeStausSubmitted && chnageStatusForm.controls.remark.errors.required"
                class="error text-danger"
              >
                Remark is required.
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="this.changeStatusSingleMultiple == 'pTicket'"
      (click)="changeStatusTicket()"
      class="btn btn-primary"
      data-dismiss="modal"
      id="submit"
      type="submit"
      [disabled]="!this.chnageStatusForm.valid"
    >
      <i class="fa fa-check-circle"></i>
      Submit
    </button>
    <button
      *ngIf="this.changeStatusSingleMultiple == 'mTicket'"
      (click)="changeSelectStatus()"
      class="btn btn-primary"
      data-dismiss="modal"
      id="submit"
      type="submit"
      [disabled]="!this.chnageStatusForm.valid"
    >
      <i class="fa fa-check-circle"></i>
      Submit
    </button>

    <button
      (click)="closeChangeStatus()"
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Assign Staff"
  [(visible)]="assignCustomerCAFModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <h5>Select Staff</h5>
          <p-table [(selection)]="selectStaff" [value]="approveCAF" responsiveLayout="scroll">
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToAllStaffTicket()"
      *ngIf="approved && !selectStaff"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      (click)="assignToStaffTicket(true)"
      *ngIf="approved && selectStaff"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeAssignStaffModel()"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Select Staff"
  [(visible)]="rejectCustomerCAFModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <h5>Select Staff</h5>
          <p-table [(selection)]="selectStaffReject" [value]="rejectCAF" responsiveLayout="scroll">
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToAllStaffTicket()"
      *ngIf="reject && !selectStaffReject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      (click)="assignToStaffTicket(false)"
      *ngIf="reject && selectStaffReject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      (click)="closeRejectCustomerCAFModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Change Priority"
  [(visible)]="idChangePriority"
  [style]="{ width: '50vw' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="height: 200px">
    <div style="margin: 1rem 0">Priority : {{ selcetTicketData.priority }}</div>
    <div class="form-group">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <!-- <label>Priority</label> -->
          <p-dropdown
            [(ngModel)]="selectPriorityValue"
            [filter]="true"
            [options]="priorityTicketData"
            filterBy="text"
            optionLabel="text"
            optionValue="value"
            placeholder="Select a Priority"
          ></p-dropdown>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="SavechangePriority()"
      [disabled]="!selectPriorityValue"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeChangePriority()"
    >
      Close
    </button>
  </div>
</p-dialog>

<div *ngIf="showLinktickets">
  <p-dialog
    header="Select Task"
    [(visible)]="selectLinkTicket"
    [style]="{ width: '60%' }"
    [modal]="true"
    [responsive]="true"
    [draggable]="false"
    [closable]="false"
  >
    <div class="modal-body">
      <div class="panel-body table-responsive">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <table class="table">
              <thead>
                <tr>
                  <th>Select</th>
                  <th>Name</th>
                  <th>Number</th>
                  <th>Type</th>
                  <!-- <th>Customer</th> -->

                  <th>Assignee</th>
                  <th>Status</th>
                  <!-- <th>Followup Date & Time</th> -->
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let ticket of ticketDataForLink
                      | paginate
                        : {
                            id: 'ticketpageData',
                            itemsPerPage: linkTicketItemsPerPage,
                            currentPage: currentPageTicketConfig,
                            totalItems: ticketConfigtotalRecords
                          };
                    index as i
                  "
                  [ngClass]="
                    ticket?.caseStatus != 'Closed' && ticket?.nextFollowupDate < currentDate
                      ? 'red-bg'
                      : ''
                  "
                >
                  <td>
                    <div class="centerCheckbox">
                      <p-checkbox
                        class="p-field-checkbox"
                        [value]="ticket.isSingleTicketChecked"
                        [inputId]="ticket.caseId"
                        [(ngModel)]="ticket.isSingleTicketChecked"
                        (onChange)="addTicketCheckedData(ticket.caseId, $event)"
                        [binary]="true"
                      ></p-checkbox>
                    </div>
                  </td>
                  <td>
                    <a
                      (click)="openTicketDetail(ticket.caseId)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                    >
                      {{ ticket.caseTitle }}
                    </a>
                  </td>
                  <td>{{ ticket.caseNumber }}</td>
                  <td>{{ ticket.caseType }}</td>
                  <!-- <td>
                    <a
                      (click)="openModal('custmerDetailModal', ticket.customersId)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                    >
                      {{ ticket.userName }}
                    </a>
                  </td> -->

                  <td *ngIf="ticket.currentAssigneeId">
                    <a
                      (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                      href="javascript:void(0)"
                      style="color: #f7b206"
                    >
                      {{ ticket.currentAssigneeName }}
                    </a>
                  </td>
                  <td *ngIf="!ticket.currentAssigneeId">
                    {{ ticket.currentAssigneeName }}
                  </td>
                  <td>
                    <div *ngIf="ticket.caseStatus == 'Open'">
                      <span class="badge badge-success">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Closed'">
                      <span class="badge badge-danger">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Rejected'">
                      <span class="badge badge-danger">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Pending'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Re-Open' || ticket.caseStatus == 'Re Open'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div
                      *ngIf="
                        ticket.caseStatus == 'In-Progress' || ticket.caseStatus == 'In Progress'
                      "
                    >
                      <span class="badge badge-success">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Resolved'">
                      <span class="badge badge-success">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'On-Hold' || ticket.caseStatus == 'On Hold'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Completed'">
                      <span class="badge badge-success">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                    <div *ngIf="ticket.caseStatus == 'Out of domain'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>

                    <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>

                    <div *ngIf="ticket.caseStatus == 'Follow Up'">
                      <span class="badge badge-info">
                        {{ ticket.caseStatus }}
                      </span>
                    </div>
                  </td>
                  <!-- <td>
                    {{ ticket?.nextFollowupDate }}
                    {{ ticket?.nextFollowupTime }}
                  </td> -->
                </tr>
              </tbody>
            </table>

            <div class="pagination_Dropdown">
              <pagination-controls
                (pageChange)="pageChangedLinkTicket($event)"
                [directionLinks]="true"
                [maxSize]="10"
                id="ticketpageData"
                nextLabel=""
                previousLabel=""
              ></pagination-controls>
              <div id="itemPerPageDropdown">
                <p-dropdown
                  (onChange)="totalItemPerPageLinkTicket($event)"
                  [options]="pageLimitOptions"
                  optionLabel="value"
                  optionValue="value"
                ></p-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="addUpdateBtn">
        <button
          (click)="linkTicket()"
          class="btn btn-primary"
          style="object-fit: cover; padding: 5px 8px"
          [disabled]="chakedTktData.length === 0"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
        <button (click)="modalCloseTicket()" class="btn btn-danger btn-sm" type="button">
          Close
        </button>
      </div>
    </div>
  </p-dialog>
</div>

<p-dialog
  header="Pick Task"
  [(visible)]="ticketPickModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark*</label>
        <textarea [(ngModel)]="pickRemark" class="form-control" name="remark"></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="pickstaff()"
      [disabled]="!pickRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Pick
    </button>
    <button
      (click)="closeTicketPickModal()"
      class="btn btn-default"
      data-dismiss="modal"
      id="searchbtn"
      type="reset"
    >
      <i class="fa fa-refresh"></i>
      Clear
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Upload Document"
  [(visible)]="uploadDocumentId"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div [formGroup]="uploadDocForm" class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Select files to upload *</label>
        <input
          (change)="onFileChangeUpload($event)"
          class="form-control"
          formControlName="file"
          id="txtSelectDocument"
          multiple="multiple"
          placeholder="Select Attachment"
          style="padding: 2px; width: 100%"
          type="file"
        />
        <div *ngFor="let file of selectedFileUploadPreview; let i = index">
          <div
            style="
              padding-left: 10px;
              padding-right: 10px;
              padding-top: 4px;
              padding-bottom: 4px;
              font-size: 10px;
            "
          >
            {{ file?.name }}
            <button type="button" class="close" (click)="deletUploadedFile(file?.name)">
              &times;
            </button>
          </div>
        </div>
        <div *ngIf="submitted && uploadDocForm.controls.file.errors" class="errorWrap text-danger">
          <div
            *ngIf="submitted && uploadDocForm.controls.file.errors.required"
            class="error text-danger"
          >
            File is required.
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button (click)="uploadDocuments()" class="btn btn-primary" id="submit" type="submit">
        <i class="fa fa-check-circle"></i>
        Upload
      </button>
      <button type="button" class="btn btn-danger btn-sm" (click)="closeUploadDocumentId()">
        Close
      </button>
    </div>
  </div>
  <!-- <div class="modal-footer">
    <button
      (click)="closeUploadDocumentId()"
      class="btn btn-default"
      data-dismiss="modal"
      id="searchbtn"
      type="reset"
    >
      <i class="fa fa-refresh"></i>
      Close
    </button>
  </div> -->
</p-dialog>
<p-dialog
  header="Change Category"
  [(visible)]="showChangeProblemDomain"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"> -->
      <!-- <label>Services *</label>
        <p-multiSelect
          [options]="this.customerServiceData"
          optionValue="id"
          optionLabel="serviceName"
          [filter]="true"
          filterBy="serviceName"
          placeholder="Select a Service"
          [(ngModel)]="ticketServiceList"
          resetFilterOnHide="true"
          (onChange)="getAllTicketReasonCategory($event.value)"
        ></p-multiSelect> -->
      <!-- <div
              class="errorWrap text-danger"
              *ngIf="submitted && ticketGroupForm.controls.ticketServiceList.errors"
            >
              <div
                class="error text-danger"
                *ngIf="
                  submitted && ticketGroupForm.controls.ticketServiceList.errors.required
                "
              >
                Please select service.
              </div>
            </div> -->
      <!-- <br />
      </div> -->
      <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" *ngIf="showServices">
            <label>Services *</label>
            <input
              value="{{ ticketDeatailData.value }}"
              class="form-control"
              type="text"
              [readOnly]="true"
            />
          </div> -->
      <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Problem Domain*</label>
        <div>
          <p-dropdown
            (onChange)="selReasonCategory($event)"
            [filter]="true"
            [options]="filteredReasonCategoryList"
            filterBy="categoryName"
            [(ngModel)]="problemDomain"
            optionLabel="categoryName"
            optionValue="id"
            placeholder="Select Problem Domain"
          >
          </p-dropdown>
        </div>
        <br />
      </div> -->
      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <label>Task Category*</label>
        <div>
          <p-dropdown
            (onChange)="onParentChange($event)"
            [filter]="true"
            [options]="parentTRCData"
            filterBy="categoryName"
            [(ngModel)]="caseCategoryId"
            optionLabel="categoryName"
            optionValue="categoryId"
            placeholder="Select Task Category"
            appendTo="body"
          >
          </p-dropdown>
        </div>
        <br />
      </div>

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <label>Task Sub Category*</label>
        <div>
          <p-dropdown
            [filter]="true"
            [options]="childTRCData"
            filterBy="subCategoryName"
            [(ngModel)]="caseSubCategoryId"
            optionLabel="subCategoryName"
            optionValue="subCategoryId"
            placeholder="Select Task Sub Category"
            appendTo="body"
          >
          </p-dropdown>
        </div>
        <br />
      </div>
      <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Task Reason</label>
        <div>
          <p-dropdown
            [filter]="true"
            [options]="groupReasonData"
            filterBy="reason"
            [(ngModel)]="reasonGroup"
            optionLabel="reason"
            optionValue="id"
            placeholder="Select a Task Reason"
          ></p-dropdown>
        </div>
        <br />
      </div> -->
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark*</label>
        <textarea [(ngModel)]="pickRemark" class="form-control" name="remark"></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="changeProblemDomain()"
      [disabled]="!caseCategoryId || !caseSubCategoryId || !pickRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Change
    </button>
    <button class="btn btn-default" (click)="closeProblemDomain()">Close</button>
  </div>
</p-dialog>

<p-dialog
  header="{{ ifApproveTicket ? 'Approve Task' : 'Reject Task' }}"
  [(visible)]="ticketApproveRejectModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark</label>
        <textarea
          [(ngModel)]="approveRejectRemark"
          placeholder="Remarks"
          class="form-control"
          name="remark"
        ></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="ifApproveTicket"
      (click)="statusApporeved()"
      [disabled]="!approveRejectRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button
      *ngIf="!ifApproveTicket"
      (click)="statusRejected()"
      [disabled]="!approveRejectRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button
      class="btn btn-default"
      (click)="closeTicketApproveRejectModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Select Customer"
  [(visible)]="displaySelectCustomer"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <h5>Search Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selParentSearchOption($event)"
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect1"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption != 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchParentCustomer()"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchParentCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="reset"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Customer</h5>
    <p-table
      #dt
      [(selection)]="selectedParentCust"
      [value]="customerList"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <!-- <th>Customer Name</th> -->
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template let-customerList let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton
              [value]="customerList"
              [disabled]="customerList.status === 'Terminate'"
            ></p-tableRadioButton>
          </td>
          <!-- <td>{{ customerList.name }}</td> -->
          <td>{{ customerList.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="paginate($event)"
          [first]="newFirst"
          [rows]="parentCustomerListdataitemsPerPage"
          [totalRecords]="parentCustomerListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer(true)"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedParentCust.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Task ETR  -->
<p-dialog
  header="Task ETR"
  [(visible)]="ticketETRModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" [formGroup]="ticketETRForm">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Message Mode*</label>
        <div>
          <p-dropdown
            [options]="messageModeETR"
            optionLabel="label"
            optionValue="value"
            (onChange)="selETRmessageMode($event)"
            [filter]="true"
            filterBy="label"
            formControlName="isTemplateDynamic"
            placeholder="Select Message Mode"
          >
          </p-dropdown>
        </div>
        <div
          *ngIf="submittedETR && ticketETRForm.controls.isTemplateDynamic.errors"
          class="errorWrap text-danger"
        >
          <div
            *ngIf="submittedETR && ticketETRForm.controls.isTemplateDynamic.errors.required"
            class="error text-danger"
          >
            Message Mode is required.
          </div>
        </div>
        <br />
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Date*</label>
        <input
          class="form-control"
          placeholder="Date"
          type="date"
          formControlName="notificationDate"
          [min]="currentDt"
        />
        <div
          *ngIf="submittedETR && ticketETRForm.controls.notificationDate.errors"
          class="errorWrap text-danger"
        >
          <div
            *ngIf="submittedETR && ticketETRForm.controls.notificationDate.errors.required"
            class="error text-danger"
          >
            Date is required.
          </div>
        </div>
        <br />
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Time*</label>
        <p-calendar
          id="fromTime"
          formControlName="notificationTime"
          placeholder="Time"
          [timeOnly]="true"
          [minDate]="minTime"
        ></p-calendar>
        <div></div>
        <div
          *ngIf="submittedETR && ticketETRForm.controls.notificationTime.errors"
          class="errorWrap text-danger"
        >
          <div
            *ngIf="submittedETR && ticketETRForm.controls.notificationTime.errors.required"
            class="error text-danger"
          >
            Time is required.
          </div>
        </div>
        <br />
      </div>
      <div
        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
        *ngIf="ticketETRForm.value.isTemplateDynamic == true"
      >
        <label>Message*</label>
        <textarea formControlName="remark" class="form-control" name="remark"></textarea>
        <div
          *ngIf="submittedETR && ticketETRForm.controls.remark.errors"
          class="errorWrap text-danger"
        >
          <div
            *ngIf="submittedETR && ticketETRForm.controls.remark.errors.required"
            class="error text-danger"
          >
            Message Mode is required.
          </div>
        </div>
        <br />
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="display: flex">
        <label>Notification Type*</label>
        <div style="margin-left: 1.5rem">
          <input type="checkbox" formControlName="sms" />
          <span style="margin-left: 0.5rem">SMS</span>
        </div>
        <div style="margin-left: 1.5rem">
          <input type="checkbox" formControlName="email" />
          <span style="margin-left: 0.5rem">Email</span>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="ETRSaveData()"
      [disabled]="!this.ticketETRForm.value.sms && !this.ticketETRForm.value.email"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>
    <button (click)="closeETRModel()" class="btn btn-default" id="searchbtn" type="reset">
      <i class="fa fa-refresh"></i>
      Clear
    </button>
    <button (click)="closeeETRModel()" class="btn btn-default" id="searchbtn" type="reset">
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="TAT Details"
  [(visible)]="displayTATDetails"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div>
      <div class="row">
        <div class="col-md-12">
          <span>Tat name : </span>
          <span *ngFor="let data of this.TATDetails">
            <a (click)="openRawDataModal(data)" href="javascript:void(0)" style="color: #f7b206"
              >{{ data.ticketTatMatrix.name }} ,
            </a></span
          >
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeDisplayTATDetails()"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Document Preview"
  [(visible)]="documentPreview"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="height: 600px">
    <iframe [src]="previewUrl" width="100%" height="100%" allowfullscreen></iframe>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeDocumentPreview()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Team Details"
  [(visible)]="ticketStaffTeamdetails"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <table class="border">
      <tr style="width: 100% !important">
        <td>
          <div *ngFor="let data of customerETRDetailData">
            {{ data }}
          </div>
        </td>
        <!-- <td style=" height: 10px;"><div style="col-lg-4; dataGroup">{{ data }}</div> </td> -->
      </tr>
    </table>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeTicketStaffTeamdetails()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<!-- Follow up schedulling popup start -->
<p-dialog
  header="Schedule a followup"
  [(visible)]="followupPopupOpen"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="height: 45rem">
    <form [formGroup]="followupScheduleForm">
      <label style="font-weight: bold"> Follow Up Name * </label>
      <input
        disabled
        type="text"
        class="form-control"
        placeholder="Enter the follow up name"
        formControlName="followUpName"
        [ngClass]="{
          'is-invalid': ifCafFollowupSubmited && followupScheduleForm.controls.followUpName.errors
        }"
      />
      <div
        class="error text-danger"
        *ngIf="ifCafFollowupSubmited && followupScheduleForm.controls.followUpName.errors"
      >
        Name is required.
      </div>
      <br />
      <label style="font-weight: bold">Follow Up Date & Time *</label>
      <p-calendar
        formControlName="followUpDatetime"
        [showTime]="true"
        [showSeconds]="true"
        inputId="time"
        [numberOfMonths]="2"
        [minDate]="dateTime"
        appendTo="body"
        [showIcon]="true"
        [ngClass]="{
          'is-invalid':
            ifCafFollowupSubmited && this.followupScheduleForm.controls.followUpDatetime.errors
        }"
      >
      </p-calendar>
      <div
        class="error text-danger"
        *ngIf="ifCafFollowupSubmited && followupScheduleForm.controls.followUpDatetime.errors"
      >
        Date & Time is required.
      </div>
      <br /><br />
      <label style="font-weight: bold">Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarks"
        [ngClass]="{
          'is-invalid': ifCafFollowupSubmited && this.followupScheduleForm.controls.remarks.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="ifCafFollowupSubmited && this.followupScheduleForm.controls.remarks.errors"
      >
        Remarks is required.
      </div>
      <!-- <br />
                    <label style="font-weight: bold">Status*</label>
                    <p-dropdown [options]="this.status" formControlName="status" optionLabel="label" optionValue="value"
                        filter="true" filterBy="label" placeholder="Select follow up status">
                    </p-dropdown>
                    <div class="errorWrap text-danger" *ngIf="ifCafFollowupSubmited && followupScheduleForm.controls.status.errors">
                        <div class="error text-danger" *ngIf="ifCafFollowupSubmited && followupScheduleForm.controls.status.errors.required">Status is required.
                        </div>
                   </div> -->
      <br />
      <!-- <div class="addUpdateBtn">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveCafFollowup()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          Schedule
        </button>
      </div> -->
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        type="submit"
        class="btn btn-primary"
        id="submit"
        (click)="saveCafFollowup()"
        [disabled]="this.showQtyError"
      >
        <i class="fa fa-check-circle"></i>
        Schedule
      </button>
      <button
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="closeFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Follow up close popup start -->
<p-dialog
  header="Close Followup"
  [(visible)]="closeFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="closeFollowupForm">
      <label style="font-weight: bold">Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarks"
        [ngClass]="{
          'is-invalid': closeFollowupFormsubmitted && this.closeFollowupForm.controls.remarks.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="closeFollowupFormsubmitted && this.closeFollowupForm.controls.remarks.errors"
      >
        Remarks is required.
      </div>
      <br />
      <div class="addUpdateBtn">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveCloseFollowUp()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeActionFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
<!-- Follow up close popup end -->

<!-- Follow up reschedulling popup start -->
<p-dialog
  header="Re-Schedule a followup"
  [(visible)]="reScheduleFollowup"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="reFollowupScheduleForm">
      <label style="font-weight: bold">Current Follow Up Close Remarks *</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the Remarks"
        formControlName="remarksTemp"
        [ngClass]="{
          'is-invalid':
            reFollowupFormsubmitted && this.reFollowupScheduleForm.controls.remarksTemp.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="reFollowupFormsubmitted && this.reFollowupScheduleForm.controls.remarksTemp.errors"
      >
        Remarks is required.
      </div>
      <br />
      <label style="font-weight: bold"> Re-Schedule Follow Up Name * </label>
      <input
        disabled
        type="text"
        class="form-control"
        placeholder="Enter the follow up name"
        formControlName="followUpName"
        [ngClass]="{
          'is-invalid':
            reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpName.errors
        }"
      />
      <div
        class="error text-danger"
        *ngIf="reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpName.errors"
      >
        ReSchedule ReSchedule Name is required.
      </div>
      <br />
      <label style="font-weight: bold">Re-Schedule Follow Up Date & Time *</label>
      <p-calendar
        formControlName="followUpDatetime"
        [showTime]="true"
        [showSeconds]="true"
        inputId="time"
        [numberOfMonths]="3"
        [minDate]="dateTime"
        [showIcon]="true"
        appendTo="body"
        [ngClass]="{
          'is-invalid':
            reFollowupFormsubmitted && this.reFollowupScheduleForm.controls.followUpDatetime.errors
        }"
      >
      </p-calendar>
      <div
        class="error text-danger"
        *ngIf="reFollowupFormsubmitted && reFollowupScheduleForm.controls.followUpDatetime.errors"
      >
        Re-Schedule Date & Time is required.
      </div>
      <br /><br />
      <label style="font-weight: bold">Re-Schedule Remarks</label>
      <!-- <textarea
            type="text"
            class="form-control"
            placeholder="Enter the Remarks"
            formControlName="remarks"
          >
          rescheduleRemarks
          </textarea> -->
      <p-dropdown
        [options]="rescheduleFollowupRemarks"
        placeholder="Select the specific remark"
        formControlName="remarks"
      >
      </p-dropdown>
      <br />
      <div class="addUpdateBtn">
        <br />
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="saveReFollowup()"
          [disabled]="this.showQtyError"
        >
          <i class="fa fa-check-circle"></i>
          Re-Schedule
        </button>
      </div>
    </form>
  </div>
  <div class="modal-footer" style="overflow: visible; display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        class="btn btn-danger btn-sm"
        #closebutton
        data-dismiss="modal"
        (click)="closeReFolloupPopup()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
<!-- Follow up reschedulling popup end -->

<!-- remarkScheduleFollowup popup end start -->
<p-dialog
  header="Remarks Followup"
  [(visible)]="remarkScheduleFollowup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeRemarkPopup()"
>
  <div class="modal-body">
    <form [formGroup]="remarkFollowupForm">
      <label style="font-weight: bold">Remarks</label>
      <textarea
        type="text"
        class="form-control"
        placeholder="Enter the remark"
        formControlName="remark"
        [ngClass]="{
          'is-invalid':
            remarkFollowupFormsubmitted && this.remarkFollowupForm.controls.remark.errors
        }"
      >
      </textarea>
      <div
        class="error text-danger"
        *ngIf="remarkFollowupFormsubmitted && this.remarkFollowupForm.controls.remark.errors"
      >
        Remarks is required.
      </div>
      <br />
      <div class="addUpdateBtn">
        <button type="submit" class="btn btn-primary" id="submit" (click)="saveRemarkFollowUp()">
          <i class="fa fa-check-circle"></i>
          Save
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body">
    <h3 class="panel-title">Remarks List</h3>
    <hr />
    <div [id]="tableWrapperRemarks">
      <div [id]="scrollIdRemarks">
        <table class="table">
          <thead>
            <tr>
              <th>Remarks</th>
              <th>Created On</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let remarkDetails of followUpRemarkList">
              <td>{{ remarkDetails?.remark }}</td>
              <td>{{ remarkDetails?.createdOn | date: "dd/MM/yyyy HH:mm:ss" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeRemarkPopup()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<div *ngIf="this.viewTrcData">
  <p-dialog
    header="Remarks Followup"
    [(visible)]="tatDetailsShow"
    [style]="{ width: '50%' }"
    [modal]="true"
    [responsive]="true"
    [draggable]="false"
    [closable]="true"
    (onHide)="closeRemarkPopup()"
  >
    <div class="modal-body">
      <div class="col-md-12 col-sm-12">
        <div class="panel">
          <div id="trcData" class="panel-collapse collapse in">
            <div class="panel-body table-responsive">
              <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
                <legend>Basic Details</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">Name :</label>
                      <span>{{ viewTrcData.name }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">Status :</label>
                      <span *ngIf="viewTrcData.status == 'Active'" class="badge badge-success">
                        Active
                      </span>
                      <span
                        *ngIf="
                          viewTrcData.status == 'Inactive' ||
                          viewTrcData.status == 'InActive' ||
                          viewTrcData.status == 'inactive'
                        "
                        class="badge badge-danger"
                      >
                        Inactive
                      </span>
                    </div>

                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">Response Time:</label>
                      <span>{{ viewTrcData.rtime }} {{ viewTrcData.runit }}</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">SLA Time P1 :</label>
                      <span>{{ viewTrcData.slaTimep1 }} {{ viewTrcData.sunitp1 }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">SLA Time P2 :</label>
                      <span>{{ viewTrcData.slaTimep2 }} {{ viewTrcData.sunitp2 }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 dataGroup">
                      <label class="datalbl">SLA Time P3 :</label>
                      <span>{{ viewTrcData.slaTime3 }} {{ viewTrcData.sunitp3 }}</span>
                    </div>
                  </div>
                </div>
              </fieldset>
              <fieldset *ngIf="viewTrcData.tatMatrixMappings.length !== 0">
                <legend>TAT Mapping Detail</legend>
                <div class="boxWhite">
                  <div class="row table-responsive">
                    <div class="col-lg-12 col-md-12">
                      <table class="table">
                        <thead>
                          <tr>
                            <th>Order</th>
                            <th>Level</th>
                            <th>TAT time for P1</th>
                            <th>TAT time for P2</th>
                            <th>TAT time for P3</th>
                            <th>Unit</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="
                              let tatData of viewTrcData.tatMatrixMappings
                                | paginate
                                  : {
                                      id: 'tatPageData',
                                      itemsPerPage: viewTATListdataitemsPerPage,
                                      currentPage: currentPageViewTATListdata,
                                      totalItems: viewTATListDatatotalRecords
                                    };
                              index as i
                            "
                          >
                            <td>{{ tatData.orderNo }}</td>
                            <td>{{ tatData.level }}</td>
                            <td>{{ tatData.mtime1 }}</td>
                            <td>{{ tatData.mtime2 }}</td>
                            <td>{{ tatData.mtime3 }}</td>
                            <td>{{ tatData.munit }}</td>
                            <td>{{ tatData.action }}</td>
                          </tr>
                        </tbody>
                      </table>
                      <pagination-controls
                        id="tatPageData"
                        [maxSize]="10"
                        [directionLinks]="true"
                        previousLabel=""
                        nextLabel=""
                        (pageChange)="pageChangedViewTAT($event)"
                      >
                      </pagination-controls>
                    </div>
                  </div>
                </div>
              </fieldset>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer" style="display: flex; justify-content: flex-end">
      <div class="addUpdateBtn">
        <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeRemarkPopup()">
          Close
        </button>
      </div>
    </div>
  </p-dialog>
</div>

<p-dialog
  header="TAT Details"
  [(visible)]="tatDetailsMessageShow"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeRemarkPopup()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div id="trcData" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset>
              <legend>TAT Message Detail</legend>
              <div class="boxWhite">
                <div class="row table-responsive">
                  <div class="col-lg-12 col-md-12">
                    <div [innerHTML]="viewTATMessage">
                      {{ viewTATMessage }}
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeRemarkPopup()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!--TAT Detail Model -->
<p-dialog
  header="TAT Details"
  [(visible)]="tatDetailsShowModel"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  (onHide)="closeTATModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div id="trcDataModel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Name :</label>
                    <span>{{ TATcaseData.name }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Status :</label>
                    <span *ngIf="TATcaseData.status == 'Active'" class="badge badge-success">
                      Active
                    </span>
                    <span
                      *ngIf="
                        TATcaseData.status == 'Inactive' ||
                        TATcaseData.status == 'InActive' ||
                        TATcaseData.status == 'inactive'
                      "
                      class="badge badge-danger"
                    >
                      Inactive
                    </span>
                  </div>

                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Response Time:</label>
                    <span>{{ TATcaseData.rtime }} {{ TATcaseData.runit }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P1 :</label>
                    <span>{{ TATcaseData.slaTimep1 }} {{ TATcaseData.sunitp1 }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P2 :</label>
                    <span>{{ TATcaseData.slaTimep2 }} {{ TATcaseData.sunitp2 }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P3 :</label>
                    <span>{{ TATcaseData.slaTime3 }} {{ TATcaseData.sunitp3 }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset>
              <legend>TAT Mapping Detail</legend>
              <div class="boxWhite">
                <div class="row table-responsive">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Order</th>
                          <th>Level</th>
                          <th>TAT time for P1</th>
                          <th>TAT time for P2</th>
                          <th>TAT time for P3</th>
                          <th>Unit</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let tatData of TATcaseData.tatMatrixMappings">
                          <td>{{ tatData.orderNo }}</td>
                          <td>{{ tatData.level }}</td>
                          <td>{{ tatData.mtime1 }}</td>
                          <td>{{ tatData.mtime2 }}</td>
                          <td>{{ tatData.mtime3 }}</td>
                          <td>{{ tatData.munit }}</td>
                          <td>{{ tatData.action }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeTATModel()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Helper List"
  [(visible)]="helperDetailsShowModel"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  (onHide)="closeHelperModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <h2 class="h2header">Helper Name List</h2>
      <div class="list-container">
        <div class="grid-list">
          <div *ngFor="let helper of HelperList" class="grid-item">
            {{ helper }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeHelperModel()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!--Parent Detail Model -->
<p-dialog
  header="Parent Task Details"
  [(visible)]="parentDetailsShowModel"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeParentTicketModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div id="trcDataModel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset>
              <legend>Task Detail</legend>
              <div class="boxWhite">
                <div class="row table-responsive">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Number</th>
                          <th>Type</th>
                          <th>Customer</th>

                          <th>Assignee</th>
                          <th>Status</th>
                          <!-- <th>Followup Date & Time</th> -->
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>
                            {{ parentTicketDetails.caseTitle }}
                          </td>
                          <td>{{ parentTicketDetails.caseNumber }}</td>
                          <td>{{ parentTicketDetails.caseType }}</td>
                          <td>
                            {{ parentTicketDetails.userName }}
                          </td>

                          <td *ngIf="parentTicketDetails.currentAssigneeId">
                            {{ parentTicketDetails.currentAssigneeName }}
                          </td>
                          <td *ngIf="!parentTicketDetails.currentAssigneeId">
                            {{ parentTicketDetails.currentAssigneeName }}
                          </td>
                          <td>
                            <div *ngIf="parentTicketDetails.caseStatus == 'Open'">
                              <span class="badge badge-success">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="parentTicketDetails.caseStatus == 'Closed'">
                              <span class="badge badge-danger">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="parentTicketDetails.caseStatus == 'Pending'">
                              <span class="badge badge-info">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                parentTicketDetails.caseStatus == 'Re-Open' ||
                                parentTicketDetails.caseStatus == 'Re Open'
                              "
                            >
                              <span class="badge badge-info">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                parentTicketDetails.caseStatus == 'In-Progress' ||
                                parentTicketDetails.caseStatus == 'In Progress'
                              "
                            >
                              <span class="badge badge-success">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="parentTicketDetails.caseStatus == 'Resolved'">
                              <span class="badge badge-success">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                parentTicketDetails.caseStatus == 'On-Hold' ||
                                parentTicketDetails.caseStatus == 'On Hold'
                              "
                            >
                              <span class="badge badge-info">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="parentTicketDetails.caseStatus == 'Completed'">
                              <span class="badge badge-success">
                                {{ parentTicketDetails.caseStatus }}
                              </span>
                            </div>
                          </td>
                          <!-- <td>
                            {{ parentTicketDetails.nextFollowupDate }}
                            {{ parentTicketDetails.nextFollowupTime }}
                          </td> -->
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeParentTicketModel()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!--Child Detail Model -->
<p-dialog
  header="Child Task Details"
  [(visible)]="childDetailsShowModel"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeParentTicketModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div id="trcDataModel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset>
              <legend>Task Detail</legend>
              <div class="boxWhite">
                <div class="row table-responsive">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Number</th>
                          <th>Type</th>
                          <!-- <th>Customer Name</th> -->

                          <th>Assignee</th>
                          <th>Status</th>
                          <!-- <th>Followup Date & Time</th> -->
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let tatData of childTicketDetails">
                          <td>
                            {{ tatData.caseTitle }}
                          </td>
                          <td>{{ tatData.caseNumber }}</td>
                          <td>{{ tatData.caseType }}</td>
                          <!-- <td>
                            {{ tatData.userName }}
                          </td> -->

                          <td *ngIf="tatData.currentAssigneeId">
                            {{ tatData.currentAssigneeName }}
                          </td>
                          <td *ngIf="!tatData.currentAssigneeId">
                            {{ tatData.currentAssigneeName }}
                          </td>
                          <td>
                            <div *ngIf="tatData.caseStatus == 'Open'">
                              <span class="badge badge-success">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="tatData.caseStatus == 'Closed'">
                              <span class="badge badge-danger">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="tatData.caseStatus == 'Pending'">
                              <span class="badge badge-info">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                tatData.caseStatus == 'Re-Open' || tatData.caseStatus == 'Re Open'
                              "
                            >
                              <span class="badge badge-info">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                tatData.caseStatus == 'In-Progress' ||
                                tatData.caseStatus == 'In Progress'
                              "
                            >
                              <span class="badge badge-success">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="tatData.caseStatus == 'Resolved'">
                              <span class="badge badge-success">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div
                              *ngIf="
                                tatData.caseStatus == 'On-Hold' || tatData.caseStatus == 'On Hold'
                              "
                            >
                              <span class="badge badge-info">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                            <div *ngIf="tatData.caseStatus == 'Completed'">
                              <span class="badge badge-success">
                                {{ tatData.caseStatus }}
                              </span>
                            </div>
                          </td>
                          <!-- <td>
                            {{ tatData.nextFollowupDate }}
                            {{ tatData.nextFollowupTime }}
                          </td> -->
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeParentTicketModel()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="SLA Counter"
  [(visible)]="counterDetailModel"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeCounterDetailModel()"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <table>
          <tr>
            <th style="text-align: center; width: 10%">
              <strong style="font-size: 30px">{{ this.SLAremainTime }}</strong>
            </th>
          </tr>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer" class="btn btn-default" (click)="closeCounterDetailModel()">Close</div>
</p-dialog>

<p-dialog
  header="Service Area"
  [(visible)]="serviceAreaDetail"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeServiceAreaDetail()"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <table>
          <tr *ngFor="let data of serviceAreaList">
            {{
              data
            }}
          </tr>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeServiceAreaDetail()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<!-- Feedback Model -->
<p-dialog
  header="{{
    ticketDeatailData.department === 'Sales' ? 'Sales Feedback Form' : 'Technical Feedback Form'
  }}"
  [(visible)]="feedbackFormModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeFeedbackFormModal()"
>
  <div class="modal-body">
    <form [formGroup]="feedbackForm">
      <div class="form-group">
        <div class="row">
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Sales Support Through</label>
            <div>
              <p-dropdown
                [options]="salesSupportData"
                formControlName="support_type"
                placeholder="Select staff Behaviour"
                optionLabel="label"
                optionValue="value"
              ></p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Staff Behaviour Feedback</label>
            <div>
              <p-dropdown
                (onChange)="getStaffBehaviourFeedback($event.value)"
                [filter]="true"
                [options]="staffBehaviourData"
                filterBy="text"
                formControlName="staff_behavior"
                placeholder="Select staff Behaviour"
              ></p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Payment Mode</label>
            <div>
              <p-dropdown
                (onChange)="getPaymentMode($event.value)"
                [filter]="true"
                [options]="paymentModeData"
                filterBy="text"
                formControlName="payment_mode"
                placeholder="Select Payment Mode"
              ></p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Information of Payment Mode</label>
            <div>
              <p-multiSelect
                [filter]="true"
                [options]="infoOfPaymentModeData"
                filterBy="rootCauseReason"
                formControlName="infoOfPaymentMode"
                optionLabel="label"
                optionValue="value"
                resetFilterOnHide="true"
                placeholder="Select Information of Payment Mode"
              >
              </p-multiSelect>
            </div>

            <br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <div>
              <label>Current Bandwidth Feedback</label>
              <p-dropdown
                [filter]="true"
                [options]="staffBehaviourData"
                filterBy="text"
                formControlName="current_bandwidth_feedback"
                placeholder="Select current Bandwidth"
              ></p-dropdown>
            </div>
            <br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Current Price Feedback</label>
            <p-dropdown
              (onChange)="getPaymentMode($event.value)"
              [filter]="true"
              [options]="staffBehaviourData"
              formControlName="current_price_feedback"
              placeholder="Select Current Price"
            ></p-dropdown>
            <div></div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Referal Information</label>
            <div>
              <p-dropdown
                [options]="referalInfoData"
                optionValue="label"
                optionLabel="value"
                filter="true"
                placeholder="Please enter Referal Information "
                formControlName="referal_information"
              >
              </p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Sales'"
          >
            <label>Technical Support Feedback</label>
            <div>
              <p-dropdown
                [options]="staffBehaviourData"
                filter="true"
                placeholder="Please enter Technical Support Feedback "
                formControlName="technicial_support_feedback"
              >
              </p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Technical'"
          >
            <label>Service Experience</label>
            <div>
              <p-dropdown
                (onChange)="getServiceExperience($event.value)"
                [options]="staffBehaviourData"
                filter="true"
                placeholder="Please enter Service Experience"
                formControlName="service_experience"
              >
              </p-dropdown>
            </div>
            <br />
          </div>

          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Technical' && isProblemType"
          >
            <label>Problem Type</label>
            <div>
              <p-multiSelect
                [options]="problemTypeData"
                filter="true"
                placeholder="Please enter problem type"
                formControlName="problem_type"
                resetFilterOnHide="true"
              >
              </p-multiSelect>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Technical'"
          >
            <label>Behaviour Professionalism</label>
            <div>
              <p-dropdown
                (onChange)="getbehaviourPro($event.value)"
                [options]="staffBehaviourData"
                filter="true"
                placeholder="Please enter Behaviour Professionalism"
                formControlName="behaviour_professionalism"
              >
              </p-dropdown>
            </div>
            <br />
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="ticketDeatailData.department == 'Technical' && isEnable"
          >
            <label>Reason for Satisfaction/Unsatisfaction</label>
            <div>
              <p-multiSelect
                [options]="BehaviourReasonData"
                filter="true"
                placeholder="Please enter reason for Satisfaction/Unsatisfaction"
                formControlName="reason"
                resetFilterOnHide="true"
              >
              </p-multiSelect>
            </div>
            <br />
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label class="datalbl">Rating :</label>
            <p-rating [cancel]="false" formControlName="overall_rating"></p-rating>
            <br />
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>General Remark</label>
            <textarea
              class="form-control"
              formControlName="general_remarks"
              name="remark"
              rows="5"
            ></textarea>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="this.changeStatusSingleMultiple == 'pTicket'"
      (click)="saveFeedback(ticketDeatailData.caseId)"
      class="btn btn-primary"
      data-dismiss="modal"
      id="submit"
      type="submit"
      [disabled]="!this.feedbackForm.valid"
    >
      <i class="fa fa-check-circle"></i>
      Submit
    </button>
    <!-- <button
          *ngIf="this.changeStatusSingleMultiple == 'mTicket'"
          (click)="saveTechnicalFeedback()"
          class="btn btn-primary"
          data-dismiss="modal"
          id="submit"
          type="submit"
          [disabled]="!this.feedbackForm.valid"
        >
          <i class="fa fa-check-circle"></i>
          Submit
        </button> -->

    <button
      (click)="closeFeedbackFormModal()"
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<!-- Task Conversation Model -->
<p-dialog
  header="Task Conversations"
  [(visible)]="conversationModal"
  [modal]="true"
  [style]="{ width: '60vw' }"
  [draggable]="false"
  [resizable]="false"
  [responsive]="true"
  [closable]="true"
  (onHide)="closeConversation()"
>
  <div class="modal-body-ticket">
    <div class="form-group">
      <br />
      <div class="row" style="height: 28vw; overflow: auto">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div
            *ngFor="let remarkData of conversationListData; let mi = index"
            class="msg"
            [ngClass]="remarkData.isFromCustomer == false ? 'msg-right' : 'msg-left'"
          >
            <div class="bubble" *ngIf="remarkData.remark != null">
              <span class="msg_name_send">{{
                remarkData.isFromCustomer == true
                  ? remarkData.customersName
                  : remarkData.staffUserName
              }}</span>
              <div class="remark">{{ remarkData.remark }}</div>
              <span class="msg_time_send">{{ remarkData?.remarkDate | date: "short" }} </span>
            </div>
          </div>
          <div *ngIf="conversationListData.length == 0">No Data Available</div>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="modal-footer">
    <div class="addUpdateBtn">
      <button class="btn btn-default" (click)="closeConversation()" type="button">Close</button>
    </div>
  </div> -->
</p-dialog>

<p-dialog
  header="Reassign Task in Bulk"
  [(visible)]="reassignTicketModal"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeReassign()"
>
  <div class="modal-body">
    <form [formGroup]="reassignStaffTicketForm">
      <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Team*</label>
          <div>
            <p-dropdown
              [ngClass]="{
                'is-invalid': staffsubmmitted && reassignStaffTicketForm.controls.teamId.errors
              }"
              [options]="teamListData"
              [showClear]="true"
              formControlName="teamId"
              [filter]="true"
              filterBy="name"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Team"
              (onChange)="onSelectTeam($event)"
              appendTo="body"
            ></p-dropdown>
          </div>
          <div
            *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.teamId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.teamId.errors.required"
              class="error text-danger"
            >
              Team is required.
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Staff</label>
          <div>
            <!--  [ngClass]="{
                'is-invalid': staffsubmmitted && reassignStaffTicketForm.controls.staffId.errors
              }" -->
            <p-dropdown
              [options]="staffDataList"
              formControlName="staffId"
              [filter]="true"
              filterBy="name"
              optionLabel="displayName"
              optionValue="id"
              placeholder="Select a Staff"
              appendTo="body"
            ></p-dropdown>
            <!-- (onChange)="onSelectStaff($event)" -->
          </div>
          <!-- <div
            *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.staffId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.staffId.errors.required"
              class="error text-danger"
            >
              Staff is required.
            </div>
          </div> -->
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': staffsubmmitted && reassignStaffTicketForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && reassignStaffTicketForm.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="reassignTicketSubmit()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Reassign Staff
    </button>
    <button class="btn btn-default" data-dismiss="modal" type="button" (click)="closeReassign()">
      Close
    </button>
  </div>
</p-dialog>

<app-customer-details
  *ngIf="dialogId"
  (closeSelectStaff)="closeSelectStaff()"
  [custId]="custId"
></app-customer-details>

<!-- Assign Team or Staff -->
<!-- <p-dialog
  header="Assign Staff"
  [(visible)]="assignTeamStaffModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
  <form [formGroup]="assignTeamStaffTicketForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Staff*</label>
          <div>
            <p-dropdown
              [ngClass]="{
                'is-invalid': staffsubmmitted && assignStaffTicketForm.controls.staffId.errors
              }"
              [options]="assignableStaffList"
              formControlName="staffId"
              optionLabel="fullName"
              optionValue="id"
              placeholder="Please select staff."
            ></p-dropdown>
          </div>
          <div
            *ngIf="staffsubmmitted && assignStaffTicketForm.controls.staffId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && assignStaffTicketForm.controls.staffId.errors.required"
              class="error text-danger"
            >
              Staff is required.
            </div>
          </div>
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': staffsubmmitted && assignStaffTicketForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="staffsubmmitted && assignStaffTicketForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="staffsubmmitted && assignStaffTicketForm.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToStaffTicket(true)"
      *ngIf="approved && selectStaff"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeAssignStaffModel()"
    >
      Close
    </button>
  </div>
</p-dialog> -->
<p-dialog
  header="TAT Details"
  [(visible)]="showTatDetails"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  (onHide)="closeTATModel()"
>
  <div class="modal-body">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div id="trcDataModel" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Name :</label>
                    <span>{{ TatDetails.name }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Status :</label>
                    <span *ngIf="TatDetails.status == 'Active'" class="badge badge-success">
                      Active
                    </span>
                    <span
                      *ngIf="
                        TatDetails.status == 'Inactive' ||
                        TatDetails.status == 'InActive' ||
                        TatDetails.status == 'inactive'
                      "
                      class="badge badge-danger"
                    >
                      Inactive
                    </span>
                  </div>

                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Response Time:</label>
                    <span>{{ TatDetails.rtime }} {{ TatDetails.runit }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P1 :</label>
                    <span>{{ TatDetails.slaTimep1 }} {{ TatDetails.sunitp1 }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P2 :</label>
                    <span>{{ TatDetails.slaTimep2 }} {{ TatDetails.sunitp2 }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">SLA Time P3 :</label>
                    <span>{{ TatDetails.slaTime3 }} {{ TatDetails.sunitp3 }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset>
              <legend>TAT Mapping Detail</legend>
              <div class="boxWhite">
                <div class="row table-responsive">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Order</th>
                          <th>Level</th>
                          <th>TAT time for P1</th>
                          <th>TAT time for P2</th>
                          <th>TAT time for P3</th>
                          <th>Unit</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let tatData of TatDetails.tatMatrixMappings">
                          <td>{{ tatData.orderNo }}</td>
                          <td>{{ tatData.level }}</td>
                          <td>{{ tatData.mtime1 }}</td>
                          <td>{{ tatData.mtime2 }}</td>
                          <td>{{ tatData.mtime3 }}</td>
                          <td>{{ tatData.munit }}</td>
                          <td>{{ tatData.action }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button class="btn btn-danger btn-sm" data-dismiss="modal" (click)="closeTAT()">Close</button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Upload Document"
  class="second-tabView"
  [(visible)]="uploadResolvedocumentId"
  [style]="{ width: '70%' }"
  [modal]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeUploadResolveDocumentId()"
>
  <p-tabView [(activeIndex)]="activeTabIndex" (onChange)="onTabChange($event)">
    <p-tabPanel *ngFor="let tab of tabs; let tabIndex = index" [header]="tab">
      <ng-container *ngTemplateOutlet="uploadForm; context: { tabIndex: tabIndex }"></ng-container>
    </p-tabPanel>
  </p-tabView>

  <ng-template #uploadForm let-tabIndex="tabIndex">
    <div [formGroup]="uploadResolveDocForm[tabIndex]" class="modal-body">
      <div class="row g-3 gy-3">
        <!-- First Row -->
        <div class="col-12 col-md-3">
          <label>Section Name</label>
          <input
            formControlName="sectionName"
            class="form-control"
            placeholder="Enter Section Name"
            [value]="tabs[tabIndex]"
            disabled
          />
        </div>
        <div *ngIf="tabs[tabIndex] !== 'Optical Power Range'" class="col-12 col-md-3">
          <label>Latitude</label>
          <input formControlName="latitude" class="form-control" placeholder="Enter Latitude" />
        </div>
        <div *ngIf="tabs[tabIndex] !== 'Optical Power Range'" class="col-12 col-md-3">
          <label>Longitude</label>
          <input formControlName="longitude" class="form-control" placeholder="Enter Longitude" />
        </div>
        <div
          *ngIf="tabs[tabIndex] !== 'Optical Power Range'"
          class="col-12 col-md-3 d-flex align-items-center"
        >
          <span
            class="HoverEffect"
            (click)="mylocation()"
            title="Get Current Location"
            style="border-bottom: 1px solid #f7b206; cursor: pointer"
          >
            <img
              class="LocationIcon LocationIconMargin"
              src="assets/img/B_Find-My-current-location_Y.png"
            />
          </span>
        </div>
        <div
          *ngIf="tabs[tabIndex] === 'Optical Power Range'"
          class="col-12 col-md-3 d-flex align-items-center"
        >
          <label>Optical Power range(In db) *</label>
          <p-dropdown
            id="title"
            [options]="opticalRangeData"
            filter="true"
            filterBy="label"
            formControlName="opticalRange"
            optionLabel="label"
            optionValue="value"
            placeholder="Select an Optical Power Range"
            appendTo="body"
          ></p-dropdown>
          <!-- <input formControlName="opticalRange" class="form-control" placeholder="Enter Optical Range" /> -->
        </div>
      </div>

      <!-- Add spacing between rows -->
      <br />
      <!-- Second Row -->
      <div class="row g-3 gy-3">
        <div class="col-12 col-md-6" *ngIf="tabs[tabIndex] !== 'Optical Power Range'">
          <div *ngIf="tabsMandatory && tabsMandatory.length > 0">
            <label>
              Select files to upload
              <span *ngIf="tabsMandatory[tabIndex]" class="text-danger">*</span>
            </label>
          </div>
          <input
            (change)="onFileResolveChangeUpload($event, tabIndex)"
            class="form-control"
            formControlName="file"
            id="txtSelectDocument"
            multiple
            placeholder="Select Attachment"
            style="padding: 2px"
            type="file"
          />

          <div
            *ngFor="let file of selectedResolveFileUploadPreview[tabIndex]; let i = index"
            class="mt-2 d-flex justify-content-between align-items-center bg-light p-2"
          >
            <span style="font-size: 12px">{{ file?.name }}</span>
            <button
              type="button"
              class="btn-close"
              (click)="deleteResolveUploadedFile(file?.name, tabIndex)"
            ></button>
          </div>
          <div *ngIf="tabsMandatory && tabsMandatory.length > 0">
            <div
              *ngIf="resolvesubmitted && uploadResolveDocForm[tabIndex].controls.file.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="uploadResolveDocForm[tabIndex].controls.file.errors.required"
                class="error text-danger"
              >
                File is required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <div class="modal-footer d-flex justify-content-between">
    <button (click)="uploadResolveAllDocuments()" class="btn btn-primary" id="submit" type="button">
      <i class="fa fa-check-circle"></i> Upload All Documents
    </button>
    <button type="button" class="btn btn-danger" (click)="closeUploadResolveDocumentId()">
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="View Document"
  class="second-tabView"
  [(visible)]="downloadResolveDocumentId"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeDownloadResolveDocumentId()"
>
  <p-tabView [(activeIndex)]="activeTabViewIndex">
    <p-tabPanel *ngFor="let tab of tabs" [header]="tab">
      <ng-container *ngIf="hasFilesForTab(tab)">
        <ng-container *ngFor="let section of ticketFileDocData">
          <ng-container *ngIf="section?.sectionName === tab">
            <ng-container *ngIf="section?.sectionName === 'Optical Power Range'; else showTable">
              <p>Optical Power Range: {{ section?.fileDetails[0].opticalRange }}</p>
            </ng-container>

            <ng-template #showTable>
              <p-table
                *ngIf="section?.fileDetails?.length > 0"
                [value]="section?.fileDetails"
                [paginator]="true"
                [rows]="5"
                [rowsPerPageOptions]="[5, 10, 20]"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th>File Name</th>
                    <th>Latitude</th>
                    <th>Longitude</th>
                    <th>Action</th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-file>
                  <tr>
                    <td>{{ file.fileName }}</td>
                    <td>{{ file.latitude }}</td>
                    <td>{{ file.longitude }}</td>
                    <td>
                      <button
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        id="download-button"
                        (click)="downloadResolveDoc(file.fileName, file, section?.sectionName)"
                        title="Download Document"
                        class="btn btn-primary"
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/pdf.png"
                        />
                      </button>

                      <button
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        id="view-button"
                        title="View Document"
                        class="btn btn-primary"
                        (click)="
                          showticketResolveDocData(file.fileName, file, section?.sectionName)
                        "
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/eye-icon.png"
                        />
                      </button>

                      <button
                        style="
                          border-radius: 5px;
                          padding: 3px 8px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        class="btn btn-primary"
                        title="Remove File"
                        id="delete-button"
                        (click)="deleteResolveConfirm(file.fileName, file, section?.sectionName)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </button>
                    </td>
                  </tr>
                </ng-template>
              </p-table>

              <div *ngIf="section?.fileDetails?.length === 0">
                <p>No files available for this section.</p>
              </div>
            </ng-template>
          </ng-container>
        </ng-container>
      </ng-container>
      <div *ngIf="!hasFilesForTab(tab)">
        <p>No files available for this tab.</p>
      </div>
    </p-tabPanel>
    <div class="modal-footer">
      <div class="closeBtn">
        <button
          class="btn btn-default"
          (click)="closeDownloadResolveDocumentId()"
          data-dismiss="modal"
          type="button"
        >
          Close
        </button>
      </div>
    </div>
  </p-tabView>
</p-dialog>
<p-dialog
  header="Document Preview"
  [(visible)]="resolvedocumentPreview"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="height: 600px">
    <iframe [src]="resolvePreviewUrl" width="100%" height="100%" allowfullscreen></iframe>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeDocumentResolvePreview()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Upload Document"
  [(visible)]="uploadDocumentRoot"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div [formGroup]="uploadRootForm" class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Select files to upload </label>
        <input
          (change)="onFileChangeUploadRoot($event)"
          class="form-control"
          id="txtSelectDocument"
          multiple="multiple"
          placeholder="Select Attachment"
          style="padding: 2px; width: 100%"
          type="file"
        />
        <div *ngFor="let file of selectedFileUploadPreview; let i = index">
          <div
            style="
              padding-left: 10px;
              padding-right: 10px;
              padding-top: 4px;
              padding-bottom: 4px;
              font-size: 10px;
            "
          >
            {{ file?.name }}
            <button type="button" class="close" (click)="deletUploadedFileRoot(file?.name)">
              &times;
            </button>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4" style="margin-top: 10px">
        <label>Latitude</label>
        <input
          type="text"
          formControlName="latitude"
          class="form-control"
          placeholder="Enter Latitude"
        />
      </div>
      <div class="col-12 col-md-4" style="margin-top: 10px">
        <label>Longitude</label>
        <input
          type="text"
          formControlName="longitude"
          class="form-control"
          placeholder="Enter Longitude"
        />
      </div>
      <div class="col-12 col-md-4" style="margin-top: 10px">
        <span
          class="HoverEffect"
          (click)="mylocationRoot()"
          title="Get Current Location"
          style="border-bottom: 1px solid #f7b206; cursor: pointer"
        >
          <img
            class="LocationIcon LocationIconMargin"
            src="assets/img/B_Find-My-current-location_Y.png"
          />
        </span>
      </div>
      <div class="col-12 col-md-4" style="margin-top: 10px">
        <p-checkbox binary="true" formControlName="addRemarkChecked" label="Add Remark">
        </p-checkbox>
      </div>

      <div
        class="col-12 col-md-4"
        *ngIf="uploadRootForm.get('addRemarkChecked')?.value"
        style="margin-top: 10px"
      >
        <textarea
          formControlName="uploadremark"
          rows="3"
          class="form-control"
          placeholder="Enter your remark"
        >
        </textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="closeUploadDocumentRoot()"
      class="btn btn-default"
      data-dismiss="modal"
      id="searchbtn"
      type="reset"
    >
      Close
    </button>
  </div>
</p-dialog>
