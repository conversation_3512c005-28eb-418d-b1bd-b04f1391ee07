import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { LeadManagementComponent } from "./lead-management.component";
import { DeactivateService } from "src/app/service/deactivate.service";

const routes = [
  { path: "", component: LeadManagementComponent, canDeactivate: [DeactivateService] },
];

@NgModule({
  declarations: [LeadManagementComponent],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],
})
export class LeadManagementModule {}
