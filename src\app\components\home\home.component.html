<!-- WRAPPER -->
<div id="wrapper">
  <!-- <router-outlet name="sidebar"></router-outlet> -->
  <app-sidebar></app-sidebar>

  <!-- MAIN -->
  <div
    class="main"
    [ngClass]="{
      IconHideMain: sidebarService.sidebarShowIcon,
      IconWidthMain: !sidebarService.sidebarShowIcon
    }"
  >
    <div class="navbar-btn menu-button" style="padding: 0; margin: 0;">
      <button
        type="button"
        class="btn-toggle-fullwidth"
        (click)="sidebarService.sidebar()"
      >
        <i class="fa fa-bars"></i>
      </button>
    </div>
    <!-- MAIN CONTENT -->
    <div class="main-content">
      <div class="container-fluid">
        <p-confirmDialog
          [style]="{ width: '40vw' }"
          [baseZIndex]="10000"
        ></p-confirmDialog>
        <div class="childComponent">
          <ngx-spinner
            template='<img class="rotate" width="100" height="100" src="assets/img/ajaxwaiting.png" >'
          ></ngx-spinner>
        </div>
        <router-outlet></router-outlet>
        <app-footer></app-footer>
      </div>
    </div>
    <!-- END MAIN CONTENT -->
  </div>
  <!-- END MAIN -->
</div>
<!-- END WRAPPER -->
