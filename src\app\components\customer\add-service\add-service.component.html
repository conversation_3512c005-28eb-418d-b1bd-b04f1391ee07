<div class="panel">
  <div class="panel-heading">
    <div class="displayflex">
      <button
        (click)="customerDetailOpen()"
        class="btn btn-secondary backbtn"
        data-placement="bottom"
        data-toggle="tooltip"
        style="margin-left: 0px"
        title="Go to Customer Details"
        type="button"
      >
        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
      </button>
      <h3 class="panel-title">
        {{ custData.title }}
        {{ custData.firstname }}
        {{ custData.lastname }} Service Details
      </h3>
    </div>
    <div class="right">
      <button
        aria-controls="serviceDetailsCust"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#serviceDetailsCust"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div>
  <div class="panel-body">
    <div class="panel-collapse collapse in" id="serviceDetailsCust">
      <button
        type="submit"
        class="btn btn-primary yellowBtn"
        (click)="openAddServiceModal()"
        data-title="Add {{ name }}"
        data-toggle="modal"
        data-backdrop="static"
        data-keyboard="false"
      >
        Add {{ name }}
      </button>

      <!-- [disabled]="commondropdownService.isPlanOnDemand && !isLeadMaster" -->
      <button
        type="button"
        class="btn btn-primary yellowBtn"
        *ngIf="serviceStartPuase"
        (click)="openPaushSearviceMedel('', 'Pause')"
      >
        Pause
      </button>
      <button
        type="button"
        class="btn btn-success yellowBtn"
        *ngIf="!serviceStartPuase"
        (click)="openPaushSearviceMedel('', 'Start')"
      >
        Start
      </button>
      <button
        *ngIf="name == 'Service'"
        class="btn refreshbtn"
        type="reset"
        (click)="getActivePlanDetails()"
      >
        <i class="fa fa-refresh"></i>
      </button>

      <button
        *ngIf="name == 'Circuit'"
        class="btn refreshbtn"
        type="reset"
        (click)="getLeadServiceList()"
      >
        <i class="fa fa-refresh"></i>
      </button>

      <div
        class="table-responsive"
        *ngIf="custCurrentPlanList && custCurrentPlanList.length > 0 && !isLeadMaster"
      >
        <table class="table">
          <thead>
            <tr>
              <th>Service Name</th>
              <th>Connection No</th>
              <!-- <th>Current Plan</th> -->
              <th>Start Date</th>
              <th>End Date</th>
              <th>Status</th>
              <th>Nick Name</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let plan of custCurrentPlanList; index as i">
              <td>{{ plan.service }}</td>
              <td (click)="particularALLDatashow(plan)" style="color: #f7b206; cursor: pointer">
                {{ plan.connection_no }}
              </td>
              <!-- <td>{{ plan.planName }}</td> -->
              <td>{{ plan.startDate }}</td>
              <td>{{ plan.endDate }}</td>
              <td>
                <!--<span
                    *ngIf="
                        plan.custServMappingStatus == 'ACTIVE' || plan.custServMappingStatus == 'Active'
                    "
                    >
                    <span class="badge badge-success">Active</span>
                    </span>
                    <span *ngIf="plan.custServMappingStatus == 'NewActivation'">
                    <span class="badge badge-success">New Activation</span>
                    </span>
                    <span
                    *ngIf="
                        plan.custServMappingStatus == 'INACTIVE' ||
                        plan.custServMappingStatus == 'Inactive'
                    "
                    >
                    <span class="badge badge-danger">Inactive</span>
                    </span>
                    <span
                    *ngIf="
                        plan.custServMappingStatus === 'STOP' && plan.custServMappingStatus === null
                    "
                    >
                    <span class="badge badge-danger">Stop</span>
                    </span>
                    <span
                    *ngIf="
                        plan.custServMappingStatus === 'STOP' && plan.custServMappingStatus !== null
                    "
                    >
                    <span class="badge badge-primary">On Hold</span>
                    </span>-->
                <span *ngIf="plan.custPlanStatus == 'ACTIVE' || plan.custPlanStatus == 'Active'">
                  <span class="badge badge-success">Active</span>
                </span>
                <span
                  *ngIf="plan.custPlanStatus == 'INACTIVE' || plan.custPlanStatus == 'Inactive'"
                >
                  <span class="badge badge-danger">Inactive</span>
                </span>
                <span *ngIf="plan.custPlanStatus === 'STOP' && plan.stopServiceDate === null">
                  <span class="badge badge-danger">Stop</span>
                </span>
                <span *ngIf="plan.custPlanStatus === 'STOP' && plan.stopServiceDate !== null">
                  <span class="badge badge-primary">On Hold</span>
                </span>
              </td>
              <td>
                <input
                  (focusout)="saveEditNickName(plan.custPlanMapppingId, plan.nickname)"
                  name="nickname"
                  id="nickname"
                  [value]="plan.nickname"
                  class="form-control"
                  placeholder="Enter Nick Name"
                  [(ngModel)]="plan.nickname"
                />
              </td>
              <td class="btnAction" *ngIf="custData.status !== 'NewActivation' && custData.status">
                <button
                  id="delete-button"
                  type="button"
                  class="approve-btn"
                  style="border: none; background: transparent; padding: 0; margin-right: 3px"
                  (click)="openPaushSearviceMedel(plan, 'Delete')"
                  title="Service Termination"
                  [disabled]="
                    !(plan.nextStaff == null && plan.nextTeamHierarchyMappingId == null) ||
                    plan.custServMappingStatus == 'Rejected' ||
                    plan.custServMappingStatus === 'Terminate' ||
                    this.custData.status === 'NewActivation' ||
                    plan.invoiceType == 'Group' ||
                    !serviceTerminationCheck(plan.serviceEndDate, plan.custPlanStatus)
                  "
                >
                  <img src="assets/img/05_Service-Termination.png" />
                </button>
                <span *ngIf="plan.custServMappingStatus != 'NewActivation'">
                  <button
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                    title="Pick"
                    (click)="pickModalOpen(plan)"
                    [disabled]="
                      (plan.nextStaff != null && plan.nextTeamHierarchyMappingId == null) ||
                      (plan.nextStaff == null && plan.nextTeamHierarchyMappingId == null) ||
                      plan.custPlanStatus === 'STOP' ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      (plan.nextStaff != null && plan.nextTeamHierarchyMappingId != null) ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Approve"
                    (click)="approvePlanOpen(plan.planId, '', plan.customerServiceMappingId, '')"
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img src="assets/img/assign.jpg" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Reject"
                    (click)="
                      rejectPlanOpen(plan.planId, '', plan.customerServiceMappingId, '') ||
                        plan.custPlanStatus === 'STOP'
                    "
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img src="assets/img/reject.jpg" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Reassign"
                    (click)="StaffReasignList(plan)"
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custPlanStatus === 'STOP' ||
                      plan.custServMappingStatus === 'STOP' ||
                      plan.custServMappingStatus == 'Rejected' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img width="32" height="32" alt="Assign" src="assets/img/icons-02.png" />
                    <!-- </a> -->
                  </button>
                </span>
                <span *ngIf="plan.custServMappingStatus == 'NewActivation'">
                  <button
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                    title="Pick"
                    (click)="pickModalOpen(plan)"
                    [disabled]="
                      (plan.nextStaff != null && plan.nextTeamHierarchyMappingId == null) ||
                      (plan.nextStaff == null && plan.nextTeamHierarchyMappingId == null) ||
                      plan.custPlanStatus === 'STOP' ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      this.custData.status === 'NewActivation' ||
                      (plan.nextStaff != null && plan.nextTeamHierarchyMappingId != null)
                    "
                  >
                    <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Approve"
                    (click)="
                      approvePlanOpen(
                        plan.planId,
                        '',
                        plan.customerServiceMappingId,
                        plan.custServMappingStatus
                      )
                    "
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custPlanStatus === 'STOP' ||
                      plan.custServMappingStatus === 'STOP' ||
                      plan.custServMappingStatus == 'Rejected' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img src="assets/img/assign.jpg" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Reject"
                    (click)="
                      rejectPlanOpen(
                        plan.planId,
                        '',
                        plan.customerServiceMappingId,
                        plan.custServMappingStatus
                      ) || plan.custPlanStatus === 'STOP'
                    "
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img src="assets/img/reject.jpg" />
                  </button>
                  <button
                    type="button"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Reassign"
                    (click)="StaffReasignList(plan)"
                    [disabled]="
                      plan.nextStaff != staffUserId ||
                      plan.custServMappingStatus == 'Rejected' ||
                      plan.custServMappingStatus === 'STOP' ||
                      this.custData.status === 'NewActivation'
                    "
                  >
                    <img width="32" height="32" alt="Assign" src="assets/img/icons-02.png" />
                    <!-- </a> -->
                  </button>
                </span>
                <!-- <a
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        data-toggle="tooltip"
                        data-placement="top"
                        id="assign-button"
                        title="Reassign"
                        (click)="StaffReasignList(plan)"
                        > -->

                <a
                  *ngIf="plan.custServMappingStatus === 'NewActivation'"
                  class="detailOnAnchorClick"
                  title="Workflow Status Details"
                  (click)="
                    openAddWorkFlow('custauditWorkflowModalService', plan.customerServiceMappingId)
                  "
                >
                  <img width="32" height="32" src="assets/img/05_inventory-to-customer_Y.png" />
                </a>
                <a
                  *ngIf="plan.custServMappingStatus !== 'NewActivation'"
                  class="detailOnAnchorClick"
                  title="Workflow Status Details"
                  (click)="
                    openEditWorkFlow('custauditWorkflowModalService', plan.customerServiceMappingId)
                  "
                >
                  <img width="32" height="32" src="assets/img/05_inventory-to-customer_Y.png" />
                </a>
                <a
                  class="detailOnAnchorClick"
                  title="Audit Details"
                  (click)="openAudit(plan.customerServiceMappingId)"
                >
                  <img width="32" height="32" src="assets/img/05_inventory-to-customer_Y.png" />
                </a>
                <button
                  type="button"
                  class="approve-btn"
                  *ngIf="
                    (plan.custPlanStatus === 'Active' || plan.custPlanStatus === 'ACTIVE') &&
                    !plan.isHold
                  "
                  title="Service Hold Resume"
                  (click)="openPaushSearviceMedel(plan, 'Pause')"
                  [disabled]="plan.invoiceType == 'Group'"
                >
                  <img src="assets/img/03_Pause-&-Resume.png" />
                </button>
                <button
                  type="button"
                  class="approve-btn"
                  title="Service Hold Resume"
                  *ngIf="plan.custPlanStatus === 'STOP' && plan.isHold"
                  (click)="openPaushSearviceMedel(plan, 'Start')"
                  [disabled]="plan.invoiceType == 'Group'"
                >
                  <img src="assets/img/03_Pause-&-Resume.png" />
                </button>
                <button
                  class="curson_pointer approve-btn"
                  style="border: none; background: transparent; padding: 0; margin-right: 3px"
                  title="Service Stop"
                  type="button"
                  (click)="openPaushSearviceMedel(plan, 'Stop')"
                  [disabled]="
                    plan.invoiceType == 'Group' ||
                    !serviceTerminationCheck(plan.serviceEndDate, plan.custPlanStatus)
                  "
                >
                  <img src="assets/img/04_Service-Stop.png" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <!-- <div class="pagination_Dropdown">
          <pagination-controls
            id="custCurrentPlanListData"
            maxSize="10"
            directionLinks="true"
            previousLabel=""
            nextLabel=""
            (pageChange)="pageChangedcustomerCurrentPlanListData($event)"
          >
          </pagination-controls>
          <div id="itemPerPageDropdown">
            <p-dropdown
              [options]="pageLimitOptions"
              optionLabel="value"
              optionValue="value"
              (onChange)="TotalCurrentPlanItemPerPage($event)"
            ></p-dropdown>
          </div>
        </div> -->
      </div>

      <div
        class="table-responsive"
        *ngIf="custCurrentPlanList && custCurrentPlanList.length > 0 && isLeadMaster"
      >
        <table class="table">
          <thead>
            <tr>
              <th>{{ name }} Name</th>
              <th>Connection No</th>
              <!-- <th>Current Plan</th> -->
              <th>Start Date</th>
              <th>End Date</th>
              <th>Status</th>
              <th>Remarks</th>
              <!-- <th>Nick Name</th> -->
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let plan of custCurrentPlanList
                  | paginate
                    : {
                        id: 'custCurrentPlanListData',
                        itemsPerPage: customerCurrentPlanListdataitemsPerPage,
                        currentPage: currentPagecustomerCurrentPlanListdata,
                        totalItems: customerCurrentPlanListdatatotalRecords
                      };
                index as i
              "
            >
              <td *ngIf="name == 'Service'">{{ plan.serviceName }}</td>
              <td
                *ngIf="name == 'Circuit'"
                (click)="viewCircuitDetail(plan.id)"
                style="color: #f7b206; cursor: pointer"
              >
                {{ plan.leaseCircuitName }}
              </td>
              <td>{{ plan.connectionNo }}</td>
              <!-- <td>{{ plan.planName }}</td> -->
              <td>{{ plan.startDate }}</td>
              <td>{{ plan.endDate }}</td>
              <td>
                <span *ngIf="plan.status == 'ACTIVE' || plan.status == 'Active'">
                  <span class="badge badge-success">Active</span>
                </span>
                <span *ngIf="plan.status == 'INACTIVE' || plan.status == 'Inactive'">
                  <span class="badge badge-danger">Inactive</span>
                </span>
                <span *ngIf="plan.status === 'STOP' && plan.stopServiceDate === null">
                  <span class="badge badge-danger">Stop</span>
                </span>
                <span *ngIf="plan.status === 'STOP' && plan.stopServiceDate !== null">
                  <span class="badge badge-primary">On Hold</span>
                </span>
              </td>
              <td>{{ plan.remarks }}</td>
              <!-- <td>
            <input
              (focusout)="saveEditNickName(plan.custPlanMapppingId, plan.nickname)"
              name="nickname"
              id="nickname"
              [value]="plan.nickname"
              class="form-control"
              placeholder="Enter Nick Name"
              [(ngModel)]="plan.nickname"
            />
          </td> -->
              <td class="btnAction">
                <a
                  id="Edit-button"
                  href="javascript:void(0)"
                  (click)="editService(plan.id)"
                  title="Edit {{ name }}"
                >
                  <img src="assets/img/ioc01.jpg" />
                </a>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="pagination_Dropdown">
          <pagination-controls
            id="custCurrentPlanListData"
            maxSize="10"
            directionLinks="true"
            previousLabel=""
            nextLabel=""
            (pageChange)="pageChangedcustomerCurrentPlanListData($event)"
          >
          </pagination-controls>
          <div id="itemPerPageDropdown">
            <p-dropdown
              [options]="pageLimitOptions"
              optionLabel="value"
              optionValue="value"
              (onChange)="TotalCurrentPlanItemPerPage($event)"
            ></p-dropdown>
          </div>
        </div>
      </div>
      <div class="table-responsive" *ngIf="custCurrentPlanList && custCurrentPlanList.length <= 0">
        Details are not available.
      </div>
    </div>
  </div>
</div>

<app-workflow-audit-details-modal
  *ngIf="ifModelIsShow"
  [auditcustid]="auditcustid"
  dialogId="custauditWorkflowModal"
  (closeParentCustt)="closeParentCustt()"
></app-workflow-audit-details-modal>
<div class="modal fade" id="addServiceModal" role="dialog">
  <div class="modal-dialog" style="width: 75%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">{{ isServiceEdit ? "Edit" : "Create" }} {{ name }}</h3>
      </div>
      <div class="modal-body">
        <!-- <ul class="nav nav-tabs">
          <li class="active">
            <a href="#generalinformation" data-toggle="tab">General Information</a>
          </li>
          <li>
            <a href="#planDetails" data-toggle="tab">Plan Details</a>
          </li>
          <li>
            <a href="#chargeDetails" data-toggle="tab">Charge Details</a>
          </li>
        </ul>
        <div class="tab-content">
       
          <div class="tab-pane" id="planDetails">
            <app-common-plan (planFormDTO)="getPlanDTO($event)"></app-common-plan>
          </div>
          <div class="tab-pane" id="chargeDetails">
            <app-common-charge (chargeFormDTO)="getChargeDTO($event)"></app-common-charge>
          </div>
        </div> -->
        <div>
          <div *ngIf="generalStep">
            <div id="generalinformation" class="tab-pane active" [formGroup]="generalInfoForm">
              <div class="row">
                <div class="card flex justify-content-center">
                  <p-accordion [activeIndex]="0">
                    <p-accordionTab header="Link Acceptance Details *">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Branch Name*</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="customerName"
                              (keyup)="generateCircuitNameFun()"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.customerName.errors
                              "
                            >
                              <div class="error text-danger">Customer Name is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Organisation Name</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="organisation"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.customerName.errors
                              "
                            >
                              <div class="error text-danger">Branch Name is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>CAF Number</label>
                            <input type="text" class="form-control" formControlName="cafNumber" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.cafNumber.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Service Type *</label>
                            <p-dropdown
                              [options]="commondropdownService.planserviceData"
                              optionValue="name"
                              optionLabel="name"
                              filter="true"
                              filterBy="name"
                              placeholder="Select a Service "
                              formControlName="serviceType"
                              (onChange)="planserviceID($event)"
                              [disabled]="ifLeadQuickInput"
                            ></p-dropdown>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.serviceType.errors
                              "
                            >
                              <div class="error text-danger">Service Type is required.</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Circuit Name*</label>
                            <span
                              style="padding-left: 7px; color: #f7b206"
                              title="Generate Circuit Name"
                              (click)="generateCircuitNameFun()"
                              ><i class="fa fa-refresh" aria-hidden="true"></i>
                            </span>
                            <input type="text" class="form-control" formControlName="circuitName" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.circuitName.errors
                              "
                            >
                              <div class="error text-danger">Circuit Name is required.</div>
                            </div>
                          </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Link Type *</label>
                            <p-dropdown
                              [options]="typeOfLinkData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Type of Link"
                              formControlName="typeOfLink"
                              (onChange)="generateCircuitNameFun()"
                            ></p-dropdown>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.typeOfLink.errors
                              "
                            >
                              <div class="error text-danger">Link Type is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Connection Type *</label>
                            <p-dropdown
                              [options]="connectionTypeData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Connection Type"
                              formControlName="connectionType"
                              (onChange)="generateCircuitNameFun()"
                            ></p-dropdown>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.connectionType.errors
                              "
                            >
                              <div class="error text-danger">Connection Type is required.</div>
                            </div>
                          </div>
                        </div>
                        <!--<div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Link Installation Date*</label>
                          <input
                            type="date"
                            class="form-control"
                            formControlName="linkInstallationDate"
                          />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              serviceparamsubmitted &&
                              generalInfoForm.controls.linkInstallationDate.errors
                            "
                          >
                            <div class="error text-danger">Link Installation Date is required.</div>
                          </div>
                        </div>
                      </div> -->
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Purchase Order Date *</label>
                          <input
                            type="date"
                            class="form-control"
                            formControlName="purchaseOrderDate"
                          />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              serviceparamsubmitted &&
                              generalInfoForm.controls.purchaseOrderDate.errors
                            "
                          >
                            <div class="error text-danger">Purchase Order Date is required.</div>
                          </div>
                        </div>
                      </div> -->
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Expiry Date</label>
                          <input type="date" class="form-control" formControlName="expiryDate" />
                        </div>
                      </div> -->
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Circuit Status</label>
                            <p-dropdown
                              [options]="circuitStatus"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Status"
                              formControlName="circuitStatus"
                            ></p-dropdown>
                          </div>
                        </div>
                      </div>

                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Organization Type1 *</label>
                            <p-dropdown
                              [options]="commondropdownService.serviceAreaTypeData"
                              optionValue="text"
                              optionLabel="displayName"
                              filter="true"
                              filterBy="text"
                              placeholder="Select a Service Area Type "
                              formControlName="serviceAreaType"
                              (onChange)="generateCircuitNameFun()"
                            ></p-dropdown>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.serviceAreaType.errors
                              "
                            >
                              <div class="error text-danger">Organization Type is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Location*</label>
                            <input type="text" class="form-control" formControlName="location" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.location.errors
                              "
                            >
                              <div class="error text-danger">Location is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Account Number </label>
                            <input type="text" class="form-control" formControlName="acctNumber" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.acctNumber.errors
                              "
                            >
                              <div class="error text-danger">Account Number is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                          <label>Valley Type</label>
                          <p-dropdown
                            [options]="commondropdownService.valleyType"
                            filter="true"
                            filterBy="text"
                            formControlName="valleyType"
                            optionLabel="text"
                            optionValue="value"
                            placeholder="Select a Valley Type"
                          ></p-dropdown>
                        </div>

                        <div
                          class="col-lg-3 col-md-4 col-sm-6 col-xs-12"
                          *ngIf="generalInfoForm.controls.uploadCaf.enabled"
                        >
                          <div class="form-group">
                            <label>Upload CAF</label>
                            <input type="text" class="form-control" formControlName="uploadCaf" />
                          </div>
                        </div>

                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Link Acceptance Date</label>
                          <input
                            type="date"
                            class="form-control"
                            formControlName="linkAcceptanceDate"
                          />
                        </div>
                      </div> -->
                      </div>
                      <div class="row">
                        <div
                          *ngIf="generalInfoForm.value.valleyType == 'Outside Valley'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <label>Outside Valley</label>
                          <p-dropdown
                            [options]="commondropdownService.outsideValley"
                            filter="true"
                            filterBy="text"
                            formControlName="insideValley"
                            optionLabel="text"
                            optionValue="value"
                            placeholder="Select a Outside Valley"
                          ></p-dropdown>
                        </div>
                        <div
                          *ngIf="generalInfoForm.value.valleyType == 'Inside Valley'"
                          class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        >
                          <label>Inside Valley</label>
                          <p-dropdown
                            [options]="commondropdownService.insideValley"
                            filter="true"
                            filterBy="text"
                            formControlName="insideValley"
                            optionLabel="text"
                            optionValue="value"
                            placeholder="Select a Inside Valley"
                          ></p-dropdown>
                        </div>
                      </div>

                      <div class="row">
                        <div
                          class="col-lg-3 col-md-4 col-sm-6 col-xs-12"
                          *ngIf="generalInfoForm.controls.partner.enabled"
                        >
                          <div class="form-group">
                            <label>Partner</label>
                            <p-dropdown
                              [options]="commondropdownService.partnerData"
                              optionValue="displayId"
                              optionLabel="displayName"
                              filter="true"
                              filterBy="displayName"
                              placeholder="Select a Partner "
                              formControlName="partner"
                            ></p-dropdown>
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                    <p-accordionTab header="Technical Details">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Distance (M)</label>
                            <input type="text" class="form-control" formControlName="distance" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.distance.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12" >
                        <label>Upload Qos*</label>
                        <input type="text" class="form-control" formControlName="uploadQos" />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="serviceparamsubmitted && generalInfoForm.controls.uploadQos.errors"
                        >
                          <div class="error text-danger">Upload QOS is required.</div>
                        </div>
                      </div> -->
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <label>Link Router Location</label>
                          <input
                            type="text"
                            class="form-control"
                            formControlName="linkRouterLocation"
                          />
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Link Router Ip</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="linkRouterIp"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>VLAN Id</label>
                            <input type="text" class="form-control" formControlName="vlanId" />
                          </div>
                        </div>
                      </div>

                      <div class="row">
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Bandwidth</label>
                          <input type="text" class="form-control" formControlName="bandwidth" />
                        </div>
                      </div> -->
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Download Qos</label>
                          <input type="text" class="form-control" formControlName="downloadQos" />
                        </div>
                      </div> -->
                      </div>

                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Link Router Name</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="linkRouterName"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Link Port Type</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="linkPortType"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Link Port On Router</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="linkPortOnRouter"
                            />
                          </div>
                        </div>
                        <!-- <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                        <div class="form-group">
                          <label>Band Width Type</label>
                          <input type="text" class="form-control" formControlName="bandWidthType" />
                        </div>
                      </div> -->
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Circuit Billing Id</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="circuitBillingId"
                            />
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                    <p-accordionTab header="Location Details">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>POP</label>
                            <p-dropdown
                              [options]="commondropdownService.popData"
                              optionValue="displayName"
                              optionLabel="displayName"
                              filter="true"
                              filterBy="displayName"
                              placeholder="Select a POP "
                              formControlName="pop"
                            ></p-dropdown>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Associated Level</label>
                            <p-dropdown
                              [options]="locationDetailsData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Associated Level"
                              formControlName="associatedLevel"
                            ></p-dropdown>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Location Level 1</label>
                            <p-dropdown
                              [options]="locationDetailsData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Location Level 1"
                              formControlName="locationLevel1"
                            ></p-dropdown>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Location Level 2</label>
                            <p-dropdown
                              [options]="locationDetailsData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Location Level 2"
                              formControlName="locationLevel2"
                            ></p-dropdown>
                          </div>
                        </div>
                      </div>

                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Location Level 3</label>
                            <p-dropdown
                              [options]="locationDetailsData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Location Level 3"
                              formControlName="locationLevel3"
                            ></p-dropdown>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Location Level 4</label>
                            <p-dropdown
                              [options]="locationDetailsData"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Location Level 4"
                              formControlName="locationLevel4"
                            ></p-dropdown>
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                    <p-accordionTab header="Link Endpoint Details">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Base Station Id 1</label>

                            <input
                              type="text"
                              class="form-control"
                              formControlName="baseStationId1"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Base Station Id 2</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="baseStationId2"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Termination Address</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="terminationAddress"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Note</label>
                            <textarea class="form-control" formControlName="note"></textarea>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.note.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 250 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                    <p-accordionTab header="Contact Details *">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Contact Person*</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="contactPerson"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.contactPerson.errors
                              "
                            >
                              <div class="error text-danger">Contact Person is required.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Contact Person</label>
                            <input
                              type="text"
                              class="form-control"
                              formControlName="contactPerson1"
                            />
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Mobile Number*</label>
                            <div style="display: flex">
                              <div style="width: 30%">
                                <p-dropdown
                                  [filter]="true"
                                  [options]="countries"
                                  formControlName="countryCode"
                                  id="countryCode"
                                  optionLabel="dial_code"
                                  optionValue="dial_code"
                                  placeholder=""
                                  [disabled]="ifLeadQuickInput"
                                >
                                </p-dropdown>
                              </div>
                              <div style="width: 70%">
                                <input
                                  class="form-control"
                                  formControlName="mobileNo"
                                  id="mobile"
                                  min="0"
                                  placeholder="Enter Mobile "
                                  type="text"
                                  [readonly]="ifLeadQuickInput"
                                />
                              </div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted && generalInfoForm.controls.countryCode.errors
                              "
                            >
                              <div class="error text-danger">Country Code is required.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.mobileNo.errors?.required
                              "
                            >
                              <div class="error text-danger">Mobile No. is required.</div>
                            </div>

                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.mobileNo.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.mobileNo.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 15 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Mobile Number 1</label>
                            <input type="text" class="form-control" formControlName="mobileNo1" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.mobileNo1.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.mobileNo1.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 15 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Landline Number</label>
                            <input type="text" class="form-control" formControlName="landlineNo" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.landlineNo.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.landlineNo.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 15 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Landline Number 1</label>
                            <input type="text" class="form-control" formControlName="landlineNo1" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.landlineNo1.errors?.pattern
                              "
                            >
                              <div class="error text-danger">Only Numeric Characters Allowed.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.landlineNo1.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 15 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Email*</label>
                            <input
                              type="email"
                              class="form-control"
                              formControlName="email"
                              [readonly]="ifLeadQuickInput"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.email.errors?.required
                              "
                            >
                              <div class="error text-danger">Email is required.</div>
                            </div>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.email.errors?.email
                              "
                            >
                              <div class="error text-danger">Email is not valid.</div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Email 1</label>
                            <input type="email" class="form-control" formControlName="email1" />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.email1.errors?.email
                              "
                            >
                              <div class="error text-danger">Email is not valid.</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                    <p-accordionTab header="Remark">
                      <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label>Acceptance Remark</label>
                            <textarea class="form-control" formControlName="remark"></textarea>
                            <div
                              class="errorWrap text-danger"
                              *ngIf="
                                serviceparamsubmitted &&
                                generalInfoForm.controls.remark.errors?.maxlength
                              "
                            >
                              <div class="error text-danger">Maximum 250 Characters Allowed.</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </p-accordionTab>
                  </p-accordion>
                  <div class="row" style="margin-top: 15px">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 text-center">
                      <button class="btn btn-primary" (click)="movePlanDetail()">
                        <i class="fa fa-arrow" aria-hidden="true"></i>
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!generalStep">
            <app-common-plan
              (planFormDTO)="getPlanDTO($event)"
              [isServiceEdit]="isServiceEdit"
              [selServiceData]="generalInfoForm.controls.serviceType.value"
              [isServiceEditData]="serviceEditDetailData"
              [planServiceId]="planServiceId"
              [leadCustomerType]="leadCustomerType"
              [generateCircuitName]="generateCircuitName"
              [ifLeadQuickInput]="ifLeadQuickInput"
              [quickplanID]="quickplanID"
            ></app-common-plan>
          </div>
        </div>

        <!-- <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <button
              class="btn btn-primary"
              (click)="onAddPlanServiceField()"
              style="margin-top: 10px; margin-left: 30px"
            >
              <i class="fa fa-plus-square" aria-hidden="true"></i>
              Add
            </button>
          </div>
        </div> -->
        <div *ngIf="isServiceShow">
          <table class="table coa-table" style="margin-top: 3rem">
            <thead>
              <tr>
                <th>Service*</th>
                <th>Plan*</th>
                <th>Validity*</th>
                <th>offerPrice*</th>

                <th *ngIf="serviceForm.value.billTo !== 'ORGANIZATION'">Discount (%)*</th>
                <th *ngIf="serviceForm.value.billTo !== 'ORGANIZATION'">Trial plan</th>

                <th>Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let row of servicePlanFormArray.controls
                    | paginate
                      : {
                          id: 'servicePlanFromArrayData',
                          itemsPerPage: servicePlanItemPerPage,
                          currentPage: currentPageServicePlan,
                          totalItems: servicePlantotalRecords
                        };
                  let index = index
                "
              >
                <td style="padding-left: 8px">
                  <!-- {{row.value.service}} -->
                  <!-- <select
                          class="form-control"
                          style="width: 100%"
                          name="service"
                          id="service"
                          [formControl]="row.get('service')"
                          disabled
                        >
                          <option value="">Select Service</option>
                          <option
                            *ngFor="let item of commondropdownService.planserviceData"
                            value="{{ item.name }}"
                          >
                            {{ item.name }}
                          </option> 
                          <option
                         
                          value="{{servicename}}"
                        >
                          {{ servicename }}
                        </option>
                        </select> -->
                  <input
                    id="service"
                    type="string"
                    min="1"
                    class="form-control"
                    placeholder="Enter Service"
                    [formControl]="row.get('service')"
                    [(ngModel)]="servicename"
                    readonly
                  />
                </td>
                <td>
                  <!-- {{row.value.planId}} -->
                  <!-- <select
                          class="form-control"
                          style="width: 100%"
                          name="planId"
                          id="planId"
                          [formControl]="row.get('planId')"
                          disabled
                        >
                          <option value="">Select Plan</option>
                          <option
                            *ngFor="let item of commondropdownService.postpaidplanData"
                            value="{{ item.id }}"
                          >
                            {{ item.name }}
                          </option> 
                        </select> -->
                  <input
                    id="planname"
                    type="string"
                    min="1"
                    class="form-control"
                    placeholder="Enter Plan"
                    [formControl]="row.get('planname')"
                    readonly
                    [(ngModel)]="planName"
                  />
                </td>
                <td>
                  <div style="display: flex">
                    <div style="width: 40%">
                      <input
                        id="validity"
                        type="number"
                        min="1"
                        class="form-control"
                        placeholder="Enter Validity"
                        [formControl]="row.get('validity')"
                        readonly
                      />
                    </div>
                    <div style="width: 60%; height: 34px">
                      <span>
                        <select
                          class="form-control"
                          style="width: 100%"
                          [formControl]="row.get('validityUnit')"
                          disabled
                        >
                          <option value="">Select Unit</option>
                          <option
                            *ngFor="let label of commondropdownService.validityUnitData"
                            value="{{ label.label }}"
                          >
                            {{ label.label }}
                          </option>
                        </select>
                      </span>
                    </div>
                  </div>
                </td>
                <td>
                  <input
                    type="number"
                    class="form-control"
                    name="offerPrice"
                    id="offerPrice"
                    placeholder="Enter a OfferPrice *"
                    [formControl]="row.get('offerprice')"
                  />
                </td>
                <td *ngIf="serviceForm.value.billTo !== 'ORGANIZATION'">
                  <input
                    type="number"
                    class="form-control"
                    name="discount"
                    id="discount"
                    placeholder="Enter a Discount *"
                    [formControl]="row.get('discount')"
                    readonly
                  />
                </td>
                <td *ngIf="serviceForm.value.billTo !== 'ORGANIZATION'">
                  <input
                    [formControl]="row.get('istrialplan')"
                    type="checkbox"
                    class="inputcheckbox"
                  />
                </td>
                <td>
                  <a
                    id="deleteAtt"
                    href="javascript:void(0)"
                    (click)="deleteConfirmonChargeField(index)"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="row">
            <div class="col-md-12">
              <pagination-controls
                id="servicePlanFromArrayData"
                maxSize="5"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedPlanService($event)"
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <!-- <div class="addUpdateBtn">
          <button type="button" class="btn btn-primary btn-sm" (click)="addPlanService()">
            Save
          </button>
        </div> -->
        <div class="addUpdateBtn" style="margin-left: 1.5rem">
          <button type="button" class="btn btn-danger btn-sm" (click)="closeServiceModal()">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="viewServiceModal" role="dialog">
  <div class="modal-dialog" style="width: 75%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">{{ name }} Detail</h3>
      </div>
      <div class="modal-body">
        <p-tabView styleClass="x-tab-view">
          <p-tabPanel class="header" header="General Details">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Link Acceptance Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Customer Name :</label>
                    <span>{{ servicelinkAcceptanceData?.customerName }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">CAF Number :</label>
                    <span>{{ servicelinkAcceptanceData?.cafNo }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Account Number :</label>
                    <span>{{ servicelinkAcceptanceData?.acctNumber }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Service Type :</label>
                    <span>{{ servicelinkAcceptanceData?.serviceName }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Circuit Name :</label>
                    <span>{{ servicelinkAcceptanceData?.circuitName }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Type :</label>
                    <span>{{ servicelinkAcceptanceData?.typeOfLink }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Connection Type :</label>
                    <span>{{ servicelinkAcceptanceData?.connectionType }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Circuit Status :</label>
                    <span>{{ servicelinkAcceptanceData?.circuitStatus }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Organization Type :</label>
                    <span>{{ servicelinkAcceptanceData?.serviceAreaType }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location :</label>
                    <span>{{ servicelinkAcceptanceData?.location }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location :</label>
                    <span>{{ servicelinkAcceptanceData?.location }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Valley Type :</label>
                    <span>{{ servicelinkAcceptanceData?.valleyType }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup m-0">
                    <label
                      *ngIf="servicelinkAcceptanceData?.valleyType == 'Outside Valley'"
                      class="datalbl"
                      >Outside Valley :</label
                    >
                    <label
                      *ngIf="servicelinkAcceptanceData?.valleyType == 'Inside Valley'"
                      class="datalbl"
                      >Inside Valley :</label
                    >
                    <span>{{ servicelinkAcceptanceData?.insideValley }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Technical Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Distance (M) :</label>
                    <span>{{ servicelinkAcceptanceData?.distance }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Router Location :</label>
                    <span>{{ servicelinkAcceptanceData?.linkRouterLocation }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Router Ip :</label>
                    <span>{{ servicelinkAcceptanceData?.linkRouterIP }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">VLAN Id :</label>
                    <span>{{ servicelinkAcceptanceData?.vlanid }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Router Name :</label>
                    <span>{{ servicelinkAcceptanceData?.linkRouterName }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Port Type :</label>
                    <span>{{ servicelinkAcceptanceData?.linkPortType }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Link Port On Router :</label>
                    <span>{{ servicelinkAcceptanceData?.linkPortOnRouter }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Circuit Billing Id :</label>
                    <span>{{ servicelinkAcceptanceData?.circuitBillingId }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Location Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">POP :</label>
                    <span>{{ servicelinkAcceptanceData?.pop }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Associated Level :</label>
                    <span>{{ servicelinkAcceptanceData?.associatedLevel }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location Level 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.locationLevel1 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location Level 2 :</label>
                    <span>{{ servicelinkAcceptanceData?.locationLevel2 }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location Level 3 :</label>
                    <span>{{ servicelinkAcceptanceData?.locationLevel3 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Location Level 4 :</label>
                    <span>{{ servicelinkAcceptanceData?.locationLevel4 }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Link Endpoint Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Base Station Id 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.baseStationId1 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Base Station Id 2 :</label>
                    <span>{{ servicelinkAcceptanceData?.baseStationId2 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Termination Address :</label>
                    <span>{{ servicelinkAcceptanceData?.terminationAddress }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Note :</label>
                    <span>{{ servicelinkAcceptanceData?.note }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Contact Details Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Contact Person:</label>
                    <span>{{ servicelinkAcceptanceData?.contactPerson }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Contact Person 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.contactPerson1 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Mobile Number :</label>
                    <span>{{ servicelinkAcceptanceData?.mobileNo }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Mobile Number 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.mobileNumber1 }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Landline Number:</label>
                    <span>{{ servicelinkAcceptanceData?.landLineNumber }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Landline Number 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.landLineNumber1 }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Email :</label>
                    <span>{{ servicelinkAcceptanceData?.email }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Email 1 :</label>
                    <span>{{ servicelinkAcceptanceData?.emailId1 }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Remark</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Remark:</label>
                    <span>{{ servicelinkAcceptanceData?.remarks }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
          </p-tabPanel>
          <p-tabPanel class="header" header="Plan Details">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Plan Information</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Plan Type :</label>
                    <span>{{ circuitPlanData?.plantype }}</span>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Service Area :</label>
                    <span *ngFor="let serviceArea of serviceAreaListDisplay"
                      >{{ serviceArea }}, &nbsp;</span
                    >
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Validity :</label>
                    <span
                      >{{ circuitPlanData?.validity }}&nbsp;{{
                        circuitPlanData?.unitsOfValidity
                      }}</span
                    >
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Description :</label>
                    <span>{{ circuitPlanData?.desc }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
            <fieldset
              style="margin-top: 0rem; margin-bottom: 2rem"
              class="col-md-12 col-sm-12"
              *ngIf="
                serviceEditDetailData?.planMappingList[0]?.postpaidPlanPojo?.productplanmappingList
                  .length > 0
              "
            >
              <legend>Product Information</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Category</th>

                          <th>Type</th>
                          <th>Quantity</th>
                          <th>Revised Charge</th>
                          <th>Ownership Type</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let product of serviceEditDetailData?.planMappingList[0]
                              ?.postpaidPlanPojo?.productplanmappingList
                              | paginate
                                : {
                                    id: 'productData',
                                    itemsPerPage: custCircuitProductItemPerPage,
                                    currentPage: currentPageCircuitProductDeatilList,
                                    totalItems: custCircuitProducttotalRecords
                                  };
                            index as i
                          "
                        >
                          <td>{{ product?.productName }}</td>
                          <td>
                            {{ product?.productCategoryName }}
                          </td>

                          <td>
                            {{ product?.product_type }}
                          </td>
                          <td>
                            {{ product?.productQuantity }}
                          </td>
                          <td>
                            {{ product?.revisedCharge }}
                          </td>
                          <td>
                            {{ product?.ownershipType }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <pagination-controls
                      id="productData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedCircuitProductDetailList($event)"
                    ></pagination-controls>
                  </div>
                </div>
              </div>
            </fieldset>

            <fieldset style="margin-top: 0rem; margin-bottom: 2rem" class="col-md-12 col-sm-12">
              <legend>Charge Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Category</th>
                          <th>Type</th>
                          <th>Ledger Id</th>
                          <th>Billing Cycle</th>
                          <th>Actual Price</th>
                          <th>Tax</th>
                          <th>Description</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let charge of serviceEditDetailData?.planMappingList[0]
                              ?.postpaidPlanPojo?.chargeList
                              | paginate
                                : {
                                    id: 'chargeData',
                                    itemsPerPage: custCircuitChargeItemPerPage,
                                    currentPage: currentPageCircuitChargeDeatilList,
                                    totalItems: custCircuitChargetotalRecords
                                  };
                            index as i
                          "
                        >
                          <td>{{ charge?.charge?.name }}</td>
                          <td>
                            {{ charge?.charge?.chargecategory }}
                          </td>
                          <td>
                            {{ charge?.charge?.chargetype }}
                          </td>
                          <td>
                            {{ charge?.charge?.ledgerId }}
                          </td>
                          <td>
                            {{ charge?.billingCycle }}
                          </td>
                          <td>
                            {{ charge.charge?.actualprice }}
                          </td>
                          <td>
                            {{ charge?.charge?.taxid }}
                          </td>
                          <td>
                            {{ charge?.charge?.description }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <pagination-controls
                      id="chargeData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedCircuitChargeDetailList($event)"
                    ></pagination-controls>
                  </div>
                </div>
              </div>
            </fieldset>
          </p-tabPanel>
        </p-tabView>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn" style="margin-left: 1.5rem">
          <button type="button" class="btn btn-danger btn-sm" (click)="closeViewCircuitModal()">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="StopServiceModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          {{ ifselecResonType }} Reason
        </h4>
      </div>
      <div class="modal-body">
        <div class="row" *ngIf="ifselecResonType == 'Pause'">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>Deactivate Reason</label>
            <p-dropdown
              [options]="deactiveDataList"
              optionValue="value"
              optionLabel="text"
              filter="true"
              filterBy="text"
              placeholder="Select a Reason *"
              [(ngModel)]="selectDeactivateReason"
            ></p-dropdown>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>Remark*</label>
            <textarea
              class="form-control"
              name="remark"
              [(ngModel)]="serviceStropRemarks"
            ></textarea>
            <!-- <div
              *ngIf="assignDiscounsubmitted && assignAppRejectDiscountForm.controls.remark.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  assignDiscounsubmitted &&
                  assignAppRejectDiscountForm.controls.remark.errors.required
                "
                class="error text-danger"
              >
                Remark is required.
              </div>
            </div> -->
            <br />
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          *ngIf="ifselecResonType == 'Pause'"
          (click)="pauseService()"
          [disabled]="selectDeactivateReason == '' || serviceStropRemarks === ''"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
        <button
          *ngIf="ifselecResonType == 'Start'"
          (click)="playService()"
          [disabled]="serviceStropRemarks == ''"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
        <button
          *ngIf="ifselecResonType == 'Delete'"
          (click)="deleteServicePlanData()"
          [disabled]="selectDeactivateReason == '' && serviceStropRemarks == ''"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          Delete
        </button>

        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Select Customer"
  [(visible)]="displaySelectParentCustomer"
  [style]="{ width: '60%' }"
  [modal]="true"
>
  <ng-template pTemplate="content">
    <h5>Search Parent Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          filter="true"
          [filterBy]="'label'"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
          (onChange)="selParentSearchOption($event)"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption !== 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.CustomerStatusValue"
          filter="true"
          [filterBy]="'text'"
          optionValue="value"
          optionLabel="text"
          placeholder="Select a Status"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'serviceareaName'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.serviceAreaList"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Servicearea"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'plan'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.postpaidplanData"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Plan"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button (click)="searchParentCustomer()" class="btn btn-primary" type="button">
          <i class="fa fa-search"></i>
          Search
        </button>
        <button (click)="clearSearchParentCustomer()" class="btn btn-default" type="button">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Parent Customer</h5>
    <div style="overflow-x: auto">
      <p-table #dt [value]="prepaidParentCustomerList" [selection]="selectedParentCust">
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 5rem"></th>
            <th>Name</th>
            <th>User Name</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
          <tr>
            <td>
              <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
            </td>
            <td>{{ prepaidParentCustomerList.name }}</td>
            <td>{{ prepaidParentCustomerList.username }}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="summary">
          <p-paginator
            [rows]="parentCustomerListdataitemsPerPage"
            [totalRecords]="parentCustomerListdatatotalRecords"
            (onPageChange)="paginate($event)"
            [first]="newFirst"
          ></p-paginator>
        </ng-template>
      </p-table>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        [disabled]="selectedParentCust.length === 0"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </ng-template>
</p-dialog>

<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="showServiceDetailsID"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document" style="width: 60%">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Service Data</h4>
      </div>
      <div class="modal-body">
        <div>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Service Name :</label>
              <span>{{ serviceParticularData.service }}</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl"> Connection No :</label>
              <span>{{ serviceParticularData.connection_no }}</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Invoice Type :</label>
              <span>{{ serviceParticularData.invoiceType }}</span>
              <span *ngIf="!serviceParticularData.invoiceType">-</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Current Plan:</label>
              <span>{{ serviceParticularData.planName }}</span>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Start Date :</label>
              <span>{{ serviceParticularData.startDate }}</span>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">End Date :</label>
              <span>{{ serviceParticularData.endDate }}</span>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Plan Category :</label>
              <span>{{ serviceParticularData.custPlanCategory }}</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Remarks :</label>
              <span>{{ serviceParticularData.remarks }}</span>
              <span *ngIf="!serviceParticularData.remarks">-</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
              <label class="datalbl">Status :</label>
              <span
                *ngIf="
                  serviceParticularData.custPlanStatus == 'ACTIVE' ||
                  serviceParticularData.custPlanStatus == 'Active'
                "
              >
                <span class="badge badge-success">Active</span>
              </span>
              <span
                *ngIf="
                  serviceParticularData.custPlanStatus == 'INACTIVE' ||
                  serviceParticularData.custPlanStatus == 'Inactive'
                "
              >
                <span class="badge badge-danger">Inactive</span>
              </span>
              <span
                *ngIf="
                  serviceParticularData.custPlanStatus === 'STOP' &&
                  serviceParticularData.stopServiceDate === null
                "
              >
                <span class="badge badge-danger">Stop</span>
              </span>
              <span
                *ngIf="
                  serviceParticularData.custPlanStatus === 'STOP' &&
                  serviceParticularData.stopServiceDate !== null
                "
              >
                <span class="badge badge-primary">On Hold</span>
              </span>
            </div>
          </div>

          <fieldset style="margin-top: 1.5rem">
            <legend>Link Acceptance Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Circuit Name : </label>
                  <span>{{ !serviceCircuitData.circuitName }}</span>
                  <span *ngIf="!serviceCircuitData.circuitName">-</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">CAF Number : </label>
                  <span>{{ serviceCircuitData.cafNo }}</span>
                  <span *ngIf="!serviceCircuitData.cafNo">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Customer Name : </label>
                  <span>{{ serviceCircuitData.customerName }}</span>
                  <span *ngIf="!serviceCircuitData.customerName">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Type of Link : </label>
                  <span>{{ serviceCircuitData.typeOfLink }}</span>
                  <span *ngIf="!serviceCircuitData.typeOfLink">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Installation Date : </label>
                  <span>{{ serviceCircuitData.linkInstallationDate }}</span>
                  <span *ngIf="!serviceCircuitData.linkInstallationDate">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Purchase Order Date : </label>
                  <span>{{ serviceCircuitData.purchaseOrderDate }}</span>
                  <span *ngIf="!serviceCircuitData.purchaseOrderDate">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Expiry Date : </label>
                  <span>{{ serviceCircuitData.expiryDate }}</span>
                  <span *ngIf="!serviceCircuitData.expiryDate">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Circuit Status : </label>
                  <span>{{ serviceCircuitData.circuitStatus }}</span>
                  <span *ngIf="!serviceCircuitData.circuitStatus">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Upload CAF : </label>
                  <span>{{ serviceCircuitData.uploadCAF }}</span>
                  <span *ngIf="!serviceCircuitData.uploadCAF">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Account Number : </label>
                  <span>{{ serviceCircuitData.accountNumber }}</span>
                  <span *ngIf="!serviceCircuitData.accountNumber">-</span>
                </div>

                <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Service Type : </label>
                  <span>{{ !serviceCircuitData.circuitName  !serviceCircuitData.circuitName }}</span>
                </div> -->
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Acceptance Date : </label>
                  <span>{{ serviceCircuitData.linkAcceptanceDate }}</span>
                  <span *ngIf="!serviceCircuitData.linkAcceptanceDate">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Service Area Type : </label>
                  <span>{{ serviceCircuitData.serviceAreaType }}</span>
                  <span *ngIf="!serviceCircuitData.serviceAreaType">-</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Partner : </label>
                  <span>{{ serviceCircuitData.partner }}</span>
                  <span *ngIf="!serviceCircuitData.partner">-</span>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 1.5rem">
            <legend>Technical Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Distance : </label>
                  <span>{{ serviceCircuitData.distance }}</span>
                  <span *ngIf="!serviceCircuitData.distance">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Upload Qos : </label>
                  <span>{{ serviceCircuitData.uploadQOS }}</span>
                  <span *ngIf="!serviceCircuitData.uploadQOS">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Router Location : </label>
                  <span>{{ serviceCircuitData.linkRouterLocation }}</span>
                  <span *ngIf="!serviceCircuitData.linkRouterLocation">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Router Ip : </label>
                  <span>{{ serviceCircuitData.linkRouterIP }}</span>
                  <span *ngIf="!serviceCircuitData.linkRouterIP">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">VLAN Id : </label>
                  <span>{{ serviceCircuitData.vlanid }}</span>
                  <span *ngIf="!serviceCircuitData.vlanid">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Router Name : </label>
                  <span>{{ serviceCircuitData.linkRouterName }}</span>
                  <span *ngIf="!serviceCircuitData.linkRouterName">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Bandwidth : </label>
                  <span>{{ serviceCircuitData.bandwidth }}</span>
                  <span *ngIf="!serviceCircuitData.bandwidth">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Download Qos : </label>
                  <span>{{ serviceCircuitData.downloadQOS }}</span>
                  <span *ngIf="!serviceCircuitData.downloadQOS">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Port Type : </label>
                  <span>{{ serviceCircuitData.linkPortType }}</span>
                  <span *ngIf="!serviceCircuitData.linkPortType">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Link Port On Router : </label>
                  <span>{{ serviceCircuitData.linkPortOnRouter }}</span>
                  <span *ngIf="!serviceCircuitData.linkPortOnRouter">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Band Width Type : </label>
                  <span>{{ serviceCircuitData.bandwidthType }}</span>
                  <span *ngIf="!serviceCircuitData.bandwidthType">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Circuit Billing Id : </label>
                  <span>{{ serviceCircuitData.circircuitBillingIdcuitName }}</span>
                  <span *ngIf="!serviceCircuitData.circircuitBillingIdcuitName">-</span>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset style="margin-top: 1.5rem">
            <legend>Location Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">POP : </label>
                  <span>{{ serviceCircuitData.pop }}</span>
                  <span *ngIf="!serviceCircuitData.pop">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Associated Level : </label>
                  <span>{{ serviceCircuitData.associatedLevel }}</span>
                  <span *ngIf="!serviceCircuitData.associatedLevel">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Location Level 1 : </label>
                  <span>{{ serviceCircuitData.locationLevel1 }}</span>
                  <span *ngIf="!serviceCircuitData.locationLevel1">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Location Level 2 : </label>
                  <span>{{ serviceCircuitData.locationLevel2 }}</span>
                  <span *ngIf="!serviceCircuitData.locationLevel2">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Location Level 3 : </label>
                  <span>{{ serviceCircuitData.locationLevel3 }}</span>
                  <span *ngIf="!serviceCircuitData.locationLevel3">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Location Level 4 : </label>
                  <span>{{ serviceCircuitData.locationLevel4 }}</span>
                  <span *ngIf="!serviceCircuitData.locationLevel4">-</span>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset style="margin-top: 1.5rem">
            <legend>Link Endpoint Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Base Station Id 1 : </label>
                  <span>{{ serviceCircuitData.baseStationId1 }}</span>
                  <span *ngIf="!serviceCircuitData.baseStationId1">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Base Station Id 2 : </label>
                  <span>{{ serviceCircuitData.baseStationId2 }}</span>
                  <span *ngIf="!serviceCircuitData.baseStationId2">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Termination Address : </label>
                  <span>{{ serviceCircuitData.terminationAddress }}</span>
                  <span *ngIf="!serviceCircuitData.terminationAddress">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Note : </label>
                  <span>{{ serviceCircuitData.note }}</span>
                  <span *ngIf="!serviceCircuitData.note">-</span>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset style="margin-top: 1.5rem">
            <legend>Contact Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Contact Person : </label>
                  <span>{{ serviceCircuitData.contactPerson }}</span>
                  <span *ngIf="!serviceCircuitData.contactPerson">-</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Mobile Number : </label>
                  <span>{{ serviceCircuitData.mobileNumber }}</span>
                  <span *ngIf="!serviceCircuitData.mobileNumber">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Mobile Number 1 : </label>
                  <span>{{ serviceCircuitData.mobileNumber1 }}</span>
                  <span *ngIf="!serviceCircuitData.mobileNumber1">-</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Landline Number : </label>
                  <span>{{ serviceCircuitData.landLineNumber }}</span>
                  <span *ngIf="!serviceCircuitData.landLineNumber">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Landline Number 1 : </label>
                  <span>{{ serviceCircuitData.landLineNumber1 }}</span>
                  <span *ngIf="!serviceCircuitData.landLineNumber1">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Email : </label>
                  <span>{{ serviceCircuitData.emailId }}</span>
                  <span *ngIf="!serviceCircuitData.emailId">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl">Email 1 : </label>
                  <span>{{ serviceCircuitData.emailId1 }}</span>
                  <span *ngIf="!serviceCircuitData.emailId1">-</span>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Select Customer"
  [(visible)]="displaySelectParentCustomer"
  [style]="{ width: '60%' }"
  [modal]="true"
>
  <ng-template pTemplate="content">
    <h5>Search Parent Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          filter="true"
          [filterBy]="'label'"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
          (onChange)="selParentSearchOption($event)"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption !== 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.CustomerStatusValue"
          filter="true"
          [filterBy]="'text'"
          optionValue="value"
          optionLabel="text"
          placeholder="Select a Status"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'serviceareaName'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.serviceAreaList"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Servicearea"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'plan'">
        <p-dropdown
          [(ngModel)]="searchParentCustValue"
          [options]="commondropdownService.postpaidplanData"
          filter="true"
          [filterBy]="'name'"
          optionValue="id"
          optionLabel="name"
          placeholder="Select a Plan"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button (click)="searchParentCustomer()" class="btn btn-primary" type="button">
          <i class="fa fa-search"></i>
          Search
        </button>
        <button (click)="clearSearchParentCustomer()" class="btn btn-default" type="button">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Parent Customer</h5>
    <div style="overflow-x: auto">
      <p-table #dt [value]="prepaidParentCustomerList" [selection]="selectedParentCust">
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 5rem"></th>
            <th>Name</th>
            <th>User Name</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
          <tr>
            <td>
              <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
            </td>
            <td>{{ prepaidParentCustomerList.name }}</td>
            <td>{{ prepaidParentCustomerList.username }}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="summary">
          <p-paginator
            [rows]="parentCustomerListdataitemsPerPage"
            [totalRecords]="parentCustomerListdatatotalRecords"
            (onPageChange)="paginate($event)"
            [first]="newFirst"
          ></p-paginator>
        </ng-template>
      </p-table>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        [disabled]="selectedParentCust.length === 0"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </ng-template>
</p-dialog>

<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignApporvePlanModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Approve Plan</h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="assignPlanForm">
          <div class="row">
            <div class="row">
              <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card">
                  <h5>Select Staff</h5>
                  <p-table
                    [(selection)]="selectStaff"
                    [value]="approvePlanData"
                    responsiveLayout="scroll"
                  >
                    <ng-template pTemplate="header">
                      <tr>
                        <th style="width: 3rem"></th>
                        <th>Name</th>
                        <th>Username</th>
                      </tr>
                    </ng-template>
                    <ng-template let-product pTemplate="body">
                      <tr>
                        <td>
                          <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                        </td>
                        <td>{{ product.fullName }}</td>
                        <td>
                          {{ product.username }}
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
              </div>
              <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <label>Remark*</label>
                <textarea
                  [ngClass]="{
                    'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
                  }"
                  class="form-control"
                  formControlName="remark"
                  name="remark"
                ></textarea>
                <div
                  *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
              <br />
            </div>
          </div>
          <!-- <input type="file" formControlName="fileName" name="fileName"> -->
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="assignPlan()"
          *ngIf="!approved"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Approve
        </button>
        <button
          (click)="assignToStaff(true)"
          *ngIf="approved"
          class="btn btn-primary"
          id="submitButtonForApprove"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>

<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="rejectPlanModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Reject Plan</h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="rejectPlanForm">
          <div class="row">
            <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [(selection)]="selectStaffReject"
                  [value]="rejectPlanData"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div *ngIf="!reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                [ngClass]="{
                  'is-invalid': rejectPlanSubmitted && rejectPlanForm.controls.remark.errors
                }"
                class="form-control"
                formControlName="remark"
                name="remark"
              ></textarea>
              <div
                *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors.required"
                  class="error text-danger"
                >
                  Remark is required.
                </div>
              </div>
            </div>
            <br />
          </div>
          <!-- <input type="file" formControlName="fileName" name="fileName"> -->
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="rejectPlan()"
          *ngIf="!reject"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Reject
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && !selectStaffReject"
          class="btn btn-primary"
          disabled
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && selectStaffReject"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="reAssignPLANModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Approve Plan</h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="assignPlanForm">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [value]="approvableStaff"
                  [(selection)]="selectStaff"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-product>
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                class="form-control"
                name="remark"
                formControlName="remark"
                [ngClass]="{
                  'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
                }"
              ></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                >
                  Remark is required.
                </div>
              </div>
              <br />
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" id="submit" (click)="reassignWorkflow()">
          <i class="fa fa-check-circle"></i>
          Assign
        </button>

        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="auditDetails" role="dialog">
  <div class="modal-dialog" style="width: 75%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Audit Details</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12" style="margin-top: 3rem">
            <table class="table">
              <thead>
                <tr>
                  <th>Action Date</th>
                  <th>Action</th>
                  <th>Staff name</th>
                  <th>Action Reason</th>
                  <th>Remark</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let data of auditData
                      | paginate
                        : {
                            id: 'auditModal',
                            itemsPerPage: auditItemPerPage,
                            currentPage: currentPageAuditList,
                            totalItems: audittotalRecords
                          };
                    index as i
                  "
                >
                  <td>{{ data.serviceStopTime | date: "yyyy-MM-dd hh:mm a" }}</td>
                  <td>{{ data.action }}</td>
                  <td>{{ data.staffName }}</td>
                  <td>{{ data.reasonCategory }}</td>
                  <td>{{ data.remarks }}</td>
                </tr>
              </tbody>
            </table>
            <pagination-controls
              (pageChange)="pageChangedauditList($event)"
              directionLinks="true"
              id="auditModal"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            ></pagination-controls>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn" style="margin-left: 1.5rem">
          <button
            #closebutton
            (click)="auditCloseModal()"
            class="btn btn-danger btn-sm"
            data-dismiss="modal"
            type="button"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<app-plan-connection-no
  *ngIf="showPlanConnectionNo"
  [planForConnection]="planForConnection"
  (closeDialog)="closeDialog()"
></app-plan-connection-no>
