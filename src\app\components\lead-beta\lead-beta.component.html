<div *ngIf="isShowTemplate && filteredArray">
  <form [formGroup]="customerGroupForm">
    <div class="panel">
      <div class="panel-body">
        <div *ngFor="let module of filteredArray">
          <div *ngIf="module.fields.length != 0">
            <fieldset style="margin: 1.5rem">
              <legend>{{ module.moduleName }}</legend>

              <div class="boxWhite">
                <div class="row" style="margin: 2px; display: flex; flex-wrap: wrap">
                  <ng-container *ngFor="let item of module.fields; let i = index">
                    <ng-container
                      *ngIf="
                        item.fieldType != 'select' &&
                        item.fieldType != 'multi-select' &&
                        item.fieldType != 'checkbox' &&
                        item.fieldType != 'object' &&
                        item.fieldType != 'objectList'
                      "
                    >
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                        <div class="form-group">
                          <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                          <input
                            *ngIf="item.fieldname !== 'countryCode' && item.fieldname !== 'leadNo'"
                            class="form-control"
                            name="{{ item.fieldname }}"
                            id="{{ item.fieldname }}"
                            placeholder="Enter {{ item.name }}"
                            type="{{ item.fieldType }}"
                            [formControlName]="item.fieldname"
                            [readonly]="
                              ifReadonlyExtingInput &&
                              customerGroupForm.controls[item.fieldname].value
                            "
                          />
                          <input
                            *ngIf="item.fieldname === 'leadNo'"
                            class="form-control"
                            name="{{ item.fieldname }}"
                            id="{{ item.fieldname }}"
                            placeholder="Enter {{ item.name }}"
                            type="{{ item.fieldType }}"
                            [formControlName]="item.fieldname"
                            readonly
                          />
                          <p-dropdown
                            *ngIf="item.fieldname === 'countryCode'"
                            [filter]="true"
                            [options]="countries"
                            optionLabel="dial_code"
                            optionValue="dial_code"
                            [formControlName]="item.fieldname"
                            name="{{ item.fieldname }}"
                            id="{{ item.fieldname }}"
                            placeholder="Select a {{ item.name }}"
                            [disabled]="
                              ifReadonlyExtingInput &&
                              customerGroupForm.controls[item.fieldname].value
                            "
                          ></p-dropdown>
                          <div
                            *ngIf="
                              customerGroupForm.controls[item.fieldname]?.errors &&
                              (customerGroupForm.controls[item.fieldname].touched || submitted)
                            "
                            class="errorWrap text-danger"
                          >
                            <div class="error text-danger">{{ item.name }} is required.</div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                    <div *ngIf="item.fieldType == 'checkbox'">
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                        <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                        <input
                          class="form-check-input"
                          name="{{ item.fieldname }}"
                          id="{{ item.fieldname }}"
                          placeholder="Enter {{ item.name }}"
                          type="checkbox"
                          [formControlName]="item.fieldname"
                          style="width: 20px; height: 20px; margin-left: 15px; margin-top: 50px"
                          [readonly]="
                            ifReadonlyExtingInput &&
                            customerGroupForm.controls[item.fieldname].value
                          "
                        />
                      </div>
                      <div
                        *ngIf="
                          customerGroupForm.controls[item.fieldname]?.errors &&
                          (customerGroupForm.controls[item.fieldname].touched || submitted)
                        "
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">{{ item.name }} is required.</div>
                      </div>
                    </div>

                    <ng-container *ngIf="item.fieldType == 'select' && item.isdependant == false">
                      <ng-container *ngFor="let list of optionList">
                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname &&
                            item.backendrequired == 'displayName'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                            <div class="dropdown-container">
                              <p-dropdown
                                [options]="list.options.dataList"
                                optionValue="value"
                                optionLabel="displayName"
                                filter="true"
                                filterBy="displayName"
                                placeholder="Select a {{ item.name }}"
                                [formControlName]="item.fieldname"
                                name="{{ i + 1 }}module"
                                (onChange)="optionSelected($event, item.fieldname)"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              >
                              </p-dropdown>
                            </div>
                            <div
                              *ngIf="
                                customerGroupForm.controls[item.fieldname]?.errors &&
                                (customerGroupForm.controls[item.fieldname].touched || submitted)
                              "
                              class="errorWrap text-danger"
                            >
                              <div class="error text-danger">{{ item.name }} is required.</div>
                            </div>
                          </div>
                        </ng-container>

                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname && item.backendrequired == 'displayId'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                            <div class="dropdown-container">
                              <p-dropdown
                                [options]="list.options.dataList"
                                optionValue="displayId"
                                optionLabel="displayName"
                                filter="true"
                                filterBy="displayName"
                                placeholder="Select a {{ item.name }}"
                                [formControlName]="item.fieldname"
                                name="{{ i + 1 }}module"
                                (onChange)="optionSelected($event, item.fieldname)"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              >
                              </p-dropdown>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname]?.errors &&
                                  (customerGroupForm.controls[item.fieldname].touched || submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                    <ng-container *ngIf="item.fieldType == 'select' && item.isdependant == true">
                      <ng-container *ngFor="let list of dependantOptionList">
                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname &&
                            item.backendrequired == 'displayName'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                            <div class="dropdown-container">
                              <p-dropdown
                                [options]="list.options.dataList"
                                optionValue="displayName"
                                optionLabel="displayName"
                                filter="true"
                                filterBy="text"
                                placeholder="Select a {{ item.name }}"
                                [formControlName]="item.fieldname"
                                name="{{ i + 1 }}module"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              >
                              </p-dropdown>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname]?.errors &&
                                  (customerGroupForm.controls[item.fieldname].touched || submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname && item.backendrequired == 'displayId'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                            <div class="dropdown-container">
                              <p-dropdown
                                [options]="list.options.dataList"
                                optionValue="displayId"
                                optionLabel="displayName"
                                filter="true"
                                filterBy="displayName"
                                placeholder="Select a {{ item.name }}"
                                [formControlName]="item.fieldname"
                                name="{{ i + 1 }}module"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              >
                              </p-dropdown>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname]?.errors &&
                                  (customerGroupForm.controls[item.fieldname].touched || submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                    <ng-container *ngIf="item.fieldType == 'multi-select'">
                      <ng-container *ngFor="let list of multiOptionList">
                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname &&
                            item.backendrequired == 'displayName'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }} <span *ngIf="item.isMandatory">*</span></label>
                            <div class="multiselect">
                              <p-multiSelect
                                [options]="list.options.dataList"
                                optionValue="displayName"
                                defaultLabel="Select a {{ item.name }}"
                                optionLabel="displayName"
                                [formControlName]="item.fieldname"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              ></p-multiSelect>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname]?.errors &&
                                  (customerGroupForm.controls[item.fieldname].touched || submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container
                          *ngIf="
                            list.fieldname == item.fieldname && item.backendrequired == 'displayId'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ item.name }}<span *ngIf="item.isMandatory">*</span></label>
                            <div class="multiselect">
                              <p-multiSelect
                                [options]="list.options.dataList"
                                optionValue="displayId"
                                defaultLabel="Select a {{ item.name }}"
                                optionLabel="displayName"
                                [formControlName]="item.fieldname"
                                [disabled]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              ></p-multiSelect>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname]?.errors &&
                                  (customerGroupForm.controls[item.fieldname].touched || submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>

                    <ng-container *ngIf="item.fieldType == 'object' && item.child.length != 0">
                      <ng-container [formGroupName]="item.fieldname">
                        <ng-container *ngFor="let child of item.child">
                          <ng-container
                            *ngIf="
                              child.fieldType != 'select' &&
                              child.fieldType != 'multi-select' &&
                              child.fieldType != 'checkbox'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                              <div class="form-group">
                                <label
                                  >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                >
                                <input
                                  class="form-control"
                                  name="{{ child.fieldname }}"
                                  id="{{ child.fieldname }}"
                                  placeholder="Enter {{ child.name }}"
                                  type="{{ child.fieldType }}"
                                  [formControlName]="child.fieldname"
                                  [readonly]="
                                    ifReadonlyExtingInput &&
                                    customerGroupForm.controls[item.fieldname].value
                                  "
                                />
                                <div
                                  *ngIf="
                                    customerGroupForm.get(item.fieldname).get(child.fieldname)
                                      ?.errors?.required &&
                                    (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                      .touched ||
                                      submitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ child.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </ng-container>
                          <ng-container *ngIf="child.fieldType == 'select'">
                            <ng-container *ngFor="let list of childOptionList">
                              <ng-container
                                *ngIf="
                                  list.fieldname == child.fieldname &&
                                  child.backendrequired == 'displayId'
                                "
                              >
                                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                  <label
                                    >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                  >
                                  <div class="dropdown-container">
                                    <p-dropdown
                                      [options]="list.options.dataList"
                                      optionValue="displayId"
                                      optionLabel="displayName"
                                      filter="true"
                                      filterBy="text"
                                      placeholder="Select a {{ child.name }}"
                                      [formControlName]="list.fieldname"
                                      name="{{ i + 1 }}module"
                                      [disabled]="
                                        ifReadonlyExtingInput &&
                                        customerGroupForm.controls[item.fieldname].value
                                      "
                                    >
                                    </p-dropdown>
                                    <div
                                      *ngIf="
                                        customerGroupForm.get(item.fieldname).get(child.fieldname)
                                          ?.errors?.required &&
                                        (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                          .touched ||
                                          submitted)
                                      "
                                      class="errorWrap text-danger"
                                    >
                                      <div class="error text-danger">
                                        {{ child.name }} is required.
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                              <ng-container
                                *ngIf="
                                  list.fieldname == child.fieldname &&
                                  child.backendrequired == 'displayName'
                                "
                              >
                                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                  <label
                                    >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                  >
                                  <div class="dropdown-container">
                                    <p-dropdown
                                      [options]="list.options.dataList"
                                      optionValue="displayName"
                                      optionLabel="displayName"
                                      filter="true"
                                      filterBy="text"
                                      placeholder="Select a {{ child.name }}"
                                      [formControlName]="list.fieldname"
                                      name="{{ i + 1 }}module"
                                      [disabled]="
                                        ifReadonlyExtingInput &&
                                        customerGroupForm.controls[item.fieldname].value
                                      "
                                    >
                                    </p-dropdown>
                                    <div
                                      *ngIf="
                                        customerGroupForm.get(item.fieldname).get(child.fieldname)
                                          ?.errors?.required &&
                                        (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                          .touched ||
                                          submitted)
                                      "
                                      class="errorWrap text-danger"
                                    >
                                      <div class="error text-danger">
                                        {{ child.name }} is required.
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-container>
                          </ng-container>
                          <div *ngIf="child.fieldType == 'checkbox'">
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                              <label
                                >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                              >
                              <input
                                class="form-check-input"
                                name="{{ child.fieldname }}"
                                id="{{ child.fieldname }}"
                                placeholder="Enter {{ child.name }}"
                                type="checkbox"
                                [formControlName]="child.fieldname"
                                style="
                                  width: 20px;
                                  height: 20px;
                                  margin-left: 15px;
                                  margin-top: 50px;
                                "
                                [readonly]="
                                  ifReadonlyExtingInput &&
                                  customerGroupForm.controls[item.fieldname].value
                                "
                              />
                              <div
                                *ngIf="
                                  customerGroupForm.get(item.fieldname).get(child.fieldname)?.errors
                                    ?.required &&
                                  (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                    .touched ||
                                    submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ child.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>

                  <br />
                </div>
              </div>
            </fieldset>
          </div>
        </div>

        <!-- Present Address Details -->
        <fieldset style="margin: 1.5rem">
          <legend>Present Address Details</legend>
          <div class="boxWhite">
            <div [formGroup]="presentGroupForm">
              <div class="row" style="display: flex; flex-wrap: wrap">
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>Address </label>
                  <input
                    class="form-control"
                    formControlName="landmark"
                    id="landmark"
                    name="landmark"
                    placeholder="Enter Address"
                    type="text"
                    [readonly]="ifReadonlyExtingInput && presentGroupForm.value.landmark"
                  />
                  <div
                    *ngIf="submitted && presentGroupForm.controls.landmark.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">Address is required.</div>
                  </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>Landmark</label>
                  <input
                    class="form-control"
                    formControlName="landmark1"
                    id="landmark1"
                    placeholder="Enter Landmark "
                    type="text"
                    [readonly]="ifReadonlyExtingInput && presentGroupForm.value.landmark1"
                  />
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>{{ pincodeTitle }} </label>

                  <p-dropdown
                    (onChange)="selectPINCODEChange($event, 'present')"
                    [ngClass]="{
                      'is-invalid': submitted && presentGroupForm.controls.pincodeId.errors
                    }"
                    [options]="pincodeDD"
                    filter="true"
                    filterBy="pincode"
                    formControlName="pincodeId"
                    optionLabel="pincode"
                    optionValue="pincodeid"
                    placeholder="Select a {{ pincodeTitle }}"
                    [disabled]="presentGroupForm.value.pincodeId"
                    *ngIf="ifReadonlyExtingInput"
                  ></p-dropdown>
                  <p-dropdown
                    (onChange)="selectPINCODEChange($event, 'present')"
                    [ngClass]="{
                      'is-invalid': submitted && presentGroupForm.controls.pincodeId.errors
                    }"
                    [options]="pincodeDD"
                    filter="true"
                    filterBy="pincode"
                    formControlName="pincodeId"
                    optionLabel="pincode"
                    optionValue="pincodeid"
                    placeholder="Select a {{ pincodeTitle }}"
                    [disabled]="!customerGroupForm.value.serviceareaid || iscustomerEdit"
                    *ngIf="!ifReadonlyExtingInput"
                  ></p-dropdown>
                  <div></div>

                  <!-- <div *ngIf="!customerGroupForm.value.serviceareaid" class="errorWrap text-danger">
                    <div class="error text-danger">Please select Service Area first!</div>
                  </div> -->
                  <div
                    *ngIf="submitted && presentGroupForm.controls.pincodeId.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">{{ pincodeTitle }} is required.</div>
                  </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>{{ areaTitle }} </label>
                  <p-dropdown
                    (onChange)="selectAreaChange($event, 'present')"
                    [ngClass]="{
                      'is-invalid': submitted && presentGroupForm.controls.areaId.errors
                    }"
                    [options]="AreaListDD"
                    filter="true"
                    filterBy="areaId"
                    formControlName="areaId"
                    id="areaId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a {{ areaTitle }}"
                    [disabled]="true"
                    *ngIf="ifReadonlyExtingInput"
                  ></p-dropdown>
                  <p-dropdown
                    (onChange)="selectAreaChange($event, 'present')"
                    [ngClass]="{
                      'is-invalid': submitted && presentGroupForm.controls.areaId.errors
                    }"
                    [options]="AreaListDD"
                    filter="true"
                    filterBy="areaId"
                    formControlName="areaId"
                    id="areaId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a {{ areaTitle }}"
                    [disabled]="!customerGroupForm.value.serviceareaid || iscustomerEdit"
                    *ngIf="!ifReadonlyExtingInput"
                  ></p-dropdown>
                  <div
                    *ngIf="submitted && presentGroupForm.controls.areaId.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">{{ areaTitle }} is required.</div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>{{ cityTitle }} </label>
                  <select
                    class="form-control"
                    disabled
                    formControlName="cityId"
                    id="cityId"
                    name="cityId"
                    style="width: 100%"
                  >
                    <option value="">Select {{ cityTitle }}</option>
                    <option
                      *ngFor="let item of commondropdownService.cityListData"
                      value="{{ item.id }}"
                    >
                      {{ item.name }}
                    </option>
                  </select>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>{{ stateTitle }} </label>
                  <select
                    class="form-control"
                    disabled
                    formControlName="stateId"
                    id="stateId"
                    name="stateId"
                    style="width: 100%"
                  >
                    <option value="">Select {{ stateTitle }}</option>
                    <option
                      *ngFor="let item of commondropdownService.stateListData"
                      value="{{ item.id }}"
                    >
                      {{ item.name }}
                    </option>
                  </select>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>{{ countryTitle }} </label>
                  <select
                    class="form-control"
                    disabled
                    formControlName="countryId"
                    id="countryId"
                    name="countryId"
                    style="width: 100%"
                  >
                    <option value="">Select {{ countryTitle }}</option>
                    <option
                      *ngFor="let item of commondropdownService.countryListData"
                      value="{{ item.id }}"
                    >
                      {{ item.name }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </fieldset>
        <div *ngIf="permanentAddressArr[0].fields[0].child.length != 0">
          <fieldset style="margin: 1.5rem">
            <legend>Permanent Address Details</legend>

            <div class="boxWhite">
              <div class="row" style="margin: 2px; display: flex; flex-wrap: wrap">
                <ng-container *ngFor="let obj of permanentAddressArr; let i = index">
                  <ng-container *ngFor="let item of obj.fields">
                    <ng-container *ngIf="item.child.length != 0" [formGroupName]="item.fieldname">
                      <ng-container *ngFor="let child of item.child">
                        <ng-container
                          *ngIf="
                            child.fieldType != 'select' &&
                            child.fieldType != 'multi-select' &&
                            child.fieldType != 'checkbox'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <div class="form-group">
                              <label
                                >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                              >
                              <input
                                class="form-control"
                                name="{{ child.fieldname }}"
                                id="{{ child.fieldname }}"
                                placeholder="Enter {{ child.name }}"
                                type="{{ child.fieldType }}"
                                [formControlName]="child.fieldname"
                              />
                              <div
                                *ngIf="
                                  customerGroupForm.get(item.fieldname).get(child.fieldname)?.errors
                                    ?.required &&
                                  (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                    .touched ||
                                    submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ child.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngIf="child.fieldType == 'select'">
                          <ng-container *ngFor="let list of childOptionList">
                            <ng-container
                              *ngIf="
                                list.fieldname == child.fieldname &&
                                list.moduleName == obj.moduleName &&
                                child.backendrequired == 'displayId'
                              "
                            >
                              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                <label
                                  >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                >
                                <div class="dropdown-container">
                                  <p-dropdown
                                    [options]="list.options.dataList"
                                    optionValue="displayId"
                                    optionLabel="displayName"
                                    filter="true"
                                    filterBy="text"
                                    placeholder="Select a {{ child.name }}"
                                    [formControlName]="list.fieldname"
                                    name="{{ i + 1 }}module"
                                  >
                                  </p-dropdown>
                                  <div
                                    *ngIf="
                                      customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        ?.errors?.required &&
                                      (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        .touched ||
                                        submitted)
                                    "
                                    class="errorWrap text-danger"
                                  >
                                    <div class="error text-danger">
                                      {{ child.name }} is required.
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                            <ng-container
                              *ngIf="
                                list.fieldname == child.fieldname &&
                                child.backendrequired == 'displayName'
                              "
                            >
                              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                <label
                                  >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                >
                                <div class="dropdown-container">
                                  <p-dropdown
                                    [options]="list.options.dataList"
                                    optionValue="displayName"
                                    optionLabel="displayName"
                                    filter="true"
                                    filterBy="text"
                                    placeholder="Select a {{ child.name }}"
                                    [formControlName]="list.fieldname"
                                    name="{{ i + 1 }}module"
                                  >
                                  </p-dropdown>
                                  <div
                                    *ngIf="
                                      customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        ?.errors?.required &&
                                      (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        .touched ||
                                        submitted)
                                    "
                                    class="errorWrap text-danger"
                                  >
                                    <div class="error text-danger">
                                      {{ child.name }} is required.
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-container>
                        </ng-container>
                        <ng-container *ngIf="child.fieldType == 'checkbox'">
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ child.name }}<span *ngIf="child.isMandatory">*</span></label>
                            <input
                              class="form-check-input"
                              name="{{ child.fieldname }}"
                              id="{{ child.fieldname }}"
                              placeholder="Enter {{ child.name }}"
                              type="checkbox"
                              [formControlName]="child.fieldname"
                              style="width: 20px; height: 20px; margin-left: 15px; margin-top: 50px"
                            />
                            <div
                              *ngIf="
                                customerGroupForm.get(item.fieldname).get(child.fieldname)?.errors
                                  ?.required &&
                                (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                  .touched ||
                                  submitted)
                              "
                              class="errorWrap text-danger"
                            >
                              <div class="error text-danger">{{ child.name }} is required.</div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </div>
            </div>
          </fieldset>
        </div>

        <!-- Payment Address Details -->

        <!-- <fieldset style="margin: 1.5rem">
            <legend>Payment Address Details</legend>

            <div class="boxWhite">
              <div class="row" style="margin: 2px; display: flex; flex-wrap: wrap">
                <ng-container *ngFor="let obj of paymentAddressArr; let i = index">
                  <ng-container *ngFor="let item of obj.fields">
                    <ng-container *ngIf="item.child.length != 0" [formGroupName]="item.fieldname">
                      <ng-container *ngFor="let child of item.child">
                        <ng-container
                          *ngIf="
                            child.fieldType != 'select' &&
                            child.fieldType != 'multi-select' &&
                            child.fieldType != 'checkbox'
                          "
                        >
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <div class="form-group">
                              <label
                                >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                              >
                              <input
                                class="form-control"
                                name="{{ child.fieldname }}"
                                id="{{ child.fieldname }}"
                                placeholder="Enter {{ child.name }}"
                                type="{{ child.fieldType }}"
                                [formControlName]="child.fieldname"
                              />
                              <div
                                *ngIf="
                                  customerGroupForm.get(item.fieldname).get(child.fieldname)?.errors
                                    ?.required &&
                                  (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                    .touched ||
                                    submitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ child.name }} is required.</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngIf="child.fieldType == 'select'">
                          <ng-container *ngFor="let list of childOptionList">
                            <ng-container
                              *ngIf="
                                list.fieldname == child.fieldname &&
                                list.moduleName == obj.moduleName &&
                                child.backendrequired == 'displayId'
                              "
                            >
                              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                <label
                                  >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                >
                                <div class="dropdown-container">
                                  <p-dropdown
                                    [options]="list.options.dataList"
                                    optionValue="displayId"
                                    optionLabel="displayName"
                                    filter="true"
                                    filterBy="text"
                                    placeholder="Select a {{ child.name }}"
                                    [formControlName]="list.fieldname"
                                    name="{{ i + 1 }}module"
                                  ></p-dropdown>
                                  <div
                                    *ngIf="
                                      customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        ?.errors?.required &&
                                      (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        .touched ||
                                        submitted)
                                    "
                                    class="errorWrap text-danger"
                                  >
                                    <div class="error text-danger">
                                      {{ child.name }} is required.
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                            <ng-container
                              *ngIf="
                                list.fieldname == child.fieldname &&
                                child.backendrequired == 'displayName'
                              "
                            >
                              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                                <label
                                  >{{ child.name }}<span *ngIf="child.isMandatory">*</span></label
                                >
                                <div class="dropdown-container">
                                  <p-dropdown
                                    [options]="list.options.dataList"
                                    optionValue="displayName"
                                    optionLabel="displayName"
                                    filter="true"
                                    filterBy="text"
                                    placeholder="Select a {{ child.name }}"
                                    [formControlName]="list.fieldname"
                                    name="{{ i + 1 }}module"
                                  ></p-dropdown>
                                  <div
                                    *ngIf="
                                      customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        ?.errors?.required &&
                                      (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                        .touched ||
                                        submitted)
                                    "
                                    class="errorWrap text-danger"
                                  >
                                    <div class="error text-danger">
                                      {{ child.name }} is required.
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-container>
                        </ng-container>
                        <ng-container *ngIf="child.fieldType == 'checkbox'">
                          <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                            <label>{{ child.name }}<span *ngIf="child.isMandatory">*</span></label>
                            <input
                              class="form-check-input"
                              name="{{ child.fieldname }}"
                              id="{{ child.fieldname }}"
                              placeholder="Enter {{ child.name }}"
                              type="checkbox"
                              [formControlName]="child.fieldname"
                              style="width: 20px; height: 20px; margin-left: 15px; margin-top: 50px"
                            />
                            <div
                              *ngIf="
                                customerGroupForm.get(item.fieldname).get(child.fieldname)?.errors
                                  ?.required &&
                                (customerGroupForm.get(item.fieldname).get(child.fieldname)
                                  .touched ||
                                  submitted)
                              "
                              class="errorWrap text-danger"
                            >
                              <div class="error text-danger">{{ child.name }} is required.</div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </div>
            </div>
          </fieldset>
        </div> -->

        <!-- Plan Details -->
        <fieldset style="margin: 1.5rem" *ngIf="!isPlanOnDemand">
          <legend>Plan Details</legend>
          <div class="boxWhite">
            <div [formGroup]="planDataForm" class="row">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                <label>Plan Offer Price</label>
                <input
                  class="form-control"
                  formControlName="offerPrice"
                  placeholder="Enter Plan Offer Price "
                  readonly
                  type="number"
                />
                <br />
              </div>
              <div
                *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
              >
                <label>New Price (with Discount)</label>
                <input
                  (keyup)="discountvaluesetPercentage($event)"
                  class="form-control"
                  formControlName="discountPrice"
                  placeholder="Enter New Price "
                  type="number"
                  [readonly]="!ifcustomerDiscountField"
                />
                <br />
              </div>
            </div>

            <div>
              <div class="row">
                <div
                  [formGroup]="planCategoryForm"
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
                >
                  <label>Plan Category</label>
                  <p-dropdown
                    (onChange)="planSelectType($event)"
                    [disabled]="iscustomerEdit || serviceareaCheck"
                    [options]="planDetailsCategory"
                    filter="true"
                    filterBy="label"
                    formControlName="planCategory"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Select a Plan Group"
                  ></p-dropdown>
                  <div *ngIf="serviceareaCheck && !iscustomerEdit" class="errorWrap text-danger">
                    <div class="error text-danger">Please select Service Area first!</div>
                  </div>
                  <br />
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top">
                  <label>Bill To</label>
                  <p-dropdown
                    (onChange)="billtoSelectValue($event)"
                    [disabled]="
                      iscustomerEdit || payMappingListFromArray.length > 0 || ifplanisSubisuSelect
                    "
                    [options]="commondropdownService.billToData"
                    filter="true"
                    filterBy="text"
                    formControlName="billTo"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select Bill To"
                  ></p-dropdown>

                  <div
                    *ngIf="customerGroupForm.controls.billTo.errors"
                    class="errorWrap text-danger"
                  >
                    <div
                      *ngIf="customerGroupForm.controls.billTo.errors.max"
                      class="error text-danger"
                    >
                      Bill To required!
                    </div>
                  </div>
                </div>
                <!-- <div
              *ngIf="this.customerGroupForm.controls.invoiceType.enabled"
              class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
            >
              <label>Invoice Type*</label>
              <p-dropdown
                [ngClass]="{
                  'is-invalid': submitted && customerGroupForm.controls.invoiceType.errors
                }"
                [options]="invoiceType"
                formControlName="invoiceType"
                optionLabel="label"
                optionValue="value"
                placeholder="Select a Invoice Type"
              ></p-dropdown>
              <div
                *ngIf="submitted && customerGroupForm.controls.invoiceType.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="submitted && customerGroupForm.controls.invoiceType.errors.required"
                  class="error text-danger"
                >
                  Invoice Type is required.
                </div>
              </div>
            </div> -->
                <!-- <div
            *ngIf="ifPlanGroup && iscustomerEdit"
            class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
          >
            <label>Plan Group</label>
            <p-dropdown
              [disabled]="true"
              [options]="commondropdownService.PlanGroupDetails"
              filter="true"
              filterBy="planGroupName"
              formControlName="plangroupid"
              optionLabel="planGroupName"
              optionValue="planGroupId"
              placeholder="Select a Plan Group"
            ></p-dropdown>
  
            <br />
          </div> -->
                <div
                  *ngIf="ifPlanGroup && !iscustomerEdit"
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
                >
                  <label>Plan Group</label>
                  <p-dropdown
                    (onChange)="planGroupSelectSubisu($event)"
                    [options]="filterNormalPlanGroup"
                    [showClear]="true"
                    filter="true"
                    filterBy="planGroupName"
                    formControlName="plangroupid"
                    optionLabel="planGroupName"
                    optionValue="planGroupId"
                    placeholder="Select a Plan Group"
                  >
                    <ng-template let-data pTemplate="item">
                      <div class="item-drop1">
                        <span class="item-value1">
                          {{ data.planGroupName }}
                          <span *ngIf="data.category == 'Business Promotion'">
                            ( Business Promotion )</span
                          >
                        </span>
                      </div>
                    </ng-template>
                  </p-dropdown>

                  <br />
                </div>

                <div
                  *ngIf="
                    ifPlanGroup && isAdmin && customerGroupForm.value.billTo !== 'ORGANIZATION'
                  "
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
                >
                  <label>Discount (%)</label>
                  <input
                    (keyup)="discountPercentage($event)"
                    [value]="customerGroupForm.value.discount | number"
                    [readonly]="!customerGroupForm.value.plangroupid || !ifcustomerDiscountField"
                    class="form-control"
                    formControlName="discount"
                    id="discount"
                    name="discount"
                    placeholder="Enter a Discount "
                    type="number"
                  />

                  <div
                    *ngIf="customerGroupForm.controls.discount.errors"
                    class="errorWrap text-danger"
                  >
                    <div
                      *ngIf="customerGroupForm.controls.discount.errors.max"
                      class="error text-danger"
                    >
                      Maximum 99 Percentage allowed.
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION' && !ifPlanGroup"
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
                >
                  <label>Invoice To Org:</label>
                  <p-dropdown
                    [options]="isInvoiceData"
                    formControlName="isInvoiceToOrg"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Select Invoice to org or not"
                  ></p-dropdown>
                  <br />
                </div>
                <div class="row">
                  <div
                    *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION' && ifPlanGroup"
                    class="col-lg-3 col-md-3 col-sm-6 col-xs-12 top"
                  >
                    <label>Invoice To Org:</label>
                    <p-dropdown
                      (onChange)="valueChange($event)"
                      [disabled]="!customerGroupForm.value.plangroupid"
                      [options]="isInvoiceData"
                      formControlName="isInvoiceToOrg"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select Invoice to org or not"
                    ></p-dropdown>
                  </div>
                  <div
                    *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION' && ifPlanGroup"
                    class="col-lg-6 col-md-6 col-sm-6 col-xs-12 top"
                  >
                    <div class="form-group form-check inputcheckboxCenter">
                      <input class="inputcheckbox" formControlName="istrialplan" type="checkbox" />
                      <label
                        class="form-check-label"
                        for="acceptTerms"
                        style="margin-left: 1rem; margin-bottom: 0"
                        >Trial Plan
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <fieldset style="margin-top: 2rem" *ngIf="this.customerGroupForm.value.plangroupid">
                <legend>Plan Mapping List</legend>
                <div class="boxWhite">
                  <div class="row table-responsive">
                    <div class="col-lg-12 col-md-12 scrollbarPlangroupMappingList">
                      <table class="table">
                        <thead>
                          <tr>
                            <th style="text-align: left; padding-left: 8px">Service</th>
                            <th style="text-align: left; padding-left: 8px">Plan Name</th>
                            <th style="text-align: left; padding-left: 8px">Validity</th>
                            <th style="text-align: left; padding-left: 8px">Price</th>
                            <th style="text-align: left; padding-left: 8px">
                              <!-- *ngIf="planGroupData.category == 'Business Promotion'" -->

                              New Price
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="
                              let data of planGroupMapingList
                                | paginate
                                  : {
                                      id: 'PlanMappingData',
                                      itemsPerPage: PlanMappingitemsPerPage,
                                      currentPage: currentPagePlanMapping,
                                      totalItems: PlanMappingtotalRecords
                                    };
                              let index = index
                            "
                          >
                            <td style="padding-left: 8px">
                              {{ data.service }}
                            </td>
                            <td style="padding-left: 8px">
                              {{ data.plan.name }}
                            </td>
                            <td style="padding-left: 8px">
                              {{ data.plan.validity }}
                              {{ data.plan.unitsOfValidity }}
                            </td>
                            <td>
                              {{ data.plan.offerprice }}
                            </td>
                            <!-- <td *ngIf="planGroupData.category == 'Business Promotion'"> -->
                            <td>
                              {{ data.newofferprice }}
                            </td>
                          </tr>
                        </tbody>
                      </table>

                      <!-- <div class="row">
                      <div class="col-md-12">
                        <pagination-controls
                          id="PlanMappingData"
                          maxSize="10"
                          directionLinks="true"
                          previousLabel=""
                          nextLabel=""
                          (pageChange)="pageChangedPlanMapping($event)"
                        ></pagination-controls>
                      </div>
                    </div> -->
                    </div>
                  </div>
                  <br />
                </div>
              </fieldset>
              <div *ngIf="ifIndividualPlan">
                <div
                  [formGroup]="planGroupForm"
                  [ngClass]="{
                    customerplanHideCSS: iscustomerEdit,
                    customerplanShowCSS: !iscustomerEdit
                  }"
                  class="row"
                >
                  <div
                    [ngClass]="
                      customerGroupForm.value.billTo == 'ORGANIZATION'
                        ? 'col-lg-2 col-md-2 col-sm-6 col-xs-12'
                        : 'col-lg-2 col-md-2 col-sm-6 col-xs-12'
                    "
                  >
                    <p-dropdown
                      (onChange)="serviceBasePlanDATA($event)"
                      [ngClass]="{
                        'is-invalid': plansubmitted && planGroupForm.controls.service.errors
                      }"
                      [options]="serviceData"
                      filter="true"
                      filterBy="name"
                      formControlName="service"
                      optionLabel="name"
                      optionValue="name"
                      placeholder="Select a Service *"
                    ></p-dropdown>
                    <div></div>

                    <div
                      *ngIf="plansubmitted && planGroupForm.controls.service.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Service is required.</div>
                    </div>
                  </div>

                  <div
                    [ngClass]="
                      customerGroupForm.value.billTo == 'ORGANIZATION'
                        ? 'col-lg-2 col-md-2 col-sm-6 col-xs-12'
                        : 'col-lg-2 col-md-2 col-sm-6 col-xs-12'
                    "
                  >
                    <p-dropdown
                      (onChange)="getPlanValidity($event)"
                      [ngClass]="{
                        'is-invalid': plansubmitted && planGroupForm.controls.planId.errors
                      }"
                      [options]="plantypaSelectData"
                      filter="true"
                      filterBy="name"
                      formControlName="planId"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a Plan *"
                    >
                      <ng-template let-data pTemplate="item">
                        <div class="item-drop1">
                          <span class="item-value1">
                            {{ data.name }}
                            <span *ngIf="data.category == 'Business Promotion'">
                              ( Business Promotion )</span
                            >
                          </span>
                        </div>
                      </ng-template>
                    </p-dropdown>
                    <div></div>

                    <div
                      *ngIf="plansubmitted && planGroupForm.controls.planId.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Plan is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12">
                    <div style="display: flex">
                      <div style="width: 40%">
                        <input
                          class="form-control"
                          formControlName="validity"
                          id="validity"
                          min="1"
                          placeholder="Enter Validity"
                          readonly
                          type="number"
                        />
                      </div>
                      <div [formGroup]="validityUnitFormGroup" style="width: 60%; height: 34px">
                        <select
                          class="form-control"
                          disabled
                          formControlName="validityUnit"
                          style="width: 100%"
                        >
                          <option value="">Select Unit</option>
                          <option
                            *ngFor="let label of commondropdownService.validityUnitData"
                            value="{{ label.label }}"
                          >
                            {{ label.label }}
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12"
                  >
                    <div style="display: flex">
                      <input
                        class="form-control"
                        formControlName="offerprice"
                        id="offerprice"
                        placeholder="Old Offerprice"
                        readonly
                        type="number"
                      />
                    </div>
                  </div>
                  <div
                    *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12"
                  >
                    <div style="display: flex">
                      <input
                        [readonly]="ifplanisSubisuSelect"
                        class="form-control"
                        formControlName="newAmount"
                        id="newAmount"
                        placeholder="New Offerprice"
                        type="number"
                      />
                    </div>
                  </div>
                  <div
                    *ngIf="isAdmin && customerGroupForm.value.billTo !== 'ORGANIZATION'"
                    [ngClass]="
                      customerGroupForm.value.billTo == 'ORGANIZATION'
                        ? 'col-lg-1 col-md-1 col-sm-6 col-xs-12'
                        : 'col-lg-2 col-md-2 col-sm-6 col-xs-12'
                    "
                  >
                    <input
                      (keyup)="discountPercentage($event)"
                      [ngClass]="{
                        'is-invalid': plansubmitted && planGroupForm.controls.discount.errors
                      }"
                      [readonly]="!planGroupForm.value.planId || !ifcustomerDiscountField"
                      class="form-control"
                      formControlName="discount"
                      id="discount"
                      name="discount"
                      placeholder="Discount %"
                      type="number"
                    />
                    <div
                      *ngIf="planGroupForm.controls.discount.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="planGroupForm.controls.discount.errors.max"
                        class="error text-danger"
                      >
                        Maximum 99 Percentage allowed.
                      </div>
                    </div>
                    <div
                      *ngIf="plansubmitted && planGroupForm.controls.discount.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Discount is required.</div>
                    </div>
                  </div>
                  <div
                    *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'"
                    class="col-lg-2 col-md-2 col-sm-6 col-xs-12"
                  >
                    <div class="form-group form-check inputcheckboxCenter">
                      <input class="inputcheckbox" formControlName="istrialplan" type="checkbox" />
                      <label
                        class="form-check-label"
                        for="acceptTerms"
                        style="margin-left: 1rem; margin-bottom: 0"
                        >Trial Plan
                      </label>
                    </div>
                  </div>
                  <div class="col-lg-1 col-md-1 col-sm-6 col-xs-12" style="text-align: center">
                    <button
                      (click)="onAddplanMappingList()"
                      class="btn btn-primary"
                      style="object-fit: cover; padding: 5px 8px"
                      type="button"
                    >
                      <i aria-hidden="true" class="fa fa-plus-square"></i>
                      Add
                    </button>
                  </div>
                </div>
                <table class="table coa-table" style="margin-top: 3rem">
                  <thead>
                    <tr>
                      <th>Service*</th>
                      <th>Plan*</th>
                      <th>Validity*</th>
                      <th *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">offerPrice*</th>
                      <th *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                        New offerPrice
                      </th>
                      <th *ngIf="isAdmin && customerGroupForm.value.billTo !== 'ORGANIZATION'">
                        Discount (%)*
                      </th>
                      <th *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">Trial plan</th>
                      <th
                        [ngClass]="{
                          customerplanHideCSS: iscustomerEdit,
                          customerplanShowCSS: !iscustomerEdit
                        }"
                      >
                        Delete
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let row of payMappingListFromArray.controls
                          | paginate
                            : {
                                id: 'payMappingListFromArrayData',
                                itemsPerPage: paymappingItemPerPage,
                                currentPage: currentPagePayMapping,
                                totalItems: payMappinftotalRecords
                              };
                        let index = index
                      "
                    >
                      <td style="padding-left: 8px">
                        <!-- {{row.value.service}} -->
                        <select
                          [formControl]="row.get('service')"
                          class="form-control"
                          disabled
                          id="service"
                          name="service"
                          style="width: 100%"
                        >
                          <option value="">Select Service</option>
                          <option
                            *ngFor="let item of commondropdownService.planserviceData"
                            value="{{ item.name }}"
                          >
                            {{ item.name }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <!-- {{row.value.planId}} -->
                        <select
                          [formControl]="row.get('planId')"
                          class="form-control"
                          disabled
                          id="planId"
                          name="planId"
                          style="width: 100%"
                        >
                          <option value="">Select Plan</option>
                          <option
                            *ngFor="let item of commondropdownService.postpaidplanData"
                            value="{{ item.id }}"
                          >
                            {{ item.name }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <div style="display: flex">
                          <div style="width: 40%">
                            <input
                              [formControl]="row.get('validity')"
                              class="form-control"
                              id="validity"
                              min="1"
                              placeholder="Enter Validity"
                              readonly
                              type="number"
                            />
                          </div>
                          <div style="width: 60%; height: 34px">
                            <span *ngFor="let data of validityUnitFormArray.controls; index as j">
                              <span *ngIf="index == j">
                                <select
                                  [formControl]="data.get('validityUnit')"
                                  class="form-control"
                                  disabled
                                  style="width: 100%"
                                >
                                  <option value="">Select Unit</option>
                                  <option
                                    *ngFor="let label of commondropdownService.validityUnitData"
                                    value="{{ label.label }}"
                                  >
                                    {{ label.label }}
                                  </option>
                                </select>
                              </span>
                            </span>

                            <!-- <p-dropdown
                        [options]="
  
                        "
                        optionLabel="label"
                        optionValue="label"
                        [formControl]="row.get('validityUnit')"
                        filter="true"
                        filterBy="label"
                        placeholder="Select "
                      ></p-dropdown> -->
                          </div>
                        </div>
                      </td>
                      <td *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                        <input
                          [formControl]="row.get('offerPrice')"
                          [ngClass]="{
                            'is-invalid': plansubmitted && planGroupForm.controls.discount.errors
                          }"
                          class="form-control"
                          id="offerPrice"
                          name="offerPrice"
                          placeholder="Enter a OfferPrice *"
                          readonly
                          type="number"
                        />
                      </td>

                      <td *ngIf="customerGroupForm.value.billTo == 'ORGANIZATION'">
                        <input
                          [formControl]="row.get('newAmount')"
                          [ngClass]="{
                            'is-invalid': plansubmitted && planGroupForm.controls.discount.errors
                          }"
                          class="form-control"
                          id="newAmount"
                          name="newAmount"
                          placeholder="Enter a ewAmount *"
                          readonly
                          type="number"
                        />
                      </td>

                      <td *ngIf="isAdmin && customerGroupForm.value.billTo !== 'ORGANIZATION'">
                        <input
                          (keyup)="discountChange($event, index)"
                          [formControl]="row.get('discount')"
                          [value]="row.value.discount | number"
                          [readonly]="iscustomerEdit || !ifcustomerDiscountField"
                          class="form-control"
                          id="discount"
                          name="discount"
                          placeholder="Enter a Discount *"
                          type="number"
                        />
                      </td>
                      <td *ngIf="customerGroupForm.value.billTo !== 'ORGANIZATION'">
                        <input
                          [formControl]="row.get('istrialplan')"
                          [readonly]="iscustomerEdit"
                          class="inputcheckbox"
                          type="checkbox"
                        />
                      </td>
                      <td
                        [ngClass]="{
                          customerplanHideCSS: iscustomerEdit,
                          customerplanShowCSS: !iscustomerEdit
                        }"
                      >
                        <a
                          (click)="deleteConfirmonChargeField(index, 'Plan')"
                          href="javascript:void(0)"
                          id="deleteAtt"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </fieldset>
        <div style="display: flex; justify-content: center; align-items: center">
          <button type="submit" class="btn btn-primary" (click)="saveLead()">Add Lead</button>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- existingCustomerModal -->
<div class="modal fade" id="selectextingCustomer" role="dialog">
  <div class="modal-dialog" style="width: 80%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Existing Customer</h3>
      </div>
      <div class="modal-body">
        <h5>Search Customer</h5>
        <div class="row">
          <div class="col-lg-3 col-md-3 m-b-10">
            <p-dropdown
              [(ngModel)]="searchextingCustType"
              [options]="leadcustTypeList"
              [filter]="true"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Customer Type"
            ></p-dropdown>
          </div>
          <div class="col-lg-3 col-md-3 m-b-10">
            <p-dropdown
              (onChange)="selextingSearchOption($event)"
              [(ngModel)]="searchextingCustOption"
              [options]="searchExtingcustomerOption"
              [filter]="true"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Search Option"
            ></p-dropdown>
          </div>
          <div
            *ngIf="
              extingFieldEnable &&
              searchextingCustOption != 'status' &&
              searchextingCustOption !== 'serviceareaName' &&
              searchextingCustOption !== 'plan'
            "
            class="col-lg-3 col-md-3 m-b-10"
          >
            <input
              [(ngModel)]="searchextingCustValue"
              class="form-control"
              id="username"
              placeholder="Enter Search Detail"
              type="text"
            />
          </div>
          <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption === 'status'">
            <p-dropdown
              [options]="commondropdownService.CustomerStatusValue"
              optionValue="value"
              optionLabel="text"
              filter="true"
              filterBy="text"
              placeholder="Select a Status"
              [(ngModel)]="searchextingCustValue"
            ></p-dropdown>
          </div>

          <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption == 'serviceareaName'">
            <p-dropdown
              [options]="commondropdownService.serviceAreaList"
              optionValue="id"
              optionLabel="name"
              filter="true"
              filterBy="name"
              placeholder="Select a Servicearea"
              [(ngModel)]="searchextingCustValue"
            ></p-dropdown>
          </div>
          <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchextingCustOption == 'plan'">
            <p-dropdown
              [options]="commondropdownService.postpaidplanData"
              optionValue="id"
              optionLabel="name"
              filter="true"
              filterBy="name"
              placeholder="Select a Plan"
              [(ngModel)]="searchextingCustValue"
            ></p-dropdown>
          </div>
          <div *ngIf="extingFieldEnable" class="col-lg-3 col-md-3 col-sm-12">
            <button
              (click)="searchextingCustomer()"
              class="btn btn-primary"
              id="searchbtn"
              type="button"
              [disabled]="!searchextingCustValue && !searchextingCustType"
            >
              <i class="fa fa-search"></i>
              Search
            </button>
            <button
              (click)="clearSearchextingCustomer()"
              class="btn btn-default"
              id="searchbtn"
              type="reset"
            >
              <i class="fa fa-refresh"></i>
              Clear
            </button>
          </div>
        </div>
        <h5 style="margin-top: 15px">Select Customer</h5>
        <p-table
          #dt
          [(selection)]="selectedextingCust"
          [value]="extingCustomerList"
          responsiveLayout="scroll"
        >
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 5rem"></th>
              <th>Name</th>
              <th>User Name</th>
            </tr>
          </ng-template>
          <ng-template let-extingCustomerList let-rowIndex="rowIndex" pTemplate="body">
            <tr>
              <td>
                <p-tableRadioButton [value]="extingCustomerList"></p-tableRadioButton>
              </td>
              <td>
                {{ extingCustomerList.name }}
                {{ extingCustomerList.lastname }}
              </td>
              <td>{{ extingCustomerList.username }}</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="summary">
            <p-paginator
              (onPageChange)="extingPaginate($event)"
              [first]="newFirstexting"
              [rows]="extingCustomerListdataitemsPerPage"
              [totalRecords]="extingCustomerListdatatotalRecords"
            ></p-paginator>
          </ng-template>
        </p-table>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <button
            (click)="SelExtingCustomer('')"
            [disabled]="this.selectedextingCust.length == 0"
            class="btn btn-primary"
            style="object-fit: cover; padding: 5px 8px"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button>
          <button (click)="modalCloseextingCustomer()" class="btn btn-danger btn-sm" type="button">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
