<p-dialog
  header="Select Customer"
  [(visible)]="displayAssignPlanInventoryModal"
  [styleClass]="'nearSearchModalLocation'"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  [baseZIndex]="1000000"
  (onHide)="modalCloseParentCustomer()"
>
  <ng-template pTemplate="content">
    <div class="modal-body">
      <h5>Search {{ type }} Customer</h5>
      <div class="row">
        <div class="col-lg-3 col-md-3 m-b-10">
          <p-dropdown
            (onChange)="selParentSearchOption($event)"
            [(ngModel)]="searchParentCustOption"
            [options]="commondropdownService.customerSearchOption"
            [filter]="true"
            filterBy="label"
            optionLabel="label"
            optionValue="value"
            placeholder="Select a Search Option"
          ></p-dropdown>
        </div>
        <div
          *ngIf="
            parentFieldEnable &&
            searchParentCustOption != 'status' &&
            searchParentCustOption !== 'serviceareaName' &&
            searchParentCustOption !== 'plan'
          "
          class="col-lg-3 col-md-3 m-b-10"
        >
          <input
            [(ngModel)]="searchParentCustValue"
            class="form-control"
            id="username"
            placeholder="Enter Search Detail"
            type="text"
            (keydown.enter)="searchParentCustomer()"
          />
        </div>
        <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
          <p-dropdown
            [options]="commondropdownService.CustomerStatusValue"
            optionValue="value"
            optionLabel="text"
            filter="true"
            filterBy="text"
            placeholder="Select a Status"
            [(ngModel)]="searchParentCustValue"
          ></p-dropdown>
        </div>

        <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
          <p-dropdown
            [options]="commondropdownService.serviceAreaList"
            optionValue="id"
            optionLabel="name"
            filter="true"
            filterBy="name"
            placeholder="Select a Servicearea"
            [(ngModel)]="searchParentCustValue"
          ></p-dropdown>
        </div>
        <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
          <p-dropdown
            [options]="commondropdownService.postpaidplanData"
            optionValue="id"
            optionLabel="name"
            filter="true"
            filterBy="name"
            placeholder="Select a Plan"
            [(ngModel)]="searchParentCustValue"
          ></p-dropdown>
        </div>
        <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
          <button
            (click)="searchParentCustomer()"
            class="btn btn-primary"
            id="searchbtn"
            type="button"
          >
            <i class="fa fa-search"></i>
            Search
          </button>
          <button
            (click)="clearSearchParentCustomer()"
            class="btn btn-default"
            id="searchbtn"
            type="reset"
          >
            <i class="fa fa-refresh"></i>
            Clear
          </button>
        </div>
      </div>
      <h5 style="margin-top: 15px">Select {{ type }} Customer</h5>
      <p-table #dt [(selection)]="selectedCust" [value]="prepaidParentCustomerList">
        <!-- <ng-template pTemplate="caption">
          <div class="flex align-items-center justify-content-between">
            <span class="p-input-icon-left">
              <input
                class="form-control"
                pInputText
                type="text"
                (input)="dt.filterGlobal($event.target.value, 'contains')"
                placeholder="Search..."
              />
            </span>
          </div>
        </ng-template> -->
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 5rem"></th>
            <th>Name</th>
            <th>User Name</th>
          </tr>
        </ng-template>
        <ng-template let-prepaidParentCustomerList let-rowIndex="rowIndex" pTemplate="body">
          <tr>
            <td>
              <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
            </td>
            <td>{{ prepaidParentCustomerList.name }}</td>
            <td>{{ prepaidParentCustomerList.username }}</td>
          </tr>
        </ng-template>

        <ng-template pTemplate="summary">
          <p-paginator
            (onPageChange)="paginate($event)"
            [first]="newFirst"
            [rows]="parentCustomerListdataitemsPerPage"
            [totalRecords]="parentCustomerListdatatotalRecords"
          ></p-paginator>
        </ng-template>
      </p-table>
    </div>
    <div class="modal-footer">
      <div class="addUpdateBtn">
        <button
          (click)="saveSelCustomer()"
          [disabled]="this.selectedCust.length == 0"
          class="btn btn-primary"
          style="object-fit: cover; padding: 5px 8px"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
        <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
          Close
        </button>
      </div>
    </div>
  </ng-template>
</p-dialog>
