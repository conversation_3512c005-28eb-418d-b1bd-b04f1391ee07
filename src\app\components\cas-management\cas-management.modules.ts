import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { CASManagementComponent } from "./cas-management.component";
import { DeactivateService } from "src/app/service/deactivate.service";

const routes = [
  { path: "", component: CASManagementComponent, canDeactivate: [DeactivateService] },
];

@NgModule({
  declarations: [CASManagementComponent],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],
})
export class casManagementModules {}
