<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            Search {{ customerLedgerDetailData?.title }}
            {{ customerLedgerDetailData?.firstname }}
            {{ customerLedgerDetailData?.lastname }} Invoice
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="searchInvoiceCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchInvoiceCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchInvoiceCust">
        <div class="panel-body table-responsive">
          <div class="searchForm">
            <form [formGroup]="searchInvoiceMasterFormGroup">
              <div class="row">
                <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                  <div class="form-group">
                    <label>Invoice No</label>
                    <input
                      class="form-control"
                      formControlName="docnumber"
                      placeholder="Invoice No"
                      type="text"
                    />
                  </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                  <div class="form-group">
                    <label>Customer Name</label>
                    <select class="form-control" disabled name="customerid" style="width: 100%">
                      <option>
                        {{ customerLedgerDetailData?.title }}
                        {{ customerLedgerDetailData?.custname }}
                      </option>
                    </select>
                  </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                  <div class="form-group">
                    <label>Bill From Date</label>
                    <input
                      class="form-control"
                      formControlName="billfromdate"
                      placeholder="Enter Pay From Date"
                      type="date"
                    />
                  </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                  <div class="form-group">
                    <label>Bill To Date</label>
                    <input
                      class="form-control"
                      formControlName="billtodate"
                      placeholder="Enter Pay To Date"
                      type="date"
                    />
                  </div>
                </div>
              </div>

              <div class="addUpdateBtn">
                <button (click)="searchInvoices()" class="btn btn-primary" type="submit">
                  <i class="fa fa-search"></i>
                  Search Invoice
                </button>
                <button
                  (click)="clearSearchinvoiceMaster()"
                  class="btn btn-default"
                  id="searchbtn"
                  type="submit"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div
        class="panel-heading"
        style="display: flex; justify-content: space-between; align-items: center"
      >
        <h3 class="panel-title" style="margin: 0">
          {{ customerLedgerDetailData?.title }}
          {{ customerLedgerDetailData?.firstname }}
          {{ customerLedgerDetailData?.lastname }} Invoice Details
        </h3>
        <div style="display: flex; align-items: center; gap: 10px">
          <button type="button" class="btn btn-primary btn-grace-period" (click)="getAuditData('')">
            Grace Period
          </button>
          <button
            aria-controls="invoiceCustomerDetails"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#invoiceCustomerDetails"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <app-invoice-detalis-model
        *ngIf="isInvoiceDetail"
        [invoiceID]="invoiceID"
        [custID]="custID"
        [sourceType]="'customer'"
        (closeInvoiceDetails)="closeInvoiceDetails()"
      ></app-invoice-detalis-model>

      <app-invoice-payment-details-modal
        [invoiceId]="invoiceId"
        dialogId="invoicePaymentDetailModal"
      >
      </app-invoice-payment-details-modal>

      <div class="panel-collapse collapse in" id="invoiceCustomerDetails">
        <div class="panel-body table-responsive">
          <div class="row">
            <div *ngIf="invoiceMasterListData.length !== 0" class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Customer</th>
                    <th>Invoice Number</th>
                    <th>Purchase By</th>
                    <th>Billable To</th>
                    <th>Total Amount</th>
                    <th>Adjusted Amount</th>
                    <th>Unpaid Amount</th>
                    <th>Bill Run Status</th>
                    <th>Bill Date</th>
                    <th>Payment Status</th>
                    <!-- <th>Reference Name</th> -->
                    <th>Payment Owner</th>
                    <!-- <th>Document</th> -->
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let invoice of invoiceMasterListData
                        | paginate
                          : {
                              id: 'searchinvoiceMasterPageData',
                              itemsPerPage: invoiceMasteritemsPerPage,
                              currentPage: currentPageinvoiceMasterSlab,
                              totalItems: invoiceMastertotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <div>
                        {{ invoice.customerName }}
                      </div>
                    </td>
                    <td *ngIf="invoice.docnumber !== '' && invoice.docnumber !== null">
                      <div
                        (click)="openInvoiceModal('InvoiceDetailModal', invoice)"
                        class="curson_pointer"
                        style="color: #f7b206"
                      >
                        {{ invoice.docnumber }}
                      </div>
                    </td>
                    <td *ngIf="invoice.docnumber == '' || invoice.docnumber === null">
                      <div
                        (click)="openInvoiceModal('InvoiceDetailModal', invoice)"
                        class="curson_pointer"
                        style="color: #f7b206"
                      >
                        N/A
                      </div>
                    </td>
                    <td>{{ invoice.createdByName }}</td>
                    <td>
                      {{
                        invoice.billableToName !== null && invoice.billableToName !== ""
                          ? invoice.billableToName
                          : "-"
                      }}
                    </td>
                    <td>
                      <!-- <span> -->
                      {{ invoice.totalamount | currency: currency : "symbol" : "1.2-2" }}
                      <!-- </span> -->
                    </td>
                    <td>
                      <span
                        (click)="openInvoicePaymentModal('invoicePaymentDetailModal', invoice.id)"
                        *ngIf="invoice.adjustedAmount"
                        class="curson_pointer"
                        style="color: #f7b206"
                      >
                        {{ invoice.adjustedAmount | currency: currency : "symbol" : "1.2-2" }}
                      </span>
                      <span *ngIf="!invoice.adjustedAmount">0</span>
                    </td>
                    <td>
                      {{
                        invoice.totalamount - invoice.adjustedAmount
                          | currency: currency : "symbol" : "1.2-2"
                      }}
                    </td>
                    <!-- <td>
                      <div class="badge badge-success">
                        {{ invoice.billrunstatus }}
                      </div>
                    </td> -->
                    <td>
                      <span
                        class="badge badge-success"
                        [ngStyle]="{
                          'background-color':
                            invoice.billrunstatus?.toLowerCase() === 'cancelled'
                              ? 'gray'
                              : '#28a745',
                          color: '#fff'
                        }"
                      >
                        {{ invoice.billrunstatus || "-" }}
                      </span>
                    </td>
                    <td style="font-weight: 400">
                      {{ invoice.billdate | date: "yyyy-MM-dd" }}
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'unpaid'">
                      <span class="badge badge-danger">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'partialy paid'">
                      <span
                        class="badge badge-warning"
                        style="background-color: #f7b206; color: #fff"
                      >
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'fully paid'">
                      <span class="badge badge-success">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'payable'">
                      <span class="badge badge-success">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'approved'">
                      <span class="badge badge-success">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'cancelled'">
                      <span class="badge badge-danger" style="background-color: gray; color: #fff">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'rejected'">
                      <span class="badge badge-danger">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'pending'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'pending sent'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'pending accepted'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'partial pending'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'clear'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="invoice.paymentStatus?.toLowerCase() === 'void'">
                      <span class="badge badge-primary">
                        {{ invoice.paymentStatus || "-" }}
                      </span>
                    </td>
                    <td *ngIf="!invoice.paymentStatus">
                      <span>–</span>
                    </td>
                    <td>
                      {{ invoice.paymentowner }}
                    </td>
                    <td class="btnAction">
                      <a
                        (click)="downloadPDFINvoice(invoice.id, invoice.customerName)"
                        *ngIf="
                          customerLedgerDetailData?.status !== 'Terminate' &&
                          (invoice.billrunstatus == 'Exported' ||
                            invoice.billrunstatus == 'Printed')
                        "
                        href="javascript:void(0)"
                        id="edit-button"
                        title="Download"
                        type="button"
                      >
                        <img src="assets/img/pdf.png" style="width: 25px; height: 25px" />
                      </a>
                      <button
                        [disabled]="invoice.docnumber == '' || invoice.docnumber === null"
                        (click)="generatePDFInvoice(invoice.id)"
                        *ngIf="
                          customerLedgerDetailData?.status !== 'Terminate' &&
                          invoice.billrunstatus == 'Generated' &&
                          generateAccess
                        "
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Generate"
                        type="button"
                      >
                        <img src="assets/img/generate.jpg" style="width: 25px; height: 25px" />
                      </button>

                      <button
                        (click)="invoicePaymentList(invoice)"
                        *ngIf="
                          customerLedgerDetailData?.status !== 'Terminate' &&
                          invoicePaymentListAccess
                        "
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Invoice Payment List"
                        type="button"
                      >
                        <img src="assets/img/icons-03.png" style="width: 25px; height: 25px" />
                      </button>
                      <button
                        (click)="invoiceRemarks(invoice, 'void')"
                        *ngIf="
                          invoice.billrunstatus != 'Cancelled' &&
                          voidInvoiceAcces &&
                          invoice.billrunstatus != 'VOID' &&
                          invoice.billrunstatus !== 'Cancelled' &&
                          customerLedgerDetailData?.status !== 'Terminate'
                        "
                        [disabled]="
                          invoice.creditDocId.length > 0 ||
                          invoice.totalamount - invoice.adjustedAmount <= 0
                        "
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Void Invoice"
                        type="button"
                      >
                        <img
                          src="assets/img/20_Void-Invoice_Y.png"
                          style="width: 25px; height: 25px"
                        />
                      </button>

                      <a
                        *ngIf="
                          invoice.billrunstatus != 'Cancelled' &&
                          reprintInvoiceAccess &&
                          invoice.billrunstatus !== 'Cancelled' &&
                          customerLedgerDetailData?.status !== 'Terminate'
                        "
                        (click)="InvoiceReprint(invoice.id, invoice.customerName)"
                        href="javascript:void(0)"
                        title="Reprint Invoice"
                      >
                        <img src="assets/img/09_Reprint-Invoice.png" />
                      </a>
                      <button
                        [disabled]="
                          invoice.creditDocId.length > 0 ||
                          invoice.totalamount - invoice.adjustedAmount <= 0
                        "
                        (click)="invoiceRemarks(invoice, 'cancelRegenerate')"
                        *ngIf="
                          invoice.billrunstatus != 'Cancelled' &&
                          cancelAndRegenerateAccess &&
                          invoice.billrunstatus !== 'Cancelled' &&
                          (invoice.paymentStatus === null ||
                            invoice.paymentStatus === '' ||
                            invoice.paymentStatus.toLowerCase() == 'unpaid' ||
                            invoice.paymentStatus.toLowerCase() == 'clear' ||
                            invoice.paymentStatus.toLowerCase() == 'partial pending' ||
                            invoice.paymentStatus.toLowerCase() == 'partialy paid' ||
                            invoice.paymentStatus.toLowerCase() == 'fully paid') &&
                          customerLedgerDetailData?.status !== 'Terminate'
                        "
                        href="javascript:void(0)"
                        title="Cancel and Regenerate Invoice"
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                      >
                        <img src="assets/img/21_Cancel-and-Regenerate-Invoice_Y.png" />
                      </button>
                      <a
                        *ngIf="
                          viewInvoiceAccess &&
                          invoice.billrunstatus !== 'Cancelled' &&
                          customerLedgerDetailData?.status !== 'Terminate'
                        "
                        (click)="viewInvoice(invoice.id, invoice.customerName)"
                        href="javascript:void(0)"
                        title="View Invoice"
                      >
                        <img src="assets/img/22_Reprint-invoice_Y.png" />
                      </a>
                      <button
                        (click)="displayNote('invoice')"
                        class="approve-btn"
                        class="curson_pointer"
                        data-backdrop="static"
                        data-keyboard="false"
                        data-target="#note"
                        data-toggle="modal"
                        style="border: none; background: transparent; padding: 0; margin: 0px 2px"
                        title="Audit"
                        type="button"
                        *ngIf="
                          (invoice.billrunstatus == 'Cancelled' ||
                            invoice.billrunstatus === 'VOID') &&
                          customerLedgerDetailData?.status !== 'Terminate'
                        "
                      >
                        <img class="icon" src="assets/img/icons-03.png" />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="Invoice Payment"
                        *ngIf="invoice.totalamount - invoice.adjustedAmount >= 1"
                        [disabled]="invoice.billrunstatus == 'Cancelled'"
                        (click)="openPaymentGatewaysforInvoicePayment(invoice)"
                      >
                        <img
                          src="assets/img/19_Promise-to-Pay_Y.png"
                          alt="Invoice Payment"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="send TRA Invoice"
                        *ngIf="
                          sendTraInvoiceAccess && isTraEnable === true && invoice.qrCode === null
                        "
                        (click)="sendInvoiceTraIntigration(invoice)"
                      >
                        <img
                          src="assets/img/icons-02.png"
                          alt="Invoice Intigration"
                          style="width: 25px; height: 25px; margin-right: 5px"
                        />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="WriteOff Payment"
                        *ngIf="invoice.paymentStatus != 'Fully Paid'"
                        (click)="openWriteOff(invoice)"
                      >
                        <img
                          src="assets/img/01_Promise-to-Pay.png"
                          alt="WriteOff Payment"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                      <a
                        id="edit-button"
                        class="detailOnAnchorClick"
                        href="javascript:void(0)"
                        type="button"
                        title="Email Invoice"
                        *ngIf="
                          invoice.billrunstatus == 'Exported' ||
                          invoice.billrunstatus == 'Distributed'
                        "
                        (click)="sendemailinvoice(invoice.id)"
                      >
                        <img style="width: 25px; height: 25px" src="assets/img/icons-02.png" />
                      </a>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="Grace Period"
                        (click)="openGracePeriod(invoice)"
                        [disabled]="invoice.billrunstatus === 'Cancelled'"
                      >
                        <img
                          src="assets/img/D_Extend-Expiry-Date_Y.png"
                          alt="Grace Period"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                      <!-- *ngIf="invoice.custPlanStatus.toLowerCase() == 'active'" -->
                    </td>
                  </tr>
                </tbody>
              </table>
              <br />
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedinvoiceMasterList($event)"
                  directionLinks="true"
                  id="searchinvoiceMasterPageData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                >
                </pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalItemPerPageInvoice($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
            <div *ngIf="invoiceMasterListData.length === 0" class="col-lg-12 col-md-12">
              Invoice data not found
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Add Remark"
  [(visible)]="Remark"
  [style]="{ width: '35%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark*</label>
        <textarea [(ngModel)]="invoiceCancelRemarks" class="form-control" name="remark"></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="addInvoiceRemarks()"
      [disabled]="!invoiceCancelRemarks"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Remarks
    </button>
    <button class="btn btn-danger" id="searchbtn" type="reset" (click)="closeInvoiceCancelremark()">
      <i class="fa fa-refresh"></i>
      Close
    </button>
  </div>
</p-dialog>

<div aria-labelledby="myModalLabel" class="modal fade" id="note" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document" style="width: 75%">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Audit</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <table class="table" *ngIf="!planNotes">
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Date</th>
                  <th>Remark</th>
                  <th>Modified By</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of showdata">
                  <td>
                    <span *ngIf="data.operationType === 'Cancel_Regenerate'">
                      Cancel and Regenerate Invoice
                    </span>
                    <span *ngIf="data.operationType == null"> - </span>
                  </td>
                  <td *ngIf="data.operationType === 'Change_Plan'">Change Plan</td>
                  <td *ngIf="data.operationType === 'VOID'">Void Invoice</td>
                  <td>{{ data.createdate }}</td>
                  <td>
                    {{ data.remarks }}
                    <span *ngIf="data.remark == null"> </span>
                  </td>

                  <td>{{ data.lastModifiedByName }}</td>
                </tr>
              </tbody>
            </table>
            <br />
          </div>
        </div>
      </div>
      <div class="modal-footer"></div>
    </div>
  </div>
</div>

<p-dialog
  header="Payment Details"
  [(visible)]="displayPaymentDetails"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12" style="margin-top: 3rem">
        <table class="table">
          <thead>
            <tr>
              <th class="widthCheckboxColom">
                <div class="centerCheckbox">
                  <p-checkbox
                    (onChange)="checkInvoicePaymentAll($event)"
                    [(ngModel)]="ispaymentChecked"
                    binary="true"
                    name="allChecked"
                  ></p-checkbox>
                </div>
              </th>
              <th>Recipt Number</th>
              <th>Payment Date</th>
              <th>Payment Amount</th>
              <th>Adjusted Amount</th>
              <th>Remaining Amount</th>
              <th>Payment Mode</th>
              <th>Type</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let data of invoicePaymentData
                  | paginate
                    : {
                        id: 'invoicePaymentModal',
                        itemsPerPage: invoicePaymentItemPerPage,
                        currentPage: currentPageinvoicePaymentList,
                        totalItems: invoicePaymenttotalRecords
                      };
                index as i
              "
            >
              <td>
                <div class="centerCheckbox">
                  <p-checkbox
                    (onChange)="addInvoicePaymentChecked(data.id, $event)"
                    [(ngModel)]="data.isSinglepaymentChecked"
                    [binary]="true"
                    [inputId]="data.id"
                    [value]="data.isSinglepaymentChecked"
                    class="p-field-checkbox"
                  ></p-checkbox>
                </div>
              </td>
              <td>{{ data.reciptNo }}</td>
              <td>{{ data.paymentdate }}</td>
              <td>{{ data.amount | number: "1.2-2" }}</td>
              <td>{{ data.adjustedAmount | number: "1.2-2" }}</td>
              <td>{{ data.amount - data.adjustedAmount | number: "1.2-2" }}</td>
              <td>{{ data.paymode }}</td>
              <td>{{ data.type }}</td>
            </tr>
          </tbody>
        </table>
        <pagination-controls
          (pageChange)="pageChangedInvoicePaymentList($event)"
          directionLinks="true"
          id="invoicePaymentModal"
          maxSize="10"
          nextLabel=""
          previousLabel=""
        ></pagination-controls>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        #closebutton
        (click)="invoicePaymentAdjsment()"
        [disabled]="allchakedPaymentData.length == 0"
        class="btn btn-success btn-sm"
        data-dismiss="modal"
        type="button"
      >
        Manual Adjustment
      </button>
    </div>
    <div class="addUpdateBtn" style="margin-left: 1.5rem">
      <button
        #closebutton
        (click)="invoicePaymentCloseModal()"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        type="button"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Payment Gateway Method"
  [(visible)]="displayInvoicePaymentDialog"
  [modal]="true"
  [style]="{ width: '50%' }"
  [draggable]="false"
  [resizable]="false"
>
  <div class="card" style="text-align: center">
    <div *ngFor="let config of savedConfig">
      <p-card
        [style]="{
          margin: '25px',
          'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.3)'
        }"
      >
        <div class="p-card-title" style="color: #f7b206; font-size: 20px">
          {{ config.paymentConfigName }}
        </div>
        <div class="p-card-content" style="width: 100%">
          <div>
            {{ config.paymentGatewayInfo?.substring(0, 50) }}
          </div>
          <div style="white-space: normal">
            {{ config.paymentGatewayInfo?.substring(50) }}
          </div>
        </div>
        <div class="p-card-footer">
          <button
            class="btn btn-primary"
            id="submit"
            type="submit"
            (click)="invoicePaymentpaymentGateway(config)"
          >
            Proceed
          </button>
        </div>
      </p-card>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Mobile number"
  [(visible)]="mpinModal"
  [modal]="true"
  [style]="{ width: '30%' }"
  [draggable]="false"
  (onHide)="hideMpinModal()"
  [resizable]="false"
>
  <form [formGroup]="mpinForm">
    <div class="form-group">
      <label style="margin-top: 10px">Mobile number: <span class="reqSign">*</span></label>
      <div style="display: flex">
        <div style="width: 30%">
          <p-dropdown
            [filter]="true"
            [options]="countries"
            formControlName="countryCode"
            id="countryCode"
            optionLabel="dial_code"
            optionValue="dial_code"
            placeholder="+91"
            appendTo="body"
          ></p-dropdown>
        </div>
        <div style="width: 70%">
          <input
            (input)="onInputMobile($event)"
            [attr.maxlength]="commondropdownService.maxMobileLength"
            class="form-control"
            type="text"
            name="mobileNo"
            id="mobileNo"
            formControlName="mobileNumber"
            [ngClass]="{
              'is-invalid': isMpinFormSubmitted && mpinForm.controls.mobileNumber.errors
            }"
            placeholder="Enter mobile number"
          />

          <div
            *ngIf="isMpinFormSubmitted && mpinForm.controls.mobileNumber.errors"
            class="errorWrap text-danger"
          >
            <div *ngIf="mpinForm.controls.mobileNumber.errors.required">
              Mobile Number is required.
            </div>

            <div
              *ngIf="
                mpinForm.controls.mobileNumber.errors.minlength ||
                mpinForm.controls.mobileNumber.errors.maxlength
              "
            >
              <ng-container
                *ngIf="
                  commondropdownService.minMobileLength === commondropdownService.maxMobileLength;
                  else mpinRangeError
                "
              >
                Mobile Number must be exactly
                {{ commondropdownService.minMobileLength }} digits.
              </ng-container>

              <ng-template #mpinRangeError>
                Mobile Number must be between
                {{ commondropdownService.minMobileLength }} and
                {{ commondropdownService.maxMobileLength }} digits.
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
  <ng-template pTemplate="footer">
    <div class="btnGroup text-center">
      <button
        class="btn btn-primary btnStyle"
        (click)="invoicePaymentGateway()"
        style="margin-right: 10px"
        [disabled]="inputMobile !== '' || mobileError"
      >
        Buy
      </button>
      <button class="btn btn-danger btnStyle" (click)="hideMpinModal()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>

<!-- Payment Confirmation Model -->
<p-dialog
  header="Payment Confirmation"
  [(visible)]="paymentConfirmationModal"
  [modal]="true"
  [breakpoints]="{ '1024px': '75vw', '767px': '90vw' }"
  [style]="{ width: '50vw' }"
  [draggable]="false"
  (onHide)="hidepaymentConfirmDialog()"
  [resizable]="false"
>
  <h5 class="modalHeadTxt">
    Payment is in the approval flow one It is approved, you will get the confirmation message
    through SMS
  </h5>

  <button
    class="btn btn-success btnStyle"
    [disabled]="exitBuy"
    (click)="hidepaymentConfirmDialog()"
  >
    <i class="fa-solid fa-xmark"></i> Close<span *ngIf="exitBuy"> ({{ paymentstatusCount }}) </span>
  </button>
</p-dialog>

<!-- Payment Success Model -->
<p-dialog
  header="Payment Success"
  [(visible)]="paymentSucessModel"
  [modal]="true"
  [breakpoints]="{ '1024px': '75vw', '767px': '90vw' }"
  [style]="{ width: '50vw' }"
  [draggable]="false"
  (onHide)="hidepaymentSucessDialog()"
  [resizable]="false"
>
  <h5 class="modalHeadTxt plan-sucess-content">Plan purchased successfully</h5>

  <button class="btn btn-success btnStyle plan-sucess-btn" (click)="hidepaymentSucessDialog()">
    OK
  </button>
</p-dialog>
<p-dialog
  header="Add Write Off"
  [(visible)]="isWriteOffModel"
  [modal]="true"
  [style]="{ width: '30%' }"
  [draggable]="false"
  (onHide)="closeWriteOff()"
  [resizable]="false"
>
  <div class="form-group">
    <label style="margin-top: 10px">Write Off Amount: </label>
    <input
      type="text"
      name="writeOffAmount"
      id="writeOffAmount"
      [(ngModel)]="writeOffAmount"
      placeholder="Enter Write Off Amount"
      class="form-control"
      min="0"
      (keypress)="keypress($event)"
    />
  </div>
  <div class="row">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
      <label>Remark*</label>
      <textarea [(ngModel)]="writeOffRemark" class="form-control" name="remark"></textarea>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="btnGroup text-center">
      <button
        class="btn btn-primary btnStyle"
        (click)="checkWriteOff()"
        style="margin-right: 10px"
        [disabled]="writeOffAmountFirst < writeOffAmount || !writeOffRemark"
      >
        Add To Write Off
      </button>
      <button class="btn btn-danger btnStyle" (click)="closeWriteOff()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  header="Add Grace Period"
  [(visible)]="isGracePeriodModel"
  [modal]="true"
  [style]="{ width: '30%' }"
  [draggable]="false"
  (onHide)="closeGracePeriod()"
  [resizable]="false"
>
  <div class="form-group">
    <label style="margin-top: 10px">Grace Period: </label>
    <input
      type="text"
      name="gracePeriod"
      id="gracePeriod"
      [(ngModel)]="gracePeriod"
      placeholder="Enter Grace Period"
      class="form-control"
      min="0"
      (keypress)="keypress($event)"
    />
  </div>
  <ng-template pTemplate="footer">
    <div class="btnGroup text-center">
      <button
        class="btn btn-primary btnStyle"
        (click)="saveGracePeriod()"
        style="margin-right: 10px"
      >
        <!-- [disabled]="writeOffAmountFirst < writeOffAmount" -->
        Add Grace period
      </button>
      <button class="btn btn-danger btnStyle" (click)="closeGracePeriod()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>
<p-dialog
  header="Audit Details"
  [(visible)]="auditListModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="panel-collapse collapse in" id="searchPreCust">
    <div class="panel-body">
      <div class="row align-items-end">
        <div class="col-lg-3 col-md-3 m-b-10">
          <p-dropdown
            [(ngModel)]="searchOption"
            [options]="searchOptions"
            placeholder="Select Search Option"
            optionLabel="label"
            optionValue="value"
          ></p-dropdown>
        </div>

        <div
          class="col-lg-3 col-md-3 m-b-10"
          *ngIf="searchOption === 'username' || searchOption === 'invoicenumber'"
        >
          <input
            [(ngModel)]="searchInput"
            class="form-control"
            placeholder="Enter Search Detail"
            type="text"
          />
        </div>

        <div class="col-lg-6 col-md-6 col-sm-12 m-b-10">
          <button
            (click)="searchAudit()"
            class="btn btn-primary"
            type="button"
            style="margin-right: 10px"
          >
            <i class="fa fa-search"></i> Search
          </button>
          <button (click)="clearAuditSearch()" class="btn btn-default" type="reset">
            <i class="fa fa-refresh"></i> Clear
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="panel-collapse collapse in" id="invoiceCustomerDetails">
    <div class="panel-body table-responsive">
      <div class="row">
        <div class="col-lg-12 col-md-12">
          <table class="table">
            <thead>
              <tr>
                <th>User Name</th>
                <th>Audit Date</th>
                <th>Invoice Number</th>
                <th>Grace Days</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let auditList of auditListData
                    | paginate
                      : {
                          id: 'searchinvoiceMasterPageData',
                          itemsPerPage: auditListitemsPerPage,
                          currentPage: currentPageAuditListSlab,
                          totalItems: auditTotalRecords
                        };
                  index as i
                "
              >
                <td>{{ auditList.userName }}</td>
                <td>{{ auditList.auditDate | date: "yyyy-MM-dd" }}</td>
                <td>{{ auditList.invoiceNumber }}</td>
                <td>{{ auditList.debitDocGraceDays }}</td>
              </tr>
            </tbody>
          </table>
          <br />
          <div class="pagination_Dropdown">
            <pagination-controls
              (pageChange)="pageChangedList($event)"
              directionLinks="true"
              id="searchinvoiceMasterPageData"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            >
            </pagination-controls>
            <div id="itemPerPageDropdown">
              <p-dropdown
                (onChange)="TotalItemPerPage($event)"
                [options]="pageLimitOptions"
                optionLabel="value"
                optionValue="value"
              ></p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" (click)="closeAuditListData()">Close</button>
  </div>
</p-dialog>

<!-- Add MPESA Payment Options Dialog -->
<p-dialog
  [(visible)]="displayMpesaOptionsDialog"
  [style]="{ width: '450px' }"
  header="Select MPESA Payment Method"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
>
  <br />
  <div class="p-fluid">
    <div class="p-field-radiobutton p-mb-3">
      <p-radioButton
        name="mpesaOption"
        value="Mpesa-B2C"
        [(ngModel)]="selectedMpesaOption"
        label="Pay using MPESA B2C Methoad"
      ></p-radioButton>
    </div>
    <br />
    <div class="p-field-radiobutton p-mb-3">
      <p-radioButton
        name="mpesaOption"
        value="Mpesa-Express"
        [(ngModel)]="selectedMpesaOption"
        label="Pay using MPESA Express"
      ></p-radioButton>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <button class="btn btn-default" type="submit" (click)="closeMpesaOptionsDialog()">
      Cancel
    </button>
    <button
      class="btn btn-success"
      type="submit"
      (click)="handleMpesaPaymentOption(selectedMpesaOption)"
      [disabled]="!selectedMpesaOption"
    >
      Submit
    </button>
  </ng-template>
</p-dialog>
