<!-- Modal -->
<div
  class="modal fade"
  id="{{ dialogId }}"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Invoice Payment Details
        </h4>
      </div>
      <div class="modal-body">
        <div class="panel-body table-responsive">
          <div class="row">
            <div
              class="col-lg-12 col-md-12 scrollbarInvoiceAmount"
              *ngIf="InvoicePaymentList.length !== 0"
            >
              <table class="table">
                <thead>
                  <tr>
                    <th>Reference Number</th>
                    <th>Payment Mode</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Amount</th>
                    <th>Adjusted Amount</th>
                    <th>Payment Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let invoice of InvoicePaymentList
                      index as i
                    "
                  >
                    <td>
                      <div>
                        {{ invoice.referenceNumber }}
                      </div>
                    </td>
                    <td>
                      <div>
                        {{ invoice.paymode }}
                      </div>
                    </td>
                    <td>
                      <div *ngIf="invoice.type">
                        {{ invoice.type }}
                      </div>
                      <div *ngIf="!invoice.type">-</div>
                    </td>
                    <td>
                      <div>
                        {{ invoice.status }}
                      </div>
                    </td>
                    <td>
                      <div>
                        {{ invoice.amount | number: "1.2-2" }}
                      </div>
                    </td>
                    <td>
                      <div>
                        {{ invoice.adjustedAmount | number: "1.2-2" }}
                      </div>
                    </td>
                    <td>
                      <div>
                        {{ invoice.paymentdate | date: "yyyy-MM-dd" }}
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="InvoicePaymentList.length !== 0">
                    <td></td>
                    <td colspan="4" style="text-align: right">
                      <b>Total Adjusted Amount :</b>
                    </td>
                    <td colspan="2" style="padding-left: 7px">
                      {{ totaladjustedAmount | number: "1.2-2" }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <br />
              <!-- <div class="pagination_Dropdown">
                <pagination-controls
                  id="searchinvoiceMasterPageData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedinvoiceMasterList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPageInvoice($event)"
                  ></p-dropdown>
                </div>
              </div> -->
            </div>
            <div class="col-lg-12 col-md-12" *ngIf="InvoicePaymentList.length === 0">
              Details are not available
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
