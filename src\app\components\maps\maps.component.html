<!DOCTYPE html>
<body>
  <!-- <div class="container" *ngIf="!viewAccess">
    <div class="row"></div>
  </div> -->
  <div class="container">
    <agm-map [latitude]="lat" [longitude]="lng" [zoom]="zoom">
      <agm-marker
        *ngFor="let marker of devicesLocations"
        [latitude]="marker.lat"
        [longitude]="marker.lng"
        [iconUrl]="marker.icon"
      >
        <agm-info-window #infoWindow>
          {{ marker.label }}
        </agm-info-window>
      </agm-marker>
    </agm-map>
    <div>
      <Legend>Map Legend</Legend>
      <img src="assets/img/All_Icons/11_Network_Management/Map/01_ONU_Y2.png" alt="ONU" /> - ONU
      <img src="assets/img/All_Icons/11_Network_Management/Map/02_OLT_Y2.png" alt="OLT" /> - OLT
      <img src="assets/img/All_Icons/11_Network_Management/Map/03_Splitter_Y2.png" alt="Splitter" />- Splitter
      <img src="assets/img/All_Icons/11_Network_Management/Map/04_Fiber_Y2.png" alt="Master DB/DB" /> -Master DB/DB
      <img src="assets/img/All_Icons/11_Network_Management/Map/04_Fiber_Y2.png" alt="Router" /> -Router
      <img src="assets/img/All_Icons/11_Network_Management/Map/04_Fiber_Y2.png" alt="Switch" /> -Switch
    </div>
  </div>
</body>
