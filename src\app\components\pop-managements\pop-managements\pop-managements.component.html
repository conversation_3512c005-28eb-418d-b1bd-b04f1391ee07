<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Pop Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#serviceAreaSearch"
            aria-expanded="false"
            aria-controls="serviceAreaSearch"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="serviceAreaSearch" class="panel-collapse collapse in">
        <div class="panel-body" id="">
          <div class="row" *ngIf="showSearchBar">
            <div class="col-lg-3 col-md-3">
              <input
                type="text"
                [(ngModel)]="searchName"
                class="form-control"
                placeholder="Enter Pop Name"
                (keydown.enter)="searchserviceArea()"
              />
              <!-- <select class="form-control" name="searchName" id="searchName" style="width: 100%" [(ngModel)]="searchName">
                              <option value="">
                                  Select Name
                              </option>
                              <option *ngFor="let item of serviceAreaListDataselector" value="{{item.name}}">
                                  {{item.name}}
                              </option>
                          </select> -->
            </div>

            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchserviceArea()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchserviceArea()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
        <div class="panel-body no-padding panel-udata">
          <div class="pcol col-md-6" *ngIf="createAccess">
            <div class="dbox">
              <a (click)="createPop()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Pop</h5>
                <!-- <p>Create Partner </p> -->
              </a>
            </div>
          </div>
          <div class="pcol col-md-6">
            <div class="dbox">
              <a (click)="this.clearPop()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search Pop</h5>
                <!-- <p>Search Partner </p> -->
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12" *ngIf="listView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Pop Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#serviceAreaDeatils"
            aria-expanded="false"
            aria-controls="serviceAreaDeatils"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="serviceAreaDeatils" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>

                    <th>Lat/Long</th>
                    <th>Status</th>
                    <th *ngIf="deleteAccess || editAccess || openInventoryAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of serviceAreaListData
                        | paginate
                          : {
                              id: 'serviceAreaListpageData',
                              itemsPerPage: serviceAreaListdataitemsPerPage,
                              currentPage: currentPageserviceAreaListdata,
                              totalItems: serviceAreaListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td
                      class="curson_pointer"
                      (click)="getPopDetails(data.id)"
                      style="color: #f7b206"
                    >
                      {{ data.name }}
                    </td>

                    <td>{{ data.latitude }}/{{ data.longitude }}</td>
                    <td *ngIf="data.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="data.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td class="btnAction" *ngIf="deleteAccess || editAccess || openInventoryAccess">
                      <a
                        *ngIf="editAccess"
                        id="editbutton"
                        href="javascript:void(0)"
                        (click)="editPop(data.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="deleteAccess"
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmPop(data)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                      <a
                        *ngIf="openInventoryAccess"
                        class="curson_pointer"
                        (click)="openMyInventory(data)"
                      >
                        <img
                          src="../../../assets/img/All_Icons/17_Inventory_Management/08_Assign-Inventories_Y.png"
                          style="width: 32px"
                        />
                      </a>
                      <!-- <a
                          id="delete-button"
                          href="javascript:void(0)"
                          *ngIf="
                            loginService.hasOperationPermission(
                              AclClassConstants.ACL_CLASS_POP_MANAGEMENT,
                              AclConstants.OPERATION_POP_MANAGEMENT_DELETE,
                              AclConstants.OPERATION_POP_MANAGEMENT_ALL
                            )
                          "
                          (click)="openMyInventory(data)"
                        >
                        <img class="icon" src="assets/img/E_Status_Y.png" />
                        </a> -->
                      <!-- <a
            class="pcol"
            [ngClass]="isCustomerDetailSubMenu ? 'col-md-3' : 'col-md-6'"
            *ngIf="isCustomerDetailSubMenu"
          >
            <div
              class="dbox"
              [ngClass]="{
                activeSubMenu: customerrMyInventoryView
              }"
            >
              <a class="curson_pointer" (click)="openMyInventory(customerLedgerDetailData.id)">
                <img class="icon" src="assets/img/E_Status_Y.png" />
              </a>
            </div>
          </a> -->
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="pagination_Dropdown">
                <pagination-controls
                  id="serviceAreaListpageData"
                  maxSize="5"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedserviceAreaList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                   [(ngModel)]="serviceAreaListdataitemsPerPage"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <form [formGroup]="serviceAreaGroupForm">
    <div class="col-md-12" *ngIf="createView">
      <div class="panel">
        <div class="panel-heading">
          <!-- <h3 class="panel-title" *ngIf="!isserviceAreaEdit">Create Pop</h3>
        <h3 class="panel-title" *ngIf="isserviceAreaEdit">Update Pop</h3> -->
          <h3 class="panel-title">{{ editMode ? "Update" : "Create" }} Pop</h3>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#serviceAreaCreate"
              aria-expanded="false"
              aria-controls="serviceAreaCreate"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>

        <div id="serviceAreaCreate" class="panel-collapse collapse in">
          <div class="panel-body">
            <form [formGroup]="serviceAreaGroupForm">
              <fieldset style="margin-top: 0px">
                <legend>Basic Details</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group">
                        <label>Pop Name *</label>
                        <input
                          id="name"
                          type="text"
                          class="form-control"
                          placeholder="Enter Name"
                          formControlName="name"
                          [ngClass]="{
                            'is-invalid': submitted && serviceAreaGroupForm.controls.name.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="submitted && serviceAreaGroupForm.controls.name.errors"
                        >
                          <div
                            class="error text-danger"
                            *ngIf="submitted && serviceAreaGroupForm.controls.name.errors.required"
                          >
                            Name is required.
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <label>Pop Code</label>
                      <input
                        id="popCode"
                        type="text"
                        class="form-control"
                        placeholder="Enter Pop Code"
                        formControlName="popCode"
                        [ngClass]="{
                          'is-invalid': submitted && serviceAreaGroupForm.controls.popCode.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && serviceAreaGroupForm.controls.popCode.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && serviceAreaGroupForm.controls.popCode.errors.required"
                        >
                          Pop Code is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <label>Status *</label>
                      <div>
                        <p-dropdown
                          [options]="statusOptions"
                          optionValue="label"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select a Status"
                          formControlName="status"
                          [ngClass]="{
                            'is-invalid': submitted && serviceAreaGroupForm.controls.status.errors
                          }"
                        ></p-dropdown>
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && serviceAreaGroupForm.controls.status.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && serviceAreaGroupForm.controls.status.errors.required"
                        >
                          Status is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                </div>
              </fieldset>
              <fieldset class="m-0">
                <legend>Location Details</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                      <label>Latitude *</label>
                      <input
                        id="latitude"
                        type="text"
                        class="form-control"
                        placeholder="Enter latitude"
                        formControlName="latitude"
                        [ngClass]="{
                          'is-invalid': submitted && serviceAreaGroupForm.controls.latitude.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && serviceAreaGroupForm.controls.latitude.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && serviceAreaGroupForm.controls.latitude.errors.required
                          "
                        >
                          Latitude is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                      <label>Longitude *</label>
                      <input
                        id="longitude"
                        type="text"
                        class="form-control"
                        placeholder="Enter longitude"
                        formControlName="longitude"
                        [ngClass]="{
                          'is-invalid': submitted && serviceAreaGroupForm.controls.longitude.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && serviceAreaGroupForm.controls.longitude.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && serviceAreaGroupForm.controls.longitude.errors.required
                          "
                        >
                          Longitude is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                      <div style="margin-bottom: 1rem">
                        <span
                          class="HoverEffect"
                          (click)="mylocation()"
                          title="Get Current Location"
                          style="border-bottom: 1px solid #f7b206"
                        >
                          <img
                            class="LocationIcon LocationIconMargin"
                            src="assets/img/B_Find-My-current-location_Y.png"
                          />
                        </span>
                        <span
                          class="HoverEffect"
                          title="Search Location"
                          data-target="#searchLocationModal"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          style="margin-left: 8px; border-bottom: 1px solid #f7b206"
                          (click)="openSearchModel()"
                        >
                          <img
                            class="LocationIcon LocationIconMargin"
                            src="assets/img/C_Search-location_Y.png"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </fieldset>
              <fieldset class="m-0">
                <legend>Address Details</legend>
                <div class="boxWhite">
                  <!-- <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12"> -->
                  <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                      <label>Service Area*</label>
                      <div>
                        <!-- optionDisabled="flag" -->
                        <p-multiSelect
                          id="roles"
                          [options]="serviceAreaList"
                          formControlName="serviceAreaIdsList"
                          defaultLabel="Select Area"
                          optionLabel="name"
                          optionValue="id"
                          resetFilterOnHide="true"
                          placeholder="Select a Service Area"
                          [ngClass]="{
                            'is-invalid':
                              submitted && serviceAreaGroupForm.controls.serviceAreaIdsList.errors
                          }"
                        >
                      <ng-template let-option pTemplate="item">
                        <span>
                        {{ option.name }}
                        <span  *ngIf="option.status === 'UnderDevelopment'" class="badge badge-info underDevelopBadge">
                            UnderDevelopment
                        </span>
                        </span>
                      </ng-template>
                    </p-multiSelect>
                      
                      </div>
                        
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && serviceAreaGroupForm.controls.serviceAreaIdsList.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            serviceAreaGroupForm.controls.serviceAreaIdsList.errors.required
                          "
                        >
                          Service Area is required.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </fieldset>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="!isserviceAreaEdit"
                  id="submit"
                  (click)="addEditserviceArea('')"
                  [disabled]="!serviceAreaGroupForm.valid"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Pop
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isserviceAreaEdit"
                  id="submit"
                  (click)="addEditserviceArea(viewserviceAreaListData.id)"
                  [disabled]="!serviceAreaGroupForm.valid"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Pop
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div *ngIf="ifsearchLocationModal">
  <div class="modal fade" id="searchLocationModal" role="dialog">
    <div class="modal-dialog searchLocationModalWidth">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Search Location</h3>
        </div>
        <div class="modal-body">
          <form name="searchLocationForm" [formGroup]="searchLocationForm">
            <div class="form-group">
              <label for="searchLocationname">Search Location Name:</label>
              <div class="row">
                <div class="col-lg-7 col-md-6">
                  <input
                    type="searchLocationname"
                    class="form-control"
                    id="searchLocationname"
                    placeholder="Enter Location Name"
                    formControlName="searchLocationname"
                  />
                </div>
                <div class="col-lg-5 col-md-6" style="padding: 0 10px !important">
                  <button
                    type="submit"
                    class="btn btn-primary btn-sm"
                    id="closeModal"
                    (click)="searchLocation()"
                    [disabled]="!searchLocationForm.valid"
                  >
                    <i class="fa fa-search"></i>
                    Search
                  </button>
                  <button
                    id="btn"
                    type="button"
                    class="btn btn-default btn-sm"
                    (click)="clearLocationForm()"
                    style="margin-left: 8px"
                  >
                    <i class="fa fa-refresh"></i>
                    Clear
                  </button>
                </div>
              </div>
            </div>
          </form>
          <div class="row">
            <div class="col-lg-12 col-md-12" style="margin-top: 3rem">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 35%">Name</th>
                    <th style="width: 65%">Address</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of searchLocationData
                        | paginate
                          : {
                              id: 'searchpageData',
                              itemsPerPage: searchLocationItemPerPage,
                              currentPage: currentPagesearchLocationList,
                              totalItems: searchLocationtotalRecords
                            };
                      index as i
                    "
                  >
                    <td
                      class="HoverEffect"
                      (click)="filedLocation(data.placeId)"
                      data-toggle="tooltip"
                      data-placement="bottom"
                      title="Set value Latitude & Longitude"
                      style="width: 35%"
                    >
                      {{ data.name }}
                    </td>
                    <td style="width: 65%">{{ data.address }}</td>
                  </tr>
                </tbody>
              </table>
              <pagination-controls
                id="searchpageData"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedSearchLocationList($event)"
              ></pagination-controls>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="addUpdateBtn">
            <button
              type="button"
              class="btn btn-danger btn-sm"
              #closebutton
              data-dismiss="modal"
              (click)="clearsearchLocationData()"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div *ngIf="this.customerrMyInventoryView" class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="getSAData()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Service Area Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ customerLedgerDetailData.title }}
            Inventory List
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="inventoryListPreCust1"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#inventoryListPreCust1"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
        <button
          (click)="assignInventoryToCustomer(id)"
          class="btn btn-primary statusbtn"
          data-backdrop="static"
          data-keyboard="false"
          data-target="#assignInventory"
          data-toggle="modal"
          style="
            background-color: #f7b206 !important;
            font-size: 16px;
            padding: 3px 12px;
            margin-top: 10px;
          "
          title="Assign inventory"
          type="submit"
        >
          Assign Inventory
        </button>
      </div>

      <div class="panel-collapse collapse in" id="inventoryListPreCust1">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th width="15%">Inward Number</th>
                    <th width="15%">Product Name</th>
                    <th width="10%">Assigned Qty.</th>
                    <th style="text-align: center" width="15%">Status</th>
                    <th width="10%">Next Approver</th>
                    <th width="10%">Assigned Date</th>
                    <th width="10%">Expiry Date</th>
                    <th width="15%">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let assignedInventory of this.assignedInventoryList
                        | paginate
                          : {
                              id: 'assignedInventoryListData',
                              itemsPerPage: customerInventoryListItemsPerPage,
                              currentPage: customerInventoryListDataCurrentPage,
                              totalItems: customerInventoryListDataTotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ assignedInventory.inwardNumber }}</td>
                    <td>{{ assignedInventory.productName }}</td>
                    <td>
                      {{ assignedInventory.qty }}
                      {{ assignedInventory.productId.unit }}
                    </td>
                    <td style="text-align: center">
                      <div *ngIf="assignedInventory.status == 'ACTIVE'">
                        <span class="badge badge-success">Active</span>
                      </div>
                      <div *ngIf="assignedInventory.status == 'REJECTED'">
                        <span class="badge badge-danger">Rejected</span>
                      </div>
                      <div *ngIf="assignedInventory.status == 'PENDING'">
                        <span class="badge badge-info"> Pending For Approve </span>
                      </div>
                    </td>
                    <td>
                      {{ assignedInventory.assigneeName }}
                    </td>
                    <td>
                      {{ assignedInventory.assignedDateTime | date: "dd-MM-yyyy hh:mm:ss" }}
                    </td>
                    <td>
                      {{ assignedInventory.expiryDateTime | date: "dd-MM-yyyy hh:mm:ss" }}
                    </td>
                    <td>
                      <a
                        title="Edit"
                        style="margin-right: 5px"
                        (click)="editCustomerInventory(assignedInventory.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <button
                        type="button"
                        class="btn btn-success gridbtn"
                        style="border: none; background: transparent; padding: 0; margin-right: 5px"
                        [disabled]="assignedInventory.nextApproverId != staffUser.id"
                        title="Approve"
                        (click)="approveInventory(assignedInventory.id)"
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>
                      <button
                        type="button"
                        class="btn btn-danger gridbtn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        [disabled]="assignedInventory.nextApproverId != staffUser.id"
                        title="Reject"
                        (click)="rejectInventory(assignedInventory.id)"
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>
                      <a
                        data-backdrop="static"
                        data-keyboard="false"
                        data-target="#inventoryStatusViewId"
                        data-toggle="modal"
                        id="delete-button"
                        href="javascript:void(0)"
                        title="Approval Progress"
                        (click)="checkStatus(assignedInventory.id, assignedInventory.status)"
                      >
                        <img
                          width="32"
                          height="32"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="display: flex">
                <pagination-controls
                  (pageChange)="pageChangedEventCustomerAssignInventory($event)"
                  [directionLinks]="true"
                  id="assignedInventoryListData"
                  [maxSize]="10"
                  nextLabel=""
                  previousLabel=""
                >
                </pagination-controls>
                <div #itemPerPageDropDownInventory id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="totalItemsEventCustomerAssignInventory($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> -->

<div class="row" *ngIf="detailView">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to Tax Details"
            (click)="popList()"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">{{ viewPopDetails.name }} Pop</h3>
        </div>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#outwardDetail"
            aria-expanded="false"
            aria-controls="taxalldea"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="outwardDetail" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Basic Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Name :</label>
                  <span>{{ viewPopDetails.name }}</span>
                </div>
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Latitude :</label>
                  <span>{{ viewPopDetails.latitude }}</span>
                </div>
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Longitude :</label>
                  <span>{{ viewPopDetails.longitude }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md- dataGroup">
                  <label class="datalbl">Status :</label>
                  <span>{{ viewPopDetails.status }}</span>
                </div>
                <div class="col-lg-4 col-md- dataGroup">
                  <label class="datalbl">Pop Code :</label>
                  <span>{{ viewPopDetails.popCode }}</span>
                </div>
                <div class="col-lg-4 col-md- dataGroup">
                  <label class="datalbl">Serivce Area :</label>
                  <span
                    class="HoverEffect"
                    (click)="serviceareListShow()"
                    data-toggle="tooltip"
                    data-placement="bottom"
                    title="Go to Service Area List"
                  >
                    Click here
                  </span>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Service Area List"
  [(visible)]="MACAssignModalOutward"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div
      *ngIf="IfPersonalNetworkDataShow"
      class="panel-body table-responsive"
      id="networkDeviceTabel"
    ></div>
    <div *ngIf="ifServiceAreaListShow">
      <div class="panel-body table-responsive" id="networkDeviceTabel">
        <table class="table">
          <tbody>
            <tr>
              <td><label class="networkLabel">Service Area :</label></td>
              <td>
                <span
                  style="word-break: break-all"
                  *ngFor="let serviceName of viewPopDetails.serviceAreaNameList"
                >
                  <span>
                    {{ serviceName }},
                    <br />
                  </span>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <!-- <div class="addUpdateBtn"> -->
    <button type="button" class="btn btn-primary" (click)="closeMACAssignModalOutward()">
      Close
    </button>
  </div>
</p-dialog>

<div *ngIf="customerrMyInventoryView" class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="getSAData()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">{{ inventoryPopData.name }} Inventory List</h3>
          <!-- <h3 class="panel-title">
            {{ customerLedgerDetailData.title }}
            {{ customerLedgerDetailData.custname }} 
          </h3> -->
        </div>
        <div class="right">
          <button
            aria-controls="inventoryListPreCust1"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#inventoryListPreCust1"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="inventoryListPreCust1">
        <div class="panel-body table-responsive">
          <app-common-inventory-management
            [data]="inventoryPopData"
            [type]="inventoryType"
            [openFrom]="'pop'"
          ></app-common-inventory-management>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="this.assignInventoryWithSerial">
  <div class="modal fade" id="assignInventoryWithSerial" role="dialog">
    <div class="modal-dialog nearSearchModalLocation">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Replace Inventory</h3>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <form [formGroup]="this.inventoryAssignForm">
                <label>Product*</label>
                <p-dropdown
                  [options]="this.replaceProducts"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  filter="true"
                  filterBy="name"
                  placeholder="Select Product"
                  (onChange)="getUnit($event)"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && this.inventoryAssignForm.controls.productId.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="submitted && this.inventoryAssignForm.controls.productId.errors.required"
                  >
                    Product is required.
                  </div>
                </div>
                <br />
                <br />
                <label>Select Inward</label>
                <p-dropdown
                  [options]="this.inwardList"
                  formControlName="inwardId"
                  optionLabel="inwardNumber"
                  optionValue="id"
                  filter="true"
                  filterBy="inwardNumber"
                  placeholder="Select Inward"
                  (onChange)="this.getAvailableQty($event.value)"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && this.inventoryAssignForm.controls.inwardId.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="submitted && this.inventoryAssignForm.controls.inwardId.errors.required"
                  >
                    Inward is required.
                  </div>
                </div>
                <br />
                <p-table
                  *ngIf="this.macList.length > 0"
                  #dt
                  [value]="this.macList"
                  [rows]="5"
                  [paginator]="true"
                  [globalFilterFields]="['macAddress']"
                  responsiveLayout="scroll"
                  [(selection)]="selectedMACAddress"
                  [rowHover]="true"
                  dataKey="id"
                  currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                  [showCurrentPageReport]="true"
                >
                  <ng-template pTemplate="caption">
                    <div class="flex align-items-center justify-content-between">
                      <h5 class="m-0">MAC Address</h5>
                      <span class="p-input-icon-left">
                        <input
                          class="form-control"
                          pInputText
                          type="text"
                          (input)="dt.filterGlobal($event.target.value, 'contains')"
                          placeholder="Search..."
                        />
                      </span>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 10%">Select</th>
                      <th *ngIf="this.productHasMac">MAC Address</th>
                      <th *ngIf="this.productHasSerial">Serial Number</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-product>
                    <tr>
                      <td style="width: 10%">
                        <p-tableCheckbox
                          [value]="product"
                          *ngIf="product.customerId == null"
                        ></p-tableCheckbox>
                      </td>
                      <td *ngIf="this.productHasMac">
                        {{ product.macAddress }}
                      </td>
                      <td *ngIf="this.productHasSerial">
                        {{ product.serialNumber }}
                      </td>
                    </tr>
                  </ng-template>
                  <!-- <ng-template pTemplate="summary right">
                  <button
                    type="button"
                    class="btn btn-danger btn-sm"
                    data-dismiss="modal"
                  >
                    Close
                  </button>
                  <div class="flex align-items-center justify-content-between">
                    In total there are {{ products ? products.length : 0 }} products.
                  </div>
                </ng-template> -->
                </p-table>
                <br />

                <label>Replace Date*</label>
                <div>
                  <p-calendar
                    [style]="{ width: '50%' }"
                    formControlName="assignedDateTime"
                    [showTime]="true"
                    [showSeconds]="true"
                    inputId="time"
                    [numberOfMonths]="3"
                  ></p-calendar>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="submitted && this.inventoryAssignForm.controls.assignedDateTime.errors"
                    `
                  >
                    <div
                      class="error text-danger"
                      *ngIf="
                        submitted &&
                        this.inventoryAssignForm.controls.assignedDateTime.errors.required
                      "
                    >
                      Replace Date is required.
                    </div>
                  </div>
                </div>
                <br />
                <br />
                <div class="addUpdateBtn">
                  <button
                    type="submit"
                    class="btn btn-primary"
                    id="submit"
                    data-dismiss="modal"
                    (click)="this.replaceInventorySubmit()"
                  >
                    <i class="fa fa-check-circle"></i>
                    Submit
                  </button>
                  <br />
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="modal-footer" style="display: flex; justify-content: flex-end">
          <div class="addUpdateBtn">
            <button
              class="btn btn-danger btn-sm"
              #closebutton
              data-dismiss="modal"
              (click)="this.close()"
            >
              Close
            </button>
            <br />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <div class="row" *ngIf="inventoryStatusView"> -->
<div class="modal fade" id="inventoryStatusViewId" role="dialog">
  <div class="modal-dialog nearSearchModalLocation">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Inventory Progress</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <div class="progressbarWrap">
              <div
                class="circleWrap"
                [ngClass]="{
                  complete: data.status == 'Approved',
                  progressdv: data.status == 'inprogress'
                }"
                *ngFor="let data of inventoryStatusDetails; last as isLast"
              >
                <div class="circledv completedWorkFlowClass">
                  <i class="fa fa-check" *ngIf="data.status == 'Approved'"></i>
                </div>
                <p>{{ data.teamName }}</p>
                <div class="lines" *ngIf="!isLast">
                  <div class="line"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12">
            <div style="margin: 20px">
              <h3>Workflow Audit</h3>
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Partner Name</th>
                          <th>Action</th>
                          <th>Staff name</th>
                          <th>Remark</th>
                          <th>Action Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let data of workflowAuditDataI
                              | paginate
                                : {
                                    id: 'searchMasterPageDataI',
                                    itemsPerPage: MasteritemsPerPageI,
                                    currentPage: currentPageMasterSlabI,
                                    totalItems: MastertotalRecordsI
                                  };
                            index as i
                          "
                        >
                          <td>
                            <div *ngIf="data.entityName">
                              {{ data.entityName }}
                            </div>
                            <div *ngIf="data.planName">
                              {{ data.planName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.action }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionByName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.remark }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        id="searchMasterPageDataI"
                        maxSize="5"
                        directionLinks="true"
                        previousLabel=""
                        nextLabel=""
                        (pageChange)="pageChangedMasterListI($event)"
                      >
                      </pagination-controls>
                      <div id="itemPerPageDropdown">
                        <!-- <p-dropdown
                          [options]="pageLimitOptions"
                          optionLabel="value"
                          optionValue="value"
                          (onChange)="TotalItemPerPageWorkFlow($event)"
                        ></p-dropdown> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn">
          <button class="btn btn-danger btn-sm" #closebutton data-dismiss="modal">Close</button>
          <br />
        </div>
      </div>
    </div>
  </div>
</div>
<!-- </div> -->

<div class="modal fade" id="inventoryStatusViewForReplace" role="dialog">
  <div class="modal-dialog nearSearchModalLocation">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Replace Inventory Progress</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <div class="progressbarWrap">
              <div
                class="circleWrap"
                [ngClass]="{
                  complete: data.status == 'Approved',
                  progressdv: data.status == 'inprogress'
                }"
                *ngFor="let data of inventoryStatusDetailsForReplace; last as isLast"
              >
                <div class="circledv completedWorkFlowClass">
                  <i class="fa fa-check" *ngIf="data.status == 'Approved'"></i>
                </div>
                <p>{{ data.teamName }}</p>
                <div class="lines" *ngIf="!isLast">
                  <div class="line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn">
          <button class="btn btn-danger btn-sm" #closebutton data-dismiss="modal">Close</button>
          <br />
        </div>
      </div>
    </div>
  </div>
</div>

<div
  class="modal fade"
  id="assignCustomerInventoryModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Staff</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [value]="approveInventoryData"
                [(selection)]="selectStaff"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <!-- <input type="file" formControlName="fileName" name="fileName"> -->
      </div>
      <div class="modal-footer">
        <button
          *ngIf="approved && !selectStaff"
          type="submit"
          class="btn btn-primary"
          id="submit"
          disabled
          (click)="assignToStaff(true)"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button
          *ngIf="approved && selectStaff"
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="assignToStaff(true)"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="rejectCustomerInventoryModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Staff</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [value]="rejectInventoryData"
                [(selection)]="selectStaffReject"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <!-- <input type="file" formControlName="fileName" name="fileName"> -->
      </div>
      <div class="modal-footer">
        <button
          *ngIf="reject && !selectStaffReject"
          type="submit"
          class="btn btn-primary"
          id="submit"
          disabled
          (click)="assignToStaff(false)"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          *ngIf="reject && selectStaffReject"
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="assignToStaff(false)"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
