// import { Injectable } from "@angular/core";
// import { Router } from "@angular/router";
// import * as Keycloak from "keycloak-js";
// // import Keycloak from "keycloak-js";

// export interface KeycloakTokenParsed {
//   preferred_username?: string;
//   email?: string;
//   [key: string]: any;
// }

// @Injectable({
//   providedIn: "root"
// })
// export class KeycloakService {
//   private keycloakInstance!: Keycloak.KeycloakInstance;

//   constructor(private router: Router) {}

//   /**
//    * Initialize Keycloak
//    */
//   init(): Promise<boolean> {
//     return new Promise(async (resolve, reject) => {
//       try {
//         // Load Keycloak config from assets
//         const response = await fetch("/assets/keycloak.json");
//         const config = await response.json();
//         console.log("Config :::::", config);
//         // this.keycloakInstance = new (Keycloak as any).default(config);
//         this.keycloakInstance = Keycloak(config);
//         // const authenticated = await this.keycloakInstance.init({
//         //   onLoad: "login-required",
//         //   checkLoginIframe: false // disables periodic iframe check (optional)
//         // });
//         // console.log("In It Authentict", authenticated);
//         // resolve(authenticated);
//         const authenticated = await this.keycloakInstance.init({
//           onLoad: "login-required",
//           checkLoginIframe: false
//           //   promiseType: "native" // ✅ avoid legacy promises
//         });

//         if (authenticated) {
//           // ✅ redirect user to dashbord
//           this.router.navigate(["/home/<USER>"]);
//         }

//         resolve(authenticated);
//       } catch (err) {
//         console.error("Keycloak init failed", err);
//         reject(false);
//       }
//     });
//   }

//   /**
//    * Get access token
//    */
//   getToken(): string | undefined {
//     return this.keycloakInstance?.token;
//   }

//   /**
//    * Logout
//    */
//   logout(): void {
//     this.keycloakInstance?.logout();
//   }

//   /**
//    * Get logged-in username
//    */
//   getUsername(): string | undefined {
//     return (this.keycloakInstance?.tokenParsed as KeycloakTokenParsed)?.preferred_username;
//   }

//   /**
//    * Refresh token
//    */
//   updateToken(minValidity: number = 30): Promise<boolean> {
//     return this.keycloakInstance.updateToken(minValidity);
//   }

//   /**
//    * Check if authenticated
//    */
//   isLoggedIn(): boolean {
//     return !!this.keycloakInstance?.token;
//   }
// }

// import { Injectable } from "@angular/core";
// import { Router } from "@angular/router";
// import { KeycloakInstance } from "keycloak-js";
// import * as Keycloak from "keycloak-js";

// @Injectable({
//   providedIn: "root"
// })
// export class KeycloakService {
//   private keycloakAuth!: KeycloakInstance;

//   constructor(private router: Router) {}

//   async init(): Promise<boolean> {
//     try {
//       const response = await fetch("/assets/keycloak.json");
//       const config = await response.json();

//       this.keycloakAuth = Keycloak(config);

//       const authenticated = await this.keycloakAuth.init({
//         onLoad: "login-required",
//         checkLoginIframe: false,
//         redirectUri: window.location.origin + "/#/home/<USER>" // ✅ clean path-based redirect
//       });

//       if (authenticated) {
//         console.log("✅ Keycloak Authenticated");
//         // Redirect to dashbord after login
//         // this.router.navigate(["/home/<USER>"]);
//         // ✅ Always clean URL if Keycloak added state/code params
//         const currentUrl = window.location.href;
//         const cleanUrl = window.location.origin + "/#/home/<USER>";

//         if (
//           currentUrl.includes("state=") ||
//           currentUrl.includes("code=") ||
//           currentUrl.includes("session_state=")
//         ) {
//           window.history.replaceState({}, document.title, cleanUrl);
//         }
//       } else {
//         this.keycloakAuth?.login();
//       }

//       return authenticated;
//     } catch (error) {
//       console.error("❌ Keycloak init failed", error);
//       return false;
//     }
//   }

//   getToken(): string | undefined {
//     return this.keycloakAuth?.token;
//   }

//   logout(): void {
//     this.keycloakAuth.logout();
//   }

//   getUsername(): string | undefined {
//     return (this.keycloakAuth?.tokenParsed as any)?.preferred_username;
//   }

//   async updateToken(minValidity: number = 30): Promise<string | undefined> {
//     try {
//       await this.keycloakAuth.updateToken(minValidity);
//       return this.keycloakAuth.token;
//     } catch (err) {
//       console.error("Failed to refresh token", err);
//       return undefined;
//     }
//   }

//   isLoggedIn(): boolean {
//     return !!this.keycloakAuth?.authenticated;
//   }
// }

// import { Injectable } from "@angular/core";
// import { Router } from "@angular/router";
// import { KeycloakInstance } from "keycloak-js";
// import * as Keycloak from "keycloak-js";

// @Injectable({
//   providedIn: "root"
// })
// export class KeycloakService {
//   private keycloakAuth!: KeycloakInstance;
//   private static initialized = false; // ✅ Static flag

//   constructor(private router: Router) {}

//   async init(): Promise<boolean> {
//     // ✅ If already initialized, just return authentication status
//     if (KeycloakService.initialized && this.keycloakAuth) {
//       console.log("🔄 Keycloak already initialized");
//       return this.keycloakAuth.authenticated || false;
//     }

//     try {
//       console.log("🚀 Initializing Keycloak");

//       const response = await fetch("/assets/keycloak.json");
//       const config = await response.json();

//       this.keycloakAuth = Keycloak(config);

//       const authenticated = await this.keycloakAuth.init({
//         onLoad: "login-required",
//         checkLoginIframe: false,
//         redirectUri: window.location.origin + "/#/home/<USER>"
//       });

//       KeycloakService.initialized = true;
//       console.log("✅ Keycloak initialized, authenticated:", authenticated);

//       // ✅ ONLY clean URL if we're authenticated AND have parameters
//       if (authenticated) {
//         const url = window.location.href;
//         if (url.includes("&") || url.includes("state=")) {
//           console.log("🧹 Cleaning callback URL");
//           // ✅ Use location.replace to avoid history entry
//           window.location.replace(window.location.origin + "/#/home/<USER>");
//           return authenticated; // ✅ Return early to prevent further processing
//         }
//       }

//       return authenticated;
//     } catch (error) {
//       console.error("❌ Keycloak init failed", error);
//       KeycloakService.initialized = true; // ✅ Still mark as initialized
//       return false;
//     }
//   }

//   getToken(): string | undefined {
//     return this.keycloakAuth?.token;
//   }

//   logout(): void {
//     KeycloakService.initialized = false; // ✅ Reset static flag
//     this.keycloakAuth?.logout();
//   }

//   getUsername(): string | undefined {
//     return (this.keycloakAuth?.tokenParsed as any)?.preferred_username;
//   }

//   async updateToken(minValidity: number = 30): Promise<string | undefined> {
//     try {
//       await this.keycloakAuth.updateToken(minValidity);
//       return this.keycloakAuth.token;
//     } catch (err) {
//       console.error("Failed to refresh token", err);
//       return undefined;
//     }
//   }

//   isLoggedIn(): boolean {
//     return !!this.keycloakAuth?.authenticated;
//   }
// }

// Update your existing keycloak.service.ts to be a simple wrapper
import { Injectable } from "@angular/core";
import { KeycloakBootstrapService } from "./keycloak-bootstrap.service";

@Injectable({
  providedIn: "root"
})
export class KeycloakService {
  constructor(private bootstrapService: KeycloakBootstrapService) {}

  // ✅ Simple wrapper methods
  getToken(): string | undefined {
    return this.bootstrapService.getToken();
  }

  logout(): void {
    this.bootstrapService.logout();
  }

  getUsername(): string | undefined {
    return this.bootstrapService.getUsername();
  }

  async updateToken(minValidity: number = 30): Promise<string | undefined> {
    return this.bootstrapService.updateToken(minValidity);
  }

  isLoggedIn(): boolean {
    return this.bootstrapService.isLoggedIn();
  }
}
