<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen(customerId)"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ customerLedgerDetailData?.title }}
            {{ customerLedgerDetailData?.firstname }}
            {{ customerLedgerDetailData?.lastname }} Discount List
          </h3>
        </div>
        <div class="right">
          <button
            class="btn refreshbtn"
            type="reset"
            (click)="getcustDiscountDetails(customerId, '', 'changeDiscount')"
          >
            <i class="fa fa-refresh"></i>
          </button>
          <button
            aria-controls="updateDisc"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#updateDisc"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="updateDisc">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Connection No.</th>
                    <th>Service</th>
                    <!-- <th>Invoice Type</th> -->
                    <th>Current Discount Type</th>
                    <th>Current Discount (%)</th>
                    <th>Current Discount Expiry Date</th>
                    <th>New Discount Type</th>
                    <th>New Discount (%)</th>
                    <th>New Discount Expiry Date</th>
                    <th>Remarks</th>
                    <!-- <th>Status</th> -->
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of custCustDiscountList; index as i">
                    <td>
                      <input
                        [(ngModel)]="data.connectionNo"
                        [value]="data.connectionNo"
                        class="form-control"
                        id="connectionNo"
                        name="connectionNo"
                        placeholder="Enter Connection No."
                        readonly
                      />
                    </td>
                    <td>
                      <input
                        [(ngModel)]="data.serviceName"
                        [value]="data.serviceName"
                        class="form-control"
                        id="serviceName"
                        name="serviceName"
                        placeholder="Enter Service Name"
                        readonly
                      />
                    </td>
                    <td>
                      <p-dropdown
                        [options]="chargeType"
                        filter="true"
                        filterBy="label"
                        [(ngModel)]="data.discountType"
                        optionLabel="label"
                        optionValue="label"
                        id="discountType"
                        name="discountType"
                        placeholder="Select a Discount Type"
                        disabled
                      ></p-dropdown>
                    </td>
                    <td>
                      <input
                        [(ngModel)]="data.discount"
                        [value]="data.discount"
                        class="form-control"
                        id="discount"
                        name="discount"
                        placeholder="Enter Discount"
                        readonly
                      />
                    </td>
                    <td>
                      <p-calendar
                        *ngIf="data.discountType === 'Recurring'"
                        [(ngModel)]="data.discountExpiryDate"
                        [hideOnDateTimeSelect]="true"
                        [showButtonBar]="true"
                        [showIcon]="true"
                        [style]="{ width: '100%' }"
                        dateFormat="dd/mm/yy"
                        [minDate]="dateTime"
                        id="discountExpiryDate"
                        name="discountExpiryDate"
                        placeholder="Enter Discount Expiry Date"
                        [disabled]="
                          !(data.nextTeamHierarchyMappingId === null && data.nextStaff === null)
                        "
                      ></p-calendar>
                      <div *ngIf="data.discountType === 'Recurring' && !data.discountExpiryDate">
                        Current Discount Expiry Date is required.
                      </div>
                      <div *ngIf="data.discountType !== 'Recurring'">-</div>
                    </td>
                    <td>
                      <p-dropdown
                        [options]="chargeType"
                        filter="true"
                        filterBy="label"
                        [(ngModel)]="data.newDiscountType"
                        optionLabel="label"
                        optionValue="label"
                        id="newDiscountType"
                        name="newDiscountType"
                        placeholder="Select New Discount Type"
                        [disabled]="
                          !(data.nextTeamHierarchyMappingId === null && data.nextStaff === null)
                        "
                      ></p-dropdown>
                    </td>
                    <td>
                      <input
                        required
                        #numberOne="ngModel"
                        [(ngModel)]="data.newDiscount"
                        [customMax]="maxDiscountValue"
                        [value]="data.newDiscount"
                        class="form-control"
                        id="newDiscount"
                        name="newDiscount"
                        placeholder="Enter New Discount*"
                        type="number"
                        (change)="changeValue(data.newDiscount)"
                        [disabled]="
                          !(data.nextTeamHierarchyMappingId === null && data.nextStaff === null)
                        "
                      />
                      <div *ngIf="numberOne.errors" [ngClass]="'error'">
                        <div *ngIf="numberOne.errors.customMax" class="error text-danger">
                          Maximum 99 Percentage allowed.
                        </div>
                      </div>
                    </td>
                    <td>
                      <p-calendar
                        *ngIf="data.newDiscountType === 'Recurring'"
                        [(ngModel)]="data.newDiscountExpiryDate"
                        [hideOnDateTimeSelect]="true"
                        [showButtonBar]="true"
                        [showIcon]="true"
                        [style]="{ width: '100%' }"
                        dateFormat="dd/mm/yy"
                        [minDate]="dateTime"
                        id="newDiscountExpiryDate"
                        name="newDiscountExpiryDate"
                        placeholder="Enter New Discount Expiry Date"
                        [disabled]="
                          !(data.nextTeamHierarchyMappingId === null && data.nextStaff === null)
                        "
                      ></p-calendar>
                      <div
                        *ngIf="data.newDiscountType === 'Recurring' && !data.newDiscountExpiryDate"
                      >
                        New Discount Expiry Date is required.
                      </div>
                      <div *ngIf="data.newDiscountType !== 'Recurring'">-</div>
                    </td>
                    <td>
                      <input
                        required
                        #remark="ngModel"
                        maxLength="250"
                        [(ngModel)]="data.remarks"
                        [value]="data.remarks"
                        class="form-control"
                        id="remarks"
                        name="remarks"
                        placeholder="Enter Remarks"
                        type="text"
                        [readonly]="
                          !(data.nextTeamHierarchyMappingId === null && data.nextStaff === null)
                        "
                      />
                      <div *ngIf="remark.errors?.maxLength" class="error text-danger">
                        Maximum 250 characters are allowed
                      </div>
                    </td>
                    <!-- <td>{{ data.status }}</td> -->
                    <td>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        [disabled]="
                          data.nextTeamHierarchyMappingId === null ||
                          data.nextStaff != null ||
                          (data.status == 'Active' && data.discountFlowInProcess != 'yes') ||
                          (data.status == 'active' && data.discountFlowInProcess != 'yes')
                        "
                        (click)="pickModalOpen(data)"
                      >
                        <img width="30px" src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <a
                        *ngIf="auditAccess"
                        (click)="
                          custDiscountWorkflowAuditopen(
                            'custauditWorkflowModal',
                            data.id,
                            data.planId
                          )
                        "
                        class="detailOnAnchorClick"
                        title="Audit & Change Discount Status Details"
                      >
                        <img
                          height="32"
                          src="assets/img/05_inventory-to-customer_Y.png"
                          width="32"
                        />
                      </a>
                      &nbsp;
                      <button
                        (click)="discountApporeved(data)"
                        [disabled]="data.nextStaff === null || data.nextStaff !== userId ||
                          (data.status.toLowerCase() == 'active' && data.discountFlowInProcess.toLowerCase() != 'yes')"
                        class="approve-btn"
                        title="Approve"
                        type="button"
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>
                      <button
                        (click)="discountRejected(data)"
                        [disabled]="data.nextStaff === null || data.nextStaff !== userId || 
                        (data.status.toLowerCase() == 'active' && data.discountFlowInProcess.toLowerCase() != 'yes')"
                        class="approve-btn"
                        title="Reject"
                        type="button"
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>
                      <button
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        [disabled]="
                          data.nextStaff !== userId || data.nextTeamHierarchyMappingId === null ||
                          (data.status.toLowerCase() == 'active' && data.discountFlowInProcess.toLowerCase() != 'yes')
                        "
                        id="assign-button"
                        class="approve-btn"
                        title="Reassign shiftLocation"
                        (click)="discountReasignListShiftLocation(data)"
                      >
                        <img
                          width="32"
                          height="32"
                          alt="Assign shiftLocation"
                          src="assets/img/icons-02.png"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="addUpdateBtn" style="margin-top: 20px">
            <button
            *ngIf="updateAccess"
              (click)="updateDiscount()"
              class="btn btn-primary"
              data-title="Update Discount Details"
              data-toggle="tooltip"
              type="submit"
            >
              <i class="fa fa-check-circle"></i>
              Update Discount
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Approve Reject Modal Start -->
<p-dialog
  header="{{ AppRjecHeader }}Discount"
  [(visible)]="rejectApproveDiscountModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="assignAppRejectDiscountForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid':
                assignDiscounsubmitted && assignAppRejectDiscountForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="assignDiscounsubmitted && assignAppRejectDiscountForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="
                assignDiscounsubmitted &&
                assignAppRejectDiscountForm.controls.remark.errors.required
              "
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="assignDiscountApprove()"
      [disabled]="!assignAppRejectDiscountForm.valid"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      {{ AppRjecHeader }}
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeRejectApproveDiscountModal()"
    >
      Close
    </button>
  </div>
</p-dialog>
<!-- Approve Reject Modal End -->

<!-- Approve Assign Staff Modal Start -->
<p-dialog
  header="Assign Staff"
  [(visible)]="assignCustomerInventoryModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="row">
                                    <div class="col-md-6">
                                        <input id="searchStaffName" type="text" name="username" class="form-control"
                                            placeholder="Global Search Filter" [(ngModel)]="searchStaffDeatil"
                                            (keydown.enter)="searchStaffByName()"
                                            [ngModelOptions]="{ standalone: true }" />
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-sm-12">
                                        <button (click)="searchStaffByName()" class="btn btn-primary" id="searchbtn"
                                            type="submit" [disabled]="!searchStaffDeatil">
                                            <i class="fa fa-search"></i>
                                            Search
                                        </button>
                                        <button (click)="clearSearchForm()" class="btn btn-default" id="searchbtn"
                                            type="reset">
                                            <i class="fa fa-refresh"></i>
                                            Clear
                                        </button>
                                    </div>
                                </div>
        <div class="card">
          <h5>Select Staff</h5>
          <p-table
            [(selection)]="selectStaff"
            [value]="approveInventoryData"
            responsiveLayout="scroll"
             [paginator]="true" [rows]="5" [rowsPerPageOptions]="[5, 10, 15,20]"

          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToStaff(true)"
      *ngIf="approved"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeAssignDiscountModal()"
    >
      Close
    </button>
  </div>
</p-dialog>

<!-- Approve Assign Staff Modal end -->

<!-- Reject Assign Staff Modal Start -->
<p-dialog
  header="Assign Staff"
  [(visible)]="rejectCustomerInventoryModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <h5>Select Staff</h5>
          <p-table
            [(selection)]="selectStaffReject"
            [value]="rejectInventoryData"
            responsiveLayout="scroll"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToStaff(false)"
      *ngIf="reject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      type="button"
      (click)="closeRejectCustomerInventoryModal()"
    >
      Close
    </button>
  </div>
</p-dialog>
<!-- Reject Assign Staff Modal End -->

<!-- Pick Modal Start -->

<!-- Pick Modal End -->
<app-workflow-audit-details-modal
  *ngIf="ifModelIsShow"
  [auditcustid]="auditcustid"
  dialogId="custauditWorkflowModal"
  (closeParentCustt)="closeParentCustt()"
></app-workflow-audit-details-modal>
<!-- {custName}="customerLedgerDetailData?.custname" -->
