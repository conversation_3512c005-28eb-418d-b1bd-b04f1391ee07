<!-- <div class="panel-heading">
    <h3 class="panel-title">{{ isPlanEdit ? "Update" : "Create" }} Plan</h3>
    <div class="right">
      <button
        aria-controls="createplan"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#createplan"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div> -->

<div>
  <form [formGroup]="planGroupForm">
    <!--    Plan Information   -->
    <fieldset style="margin-top: 0px">
      <legend>Plan Information</legend>
      <div class="boxWhite">
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.name.enabled"
          >
            <label>Plan Name *</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.name.errors
              }"
              class="form-control"
              formControlName="name"
              id="name"
              placeholder="Enter Plan Name"
              type="text"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.name.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.name.errors.required"
                class="error text-danger"
              >
                Plan Name is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>Plan Type *</label>
            <p-dropdown
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.plantype.errors
              }"
              [options]="planTypeData"
              (onChange)="plantypeEvntData($event)"
              filter="true"
              filterBy="text"
              formControlName="plantype"
              optionLabel="text"
              optionValue="value"
              placeholder="Select a Plan Type"
              [disabled]="true"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.plantype.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.plantype.errors.required"
                class="error text-danger"
              >
                Plan Type is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>Service Area *</label>

            <p-multiSelect
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.serviceAreaIds.errors
              }"
              [options]="commondropdownService.serviceAreaList"
              defaultLabel="Select Area"
              formControlName="serviceAreaIds"
              id="roles"
              optionLabel="name"
              optionValue="id"
              [disabled]="ifLeadQuickInput"  
            ></p-multiSelect>
            <div
              *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors.required"
                class="error text-danger"
              >
                Service Area is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>Validity *</label>
            <div style="display: flex">
              <div style="width: 60%">
                <input
                  [ngClass]="{
                    'is-invalid': submitted && planGroupForm.controls.validity.errors
                  }"
                  [readonly]="(this.ifPlanEditInput && isPlanEdit) || ifLeadQuickInput"
                  class="form-control"
                  formControlName="validity"
                  id="validity"
                  min="1"
                  placeholder="Enter Validity"
                  type="number"
                />
                <div
                  *ngIf="submitted && planGroupForm.controls.validity.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && planGroupForm.controls.validity.errors.required"
                    class="error text-danger"
                  >
                    Validity is required.
                  </div>
                  <div
                    *ngIf="submitted && planGroupForm.controls.validity.errors.pattern"
                    class="error text-danger"
                  >
                    Only Numeric value are allowed.
                  </div>
                </div>
              </div>
              <div style="width: 40%; height: 34px">
                <p-dropdown
                  [disabled]="this.ifPlanEditInput && isPlanEdit"
                  [ngClass]="{
                    'is-invalid': submitted && planGroupForm.controls.unitsOfValidity.errors
                  }"
                  [options]="validityUnit"
                  filter="true"
                  filterBy="label"
                  formControlName="unitsOfValidity"
                  optionLabel="label"
                  optionValue="label"
                  placeholder="Select Unit"
                  [disabled]="ifLeadQuickInput"  
                ></p-dropdown>

                <div></div>
                <div
                  *ngIf="submitted && planGroupForm.controls.unitsOfValidity.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="submitted && planGroupForm.controls.unitsOfValidity.errors.required"
                    class="error text-danger"
                  >
                    Unit is required.
                  </div>
                </div>
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>Description *</label>
            <textarea
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.desc.errors
              }"
              class="form-control"
              formControlName="desc"
              placeholder="Enter Description"                          
              [readonly]="ifLeadQuickInput"
            ></textarea>
            <div
              *ngIf="submitted && planGroupForm.controls.desc.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                class="error text-danger"
              >
                Description is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                class="error text-danger"
              >
                Maximum 255 charecter required.
              </div>
            </div>
            <br />
          </div>
        </div>
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.serviceId.enabled"
          >
            <label>Service *</label>
            <p-dropdown
              [disabled]="isPlanEdit || ifLeadQuickInput"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.serviceId.errors
              }"
              [options]="serviceData"
              filter="true"
              filterBy="name"
              formControlName="serviceId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Service"
            ></p-dropdown>
            <div></div>

            <div class="error text-danger">
              <div class="error text-danger">Please select Plan Type first!</div>
            </div>
            <div
              *ngIf="submitted && planGroupForm.controls.serviceId.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.serviceId.errors.required"
                class="error text-danger"
              >
                Service is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.displayName.enabled"
          >
            <label>Display Name *</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.displayName.errors
              }"
              class="form-control"
              formControlName="displayName"
              id="displayName"
              placeholder="Enter Display Name"
              type="text"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.displayName.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.displayName.errors.required"
                class="error text-danger"
              >
                Display Name is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.code.enabled"
          >
            <label>Plan Code</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.code.errors
              }"
              class="form-control"
              formControlName="code"
              id="code"
              placeholder="Enter Plan Code"
              type="text"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.code.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.code.errors.required"
                class="error text-danger"
              >
                Plan Code is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.startDate.enabled"
          >
            <label>Start Date *</label>
            <input
              (change)="dateDisble($event)"
              [max]="maxDisbleDate"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.startDate.errors
              }"
              class="form-control"
              formControlName="startDate"
              placeholder="DD/MM/YYYY"
              type="date"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.startDate.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.startDate.errors.required"
                class="error text-danger"
              >
                Start Date is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.endDate.enabled"
          >
            <label>End Date *</label>
            <input
              (change)="dateMaxDisble($event)"
              [min]="disbleDate"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.endDate.errors
              }"
              class="form-control"
              formControlName="endDate"
              placeholder="DD/MM/YYYY"
              type="date"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.endDate.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.endDate.errors.required"
                class="error text-danger"
              >
                End Date is required.
              </div>
            </div>
            <br />
          </div>
        </div>

        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.category.enabled"
          >
            <label>Plan Category *</label>
            <p-dropdown
              (onChange)="selPlanCategory($event)"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.category.errors
              }"
              [options]="planCategoryData"
              filter="true"
              filterBy="text"
              formControlName="category"
              optionLabel="text"
              optionValue="value"
              placeholder="Select a Plan Category"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.category.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.category.errors.required"
                class="error text-danger"
              >
                Plan Category is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.mode.enabled"
          >
            <label>Plan Mode *</label>
            <p-dropdown
              (onChange)="selPlanMode($event)"
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.mode.errors
              }"
              [options]="type"
              formControlName="mode"
              optionLabel="label"
              optionValue="label"
              placeholder="Select a Plan Mode"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.mode.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.mode.errors.required"
                class="error text-danger"
              >
                Plan Mode is required.
              </div>
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.planGroup.enabled"
          >
            <label>Plan Group *</label>
            <p-dropdown
              (onChange)="selPlanGroup($event)"
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.planGroup.errors
              }"
              [options]="planGroupData"
              filter="true"
              filterBy="text"
              formControlName="planGroup"
              optionLabel="text"
              optionValue="value"
              placeholder="Select a Plan Group"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.planGroup.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.planGroup.errors.required"
                class="error text-danger"
              >
                Plan Group is required.
              </div>
            </div>
            <br />
          </div>
        </div>
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.accessibility.enabled"
          >
            <label>Accessibility</label>

            <p-dropdown
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.accessibility.errors
              }"
              [options]="accessibilityData"
              id="roles"
              optionLabel="text"
              optionValue="value"
              filter="true"
              filterBy="text"
              placeholder="Select Accessibility"
              formControlName="accessibility"
            ></p-dropdown>
            <div
              *ngIf="submitted && planGroupForm.controls.accessibility.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.accessibility.errors.required"
                class="error text-danger"
              >
                Accessibility is required.
              </div>
            </div>
            <br />
            <br />
          </div>
        </div>
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.allowdiscount.enabled"
          >
            <label>Allow Discount *</label>

            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.allowdiscount.errors
              }"
              [options]="planDiscount"
              filter="true"
              filterBy="label"
              formControlName="allowdiscount"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Discount"
            ></p-dropdown>
            <div
              *ngIf="submitted && planGroupForm.controls.allowdiscount.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.allowdiscount.errors.required"
                class="error text-danger"
              >
                Allow Discount is required.
              </div>
            </div>
            <br />
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.status.enabled"
          >
            <label>Status *</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.status.errors
              }"
              [options]="planStatus"
              filter="true"
              filterBy="label"
              formControlName="status"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Status"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.status.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.status.errors.required"
                class="error text-danger"
              >
                status is required.
              </div>
            </div>
            <br />
          </div>
          <!-- <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf=" 
                      (planGroupForm.value.category == 'Business Promotion' &&
                        !isServiceHideField) ||
                      (planGroupForm.value.category == 'Business Promotion' && isServiceHideField)
                    "
                  >
                    <label>Invoice To Org: *</label>

                    <p-dropdown
                      [disabled]="editMode"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.invoiceToOrg.errors
                      }"
                      [options]="planDiscount"
                      filter="true"
                      filterBy="label"
                      formControlName="invoiceToOrg"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select Invoice to org or not"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors.required"
                        class="error text-danger"
                      >
                        Invoice To Org is required.
                      </div>
                    </div>
                    <br />
                  </div> -->
          <!-- <div
                    *ngIf="
                      planGroupForm.value.category !== 'Business Promotion' && !isServiceHideField
                    "
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                  >
                    <label>Allow Over Usage *</label>
                    <p-dropdown
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.allowOverUsage.errors
                      }"
                      [options]="planCategory"
                      filter="true"
                      filterBy="label"
                      formControlName="allowOverUsage"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Plan Category"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors.required"
                        class="error text-danger"
                      >
                        Allow Over Usage is required.
                      </div>
                    </div>
                    <br />
                  </div> -->
        </div>
        <div class="row">
          <!-- <div
                  class="col-lg-3 col-md-6 col-sm-6 col-xs-12 left"
                  *ngIf="
                    planGroupForm.value.category !== 'Business Promotion' && isServiceHideField
                  "
                >
                  <label>Description *</label>
                  <textarea
                    [ngClass]="{
                      'is-invalid': submitted && planGroupForm.controls.desc.errors
                    }"
                    class="form-control"
                    formControlName="desc"
                    placeholder="Enter Description"
                  ></textarea>
                  <div
                    *ngIf="submitted && planGroupForm.controls.desc.errors"
                    class="errorWrap text-danger"
                  >
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                      class="error text-danger"
                    >
                      Description is required.
                    </div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                      class="error text-danger"
                    >
                      Maximum 255 charecter required.
                    </div>
                  </div>
                  <br />
                </div>
                  <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf="planGroupForm.value.category == 'Business Promotion'"
                  >
                    <label>Required Approval *</label>

                    <p-dropdown
                      [disabled]="editMode"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.requiredApproval.errors
                      }"
                      [options]="planDiscount"
                      filter="true"
                      filterBy="label"
                      formControlName="requiredApproval"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Required Approval"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.requiredApproval.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.requiredApproval.errors.required"
                        class="error text-danger"
                      >
                        Required Approval is required.
                      </div>
                    </div>
                    <br />
                  </div> -->
          <!-- <div
                    *ngIf="
                      planGroupForm.value.category == 'Business Promotion' && !isServiceHideField
                    "
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                  >
                    <label>Allow Over Usage *</label>
                    <p-dropdown
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.allowOverUsage.errors
                      }"
                      [options]="planCategory"
                      filter="true"
                      filterBy="label"
                      formControlName="allowOverUsage"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Plan Category"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors.required"
                        class="error text-danger"
                      >
                        Allow Over Usage is required.
                      </div>
                    </div>
                    <br />
                  </div> -->
          <div
            *ngIf="planGroupForm.controls.maxconcurrentsession.enabled"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Max Current Session *</label>
            <input
              [readonly]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.maxconcurrentsession.errors
              }"
              class="form-control"
              formControlName="maxconcurrentsession"
              id="maxconcurrentsession"
              min="1"
              placeholder="Enter Max Current Session"
              type="number"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.maxconcurrentsession.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.maxconcurrentsession.errors.required"
                class="error text-danger"
              >
                Max Current Session is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.maxconcurrentsession.errors.pattern"
                class="error text-danger"
              >
                Only Numeric Character Allowed.
              </div>
            </div>
            <br />
          </div>
          <!-- <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12 left"
                    *ngIf="
                      (planGroupForm.value.category == 'Business Promotion' &&
                        !isServiceHideField) ||
                      (planGroupForm.value.category == 'Business Promotion' &&
                        isServiceHideField) ||
                      (planGroupForm.value.category !== 'Business Promotion' && !isServiceHideField)
                    "
                  >
                    <label>Description *</label>
                    <textarea
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.desc.errors
                      }"
                      class="form-control"
                      formControlName="desc"
                      placeholder="Enter Description"
                    ></textarea>
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                        class="error text-danger"
                      >
                        Description is required.
                      </div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                        class="error text-danger"
                      >
                        Maximum 255 charecter required.
                      </div>
                    </div>
                    <br />
                  </div> -->
        </div>

        <!-- <div *ngIf="ifplanGroup_VB && !ifplanGroup_BWB">
                <div class="row">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Plan Category *</label>
                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.category.errors
                      }"
                      [options]="planCategoryData"
                      filter="true"
                      filterBy="text"
                      formControlName="category"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Plan Category"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.category.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.category.errors.required"
                        class="error text-danger"
                      >
                        Plan Category is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Plan Group *</label>
                    <p-dropdown
                      (onChange)="selPlanGroup($event)"
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.planGroup.errors
                      }"
                      [options]="planGroupData"
                      filter="true"
                      filterBy="text"
                      formControlName="planGroup"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Plan Group"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.planGroup.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.planGroup.errors.required"
                        class="error text-danger"
                      >
                        Plan Group is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Service *</label>
                    <p-dropdown
                      [disabled]="isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.serviceId.errors
                      }"
                      [options]="serviceData"
                      filter="true"
                      filterBy="name"
                      formControlName="serviceId"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a Service"
                      (onChange)="selService($event)"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.serviceId.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.serviceId.errors.required"
                        class="error text-danger"
                      >
                        Service is required.
                      </div>
                    </div>

                    <div *ngIf="!this.planGroupForm.value.plantype" class="error text-danger">
                      <div class="error text-danger">Please select Plan Type first!</div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Service Area *</label>

                    <p-multiSelect
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.serviceAreaIds.errors
                      }"
                      [options]="commondropdownService.serviceAreaList"
                      defaultLabel="Select Area"
                      formControlName="serviceAreaIds"
                      id="roles"
                      optionLabel="name"
                      optionValue="id"
                      (onChange)="selServiceArea($event)"
                    ></p-multiSelect>
                    <div
                      *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors.required"
                        class="error text-danger"
                      >
                        Service Area is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Accessibility</label>

                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.accessibility.errors
                      }"
                      [options]="accessibilityData"
                      id="roles"
                      optionLabel="text"
                      optionValue="value"
                      filter="true"
                      filterBy="text"
                      placeholder="Select Accessibility"
                      formControlName="accessibility"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.accessibility.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.accessibility.errors.required"
                        class="error text-danger"
                      >
                        Accessibility is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Start Date *</label>
                    <input
                      (change)="dateDisble($event)"
                      [max]="maxDisbleDate"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.startDate.errors
                      }"
                      class="form-control"
                      formControlName="startDate"
                      placeholder="DD/MM/YYYY"
                      type="date"
                    />
                    <div
                      *ngIf="submitted && planGroupForm.controls.startDate.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.startDate.errors.required"
                        class="error text-danger"
                      >
                        Start Date is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>End Date *</label>
                    <input
                      (change)="dateMaxDisble($event)"
                      [min]="disbleDate"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.endDate.errors
                      }"
                      class="form-control"
                      formControlName="endDate"
                      placeholder="DD/MM/YYYY"
                      type="date"
                    />
                    <div
                      *ngIf="submitted && planGroupForm.controls.endDate.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.endDate.errors.required"
                        class="error text-danger"
                      >
                        End Date is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Validity *</label>
                    <div style="display: flex">
                      <div style="width: 60%">
                        <input
                          [readonly]="this.ifPlanEditInput && isPlanEdit"
                          class="form-control"
                          formControlName="validity"
                          id="validity"
                          min="1"
                          placeholder="Enter Validity"
                          readonly
                          type="number"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.validity.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.validity.errors.required"
                            class="error text-danger"
                          >
                            Validity is required.
                          </div>
                          <div
                            *ngIf="submitted && planGroupForm.controls.validity.errors.pattern"
                            class="error text-danger"
                          >
                            Only Numeric value are allowed.
                          </div>
                        </div>
                      </div>
                      <div style="width: 40%; height: 34px">
                        <p-dropdown
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.unitsOfValidity.errors
                          }"
                          [options]="validityUnit"
                          filter="true"
                          filterBy="label"
                          formControlName="unitsOfValidity"
                          optionLabel="label"
                          optionValue="label"
                          placeholder="Select Unit"
                        ></p-dropdown>

                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.unitsOfValidity.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.unitsOfValidity.errors.required
                            "
                            class="error text-danger"
                          >
                            Unit is required.
                          </div>
                        </div>
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Allow Discount *</label>

                    <p-dropdown
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.allowdiscount.errors
                      }"
                      [options]="planDiscount"
                      filter="true"
                      filterBy="label"
                      formControlName="allowdiscount"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Discount"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.allowdiscount.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.allowdiscount.errors.required"
                        class="error text-danger"
                      >
                        Discount is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Status *</label>
                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.status.errors
                      }"
                      [options]="statusOptions"
                      filter="true"
                      filterBy="label"
                      formControlName="status"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Status"
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="submitted && planGroupForm.controls.status.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.status.errors.required"
                        class="error text-danger"
                      >
                        status is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf="planGroupForm.value.category == 'Business Promotion'"
                  >
                    <label>Invoice To Org: *</label>

                    <p-dropdown
                      [disabled]="editMode"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.invoiceToOrg.errors
                      }"
                      [options]="planDiscount"
                      filter="true"
                      filterBy="label"
                      formControlName="invoiceToOrg"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select Invoice to org or not"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors.required"
                        class="error text-danger"
                      >
                        Invoice To Org is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf="planGroupForm.value.category == 'Business Promotion'"
                  >
                    <label>Required Approval *</label>

                    <p-dropdown
                      [disabled]="editMode"
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.requiredApproval.errors
                      }"
                      [options]="planDiscount"
                      filter="true"
                      filterBy="label"
                      formControlName="requiredApproval"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="Select a Required Approval"
                    ></p-dropdown>
                    <div
                      *ngIf="submitted && planGroupForm.controls.requiredApproval.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.requiredApproval.errors.required"
                        class="error text-danger"
                      >
                        Required Approval is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf="planGroupForm.value.category !== 'Business Promotion'"
                  >
                    <label>Description *</label>
                    <textarea
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.desc.errors
                      }"
                      class="form-control"
                      formControlName="desc"
                      placeholder="Enter Description"
                    ></textarea>
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                        class="error text-danger"
                      >
                        Description is required.
                      </div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                        class="error text-danger"
                      >
                        Maximum 255 charecter required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row">
                  <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    *ngIf="planGroupForm.value.category == 'Business Promotion'"
                  >
                    <label>Description *</label>
                    <textarea
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.desc.errors
                      }"
                      class="form-control"
                      formControlName="desc"
                      placeholder="Enter Description"
                    ></textarea>
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                        class="error text-danger"
                      >
                        Description is required.
                      </div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                        class="error text-danger"
                      >
                        Maximum 255 charecter required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
              </div> -->
      </div>
    </fieldset>

    <!--    Quota Details   -->
    <fieldset *ngIf="!isServiceHideField">
      <legend>Quota Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12" *ngIf="!ifplanGroup_BWB">
            <label>Quota Type *</label>
            <p-dropdown
              (onChange)="getSelQoutaType($event)"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotatype.errors
              }"
              [options]="quotaTypeData"
              filter="true"
              filterBy="text"
              formControlName="quotatype"
              optionLabel="text"
              optionValue="value"
              placeholder="Select a Quota Type"
              [disabled]="this.ifPlanEditInput && isPlanEdit"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.quotatype.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotatype.errors.required"
                class="error text-danger"
              >
                Quota Type is required.
              </div>
            </div>
            <br />
          </div>
          <div *ngIf="!isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>Base Qos Policy</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.qospolicyid.errors
              }"
              [options]="qosPolicyData"
              filter="true"
              filterBy="name"
              formControlName="qospolicyid"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Base Qos Policy"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.qospolicyid.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.qospolicyid.errors.required"
                class="error text-danger"
              >
                Base Qos Policy is required.
              </div>
            </div>
            <br />
          </div>
          <div
            *ngIf="this.planGroupForm.controls.quotaUnit.enabled && !ifplanGroup_BWB"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Quota Unit *</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotaUnit.errors
              }"
              [options]="quotaData"
              filter="true"
              filterBy="text"
              formControlName="quotaUnit"
              optionLabel="text"
              optionValue="value"
              placeholder="Select a Quota Unit"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.quotaUnit.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotaUnit.errors.required"
                class="error text-danger"
              >
                Quota Unit is required.
              </div>
            </div>
            <br />
          </div>
          <div
            *ngIf="this.isQuotaEnabled && !ifplanGroup_BWB"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Quota *</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quota.errors
              }"
              [readonly]="this.ifPlanEditInput && isPlanEdit"
              class="form-control"
              formControlName="quota"
              id="Quota"
              min="0"
              pInputText
              placeholder="Enter Quota"
              type="number"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.quota.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quota.errors.required"
                class="error text-danger"
              >
                Quota is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.quota.errors.pattern"
                class="error text-danger"
              >
                Only Numeric Value allowed.
              </div>
            </div>
            <br />
          </div>
          <div
            *ngIf="this.planGroupForm.controls.quotaunittime.enabled && !ifplanGroup_BWB"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Quota Unit Time *</label>
            <p-dropdown
              (onChange)="quotaUnitChange($event)"
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotaunittime.errors
              }"
              [options]="qutaUnitTime"
              filter="true"
              filterBy="label"
              formControlName="quotaunittime"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Quota Unit Time"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.quotaunittime.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotaunittime.errors.required"
                class="error text-danger"
              >
                Quota Unit Time is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                class="error text-danger"
              >
                Only Numeric Value allowed.
              </div>
              <!-- <div class="error text-danger"
                                                  *ngIf="submitted && planGroupForm.controls.quotatime.errors.max">
                                                  Max {{max}} value allowed.
                                              </div> -->
            </div>
            <br />
          </div>

          <div
            *ngIf="
              this.isQuotaTimeEnabled &&
              this.planGroupForm.value.quotatype !== 'Both' &&
              this.planGroupForm.value.quotatype &&
              !ifplanGroup_BWB
            "
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Quota Time *</label>
            <input
              [max]="max"
              [min]="1"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotatime.errors
              }"
              [readonly]="this.ifPlanEditInput && isPlanEdit"
              class="form-control"
              formControlName="quotatime"
              id="quotatime"
              pInputText
              placeholder="Enter Quota Time"
              type="number"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.quotatime.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotatime.errors.required"
                class="error text-danger"
              >
                Quota Time is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                class="error text-danger"
              >
                Only Numeric Value allowed.
              </div>
              <!-- <div class="error text-danger"
                                                              *ngIf="submitted && planGroupForm.controls.quotatime.errors.max">
                                                              Max {{max}} value allowed.
                                                          </div> -->
            </div>
            <br />
          </div>

          <div
            *ngIf="this.planGroupForm.value.quotatype == 'Both' && !ifplanGroup_BWB"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Quota Time *</label>
            <input
              [max]="max"
              [min]="1"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotatime.errors
              }"
              [readonly]="this.ifPlanEditInput && isPlanEdit"
              class="form-control"
              formControlName="quotatime"
              id="quotatime"
              pInputText
              placeholder="Enter Quota Time"
              type="number"
            />
            <div
              *ngIf="submitted && planGroupForm.controls.quotatime.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotatime.errors.required"
                class="error text-danger"
              >
                Quota Time is required.
              </div>
              <div
                *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                class="error text-danger"
              >
                Only Numeric Value allowed.
              </div>
              <!-- <div class="error text-danger"
                                                                  *ngIf="submitted && planGroupForm.controls.quotatime.errors.max">
                                                                  Max {{max}} value allowed.
                                                              </div> -->
            </div>
            <br />
          </div>
        </div>
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="this.planGroupForm.value.quotatype !== 'Both' && !ifplanGroup_BWB"
          >
            <label>Quota Reset Interval *</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [ngClass]="{
                'is-invalid': submitted && planGroupForm.controls.quotaResetInterval.errors
              }"
              [options]="quotaResetIntervalData"
              filter="true"
              filterBy="label"
              formControlName="quotaResetInterval"
              optionLabel="label"
              optionValue="label"
              placeholder="Select a Interval"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors.required"
                class="error text-danger"
              >
                Quota Type is required.
              </div>
              <!-- <div class="error text-danger"
                                                    *ngIf="submitted && planGroupForm.controls.quotatime.errors.max">
                                                    Max {{max}} value allowed.
                                                </div> -->
            </div>
            <br />
          </div>
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="this.planGroupForm.value.quotatype == 'Both' && !ifplanGroup_BWB"
          >
            <label>Quota Reset Interval *</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [options]="quotaResetIntervalData"
              filter="true"
              filterBy="label"
              formControlName="quotaResetInterval"
              optionLabel="label"
              optionValue="label"
              placeholder="Select a Interval"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors.required"
                class="error text-danger"
              >
                Quota Type is required.
              </div>
            </div>
            <br />
          </div>
          <div *ngIf="!isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <label>TimeBase Policy</label>
            <p-dropdown
              [disabled]="this.ifPlanEditInput && isPlanEdit"
              [options]="timeBasePolicyData"
              filter="true"
              filterBy="name"
              formControlName="timebasepolicyId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a TimeBase Policy"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="submitted && planGroupForm.controls.timebasepolicyId.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="submitted && planGroupForm.controls.timebasepolicyId.errors.required"
                class="error text-danger"
              >
                TimeBase Policy is required.
              </div>
            </div>
            <br />
          </div>
        </div>
      </div>
    </fieldset>

    <!--    Overusage Quota QoS Policy   -->
    <fieldset
      *ngIf="
        planGroupForm.controls.allowOverUsage.enabled &&
        planGroupForm.value.allowOverUsage &&
        planGroupForm.value.qospolicyid !== null &&
        planGroupForm.value.qospolicyid !== ''
      "
    >
      <legend>Overusage Quota QoS Policy</legend>
      <div class="boxWhite">
        <div [formGroup]="qospolicyformgroup" class="row">
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <input
              class="form-control"
              formControlName="frompercentage"
              id="frompercentage"
              placeholder="Enter From(%)"
              type="text"
              (change)="setToValidation()"
            />
            <div
              *ngIf="qospolicySubmitted && qospolicyformgroup.controls.frompercentage.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  qospolicySubmitted && qospolicyformgroup.controls.frompercentage.errors.required
                "
                class="error text-danger"
              >
                From (%) is required.
              </div>
              <div
                *ngIf="qospolicySubmitted && qospolicyformgroup.controls.frompercentage.errors.min"
                class="error text-danger"
              >
                From (%) should be greater than {{ minfrompercentage }}.
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <input
              class="form-control"
              formControlName="topercentage"
              id="topercentage"
              placeholder="Enter To(%)"
              type="text"
            />
            <div
              *ngIf="qospolicySubmitted && qospolicyformgroup.controls.topercentage.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  qospolicySubmitted && qospolicyformgroup.controls.topercentage.errors.required
                "
                class="error text-danger"
              >
                To (%) is required.
              </div>
              <div
                *ngIf="qospolicySubmitted && qospolicyformgroup.controls.topercentage.errors.min"
                class="error text-danger"
              >
                To (%) should be greater than {{ mintopercentage }}.
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <p-dropdown
              [ngClass]="{
                'is-invalid': qospolicySubmitted && qospolicyformgroup.controls.qosid.errors
              }"
              [options]="qosPolicyData"
              filter="true"
              filterBy="name"
              formControlName="qosid"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Qos Policy"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="qospolicySubmitted && qospolicyformgroup.controls.qosid.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="qospolicySubmitted && qospolicyformgroup.controls.qosid.errors.required"
                class="error text-danger"
              >
                Qos Policy is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <button
              (click)="onAddQosPolicy()"
              class="btn btn-primary"
              id="addAtt"
              style="object-fit: cover; padding: 5px 8px"
              [disabled]="this.ifPlanEditInput && isPlanEdit"
            >
              <i aria-hidden="true" class="fa fa-plus-square"></i>
              Add
            </button>
          </div>
        </div>
        <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
          <table class="table coa-table">
            <thead>
              <tr>
                <th style="text-align: center">From (%)</th>
                <th style="text-align: center">To (%)</th>
                <th style="text-align: center">QoS Policy</th>
                <th style="text-align: right; padding: 8px">Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let row of qospolicyformArray.controls
                    | paginate
                      : {
                          id: 'qospolicyformArrayData',
                          itemsPerPage: qospolicyitemsPerPage,
                          currentPage: currentPageqospolicye,
                          totalItems: qospolicytotalRecords
                        };
                  let index = index
                "
              >
                <td style="padding-left: 8px">
                  <input
                    class="form-control"
                    [formControl]="row.get('frompercentage')"
                    id="frompercentage"
                    placeholder="Enter From(%)"
                    type="text"
                  />
                </td>
                <td>
                  <input
                    class="form-control"
                    [formControl]="row.get('topercentage')"
                    id="topercentage"
                    placeholder="Enter To(%)"
                    type="text"
                  />
                </td>
                <td style="padding-left: 8px">
                  <p-dropdown
                    [disabled]="true"
                    [formControl]="row.get('qosid')"
                    [options]="qosPolicyData"
                    filter="true"
                    filterBy="name"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Qos Policy"
                  ></p-dropdown>
                  <div></div>
                </td>
                <td style="text-align: right">
                  <button
                    (click)="deleteConfirmonQosPolicy(index)"
                    id="deleteAtt"
                    [disabled]="this.ifPlanEditInput && isPlanEdit"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </fieldset>

    <!--    Additional inforamation   -->
    <fieldset *ngIf="planGroupForm.controls.saccode.enabled">
      <legend>Additional Information</legend>
      <div class="boxWhite">
        <div class="row">
          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.saccode.enabled"
          >
            <label>SAC Code</label>
            <input
              class="form-control"
              formControlName="saccode"
              id="saccode"
              placeholder="Enter SAC Code"
              type="text"
            />
            <br />
          </div>

          <div
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
            *ngIf="planGroupForm.controls.param1.enabled"
          >
            <label>Param 1</label>
            <input
              class="form-control"
              formControlName="param1"
              id="param1"
              placeholder="Enter Param 1"
              type="text"
            />
            <br />
          </div>
          <div
            *ngIf="isServiceHideField && planGroupForm.controls.param2.enabled"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Param 2</label>
            <input
              class="form-control"
              formControlName="param2"
              id="param2"
              placeholder="Enter Param 2"
              type="text"
            />
            <br />
          </div>
          <div
            *ngIf="isServiceHideField && planGroupForm.controls.param3.enabled"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Param 3</label>
            <input
              class="form-control"
              formControlName="param3"
              id="param3"
              placeholder="Enter Param 3"
              type="text"
            />
            <br />
          </div>
        </div>
        <div class="row">
          <div
            *ngIf="!isServiceHideField && planGroupForm.controls.param2.enabled"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Param 2</label>
            <input
              class="form-control"
              formControlName="param2"
              id="param2"
              placeholder="Enter Param 2"
              type="text"
            />
            <br />
          </div>
          <div
            *ngIf="!isServiceHideField && planGroupForm.controls.param3.enabled"
            class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
          >
            <label>Param 3</label>
            <input
              class="form-control"
              formControlName="param3"
              id="param3"
              placeholder="Enter Param 3"
              type="text"
            />
            <br />
          </div>
        </div>
      </div>
    </fieldset>

    <!--    Additional inforamation   -->
    <!-- <fieldset *ngIf="!ifplanGroup_VB && ifplanGroup_BWB">
            <legend>Additional Information</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                  <label>SAC Code</label>
                  <input
                    class="form-control"
                    formControlName="saccode"
                    id="saccode"
                    placeholder="Enter SAC Code"
                    type="text"
                  />
                  <br />
                </div>
              </div>
            </div>
          </fieldset> -->

    <!-- Charge -->
    <!-- <fieldset>
              <legend>Charge Details</legend>
              <div class="boxWhite">
                <div [formGroup]="planGroupForm" class="row">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <label>Plan Price *</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.offerprice.errors
                      }"
                      class="form-control"
                      formControlName="offerprice"
                      id="offerprice"
                      min="1"
                      placeholder="Enter Plan Price"
                      type="number"
                      readonly
                    />
                    <br />
                  </div>
                  <div
                    *ngIf="planGroupForm.value.category == 'Business Promotion'"
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                  >
                    <label>New Offer Price</label>
                    <input
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.newOfferPrice.errors
                      }"
                      class="form-control"
                      formControlName="newOfferPrice"
                      id="newOfferPrice"
                      min="1"
                      placeholder="Enter Offer Price"
                      type="number"
                    />
                    <div
                      *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors.required"
                        class="error text-danger"
                      >
                        New Offer Price is required.
                      </div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors.pattern"
                        class="error text-danger"
                      >
                        Only Numeric value is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div [formGroup]="chargefromgroup" class="row">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <p-dropdown
                      (onChange)="getofferPrice($event)"
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                      [ngClass]="{
                        'is-invalid': submitted && chargefromgroup.controls.id.errors
                      }"
                      [options]="advanceListData"
                      filter="true"
                      filterBy="name"
                      formControlName="id"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select Charge"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="chargeSubmitted && chargefromgroup.controls.id.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Charge is required.</div>
                    </div>
                  </div>
                  <div
                    *ngIf="chargefromgroup.controls.billingCycle.enabled"
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                  >
                    <p-dropdown
                      [ngClass]="{
                        'is-invalid': submitted && chargefromgroup.controls.billingCycle.errors
                      }"
                      [options]="billingCycle"
                      filter="true"
                      filterBy="label"
                      formControlName="billingCycle"
                      optionLabel="label"
                      optionValue="label"
                      placeholder="Select a Billing Cycle"
                    ></p-dropdown>
                    <div></div>
                    <div
                      *ngIf="chargeSubmitted && chargefromgroup.controls.billingCycle.errors"
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Billing Cycle is required.</div>
                    </div>
                  </div>

                  <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <button
                      (click)="onAddChargeField()"
                      class="btn btn-primary"
                      id="addAtt"
                      style="object-fit: cover; padding: 5px 8px"
                      [disabled]="this.ifPlanEditInput && isPlanEdit"
                    >
                      <i aria-hidden="true" class="fa fa-plus-square"></i>
                      Add
                    </button>
                  </div>
                </div>
                <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
                  <table class="table coa-table">
                    <thead>
                      <tr>
                        <th style="text-align: center">Charge Name</th>
                        <th
                          *ngIf="chargefromgroup.controls.billingCycle.enabled"
                          style="text-align: center"
                        >
                          Biling Cycle
                        </th>
                        <th style="text-align: center">Actual Price</th>
                        <th style="text-align: center">Charge Price</th>
                        <th style="text-align: center">Tax Amount</th>
                        <th style="text-align: center">Price(including tax)</th>
                        <th style="text-align: right; padding: 8px">
                          Delete
                         
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let row of chargeFromArray.controls
                            | paginate
                              : {
                                  id: 'chargeFromArrayData',
                                  itemsPerPage: chargeitemsPerPage,
                                  currentPage: currentPageCharge,
                                  totalItems: chargetotalRecords
                                };
                          let index = index
                        "
                      >
                        <td style="padding-left: 8px">
                          <p-dropdown
                            [disabled]="true"
                            [formControl]="row.get('id')"
                            [options]="commondropdownService.chargeList"
                            filter="true"
                            filterBy="name"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Select a Charge"
                          ></p-dropdown>
                          <div></div>
                        </td>
                        <td
                          *ngIf="chargefromgroup.controls.billingCycle.enabled"
                          style="padding-left: 8px"
                        >
                          <p-dropdown
                            [disabled]="this.ifPlanEditInput && isPlanEdit"
                            [formControl]="row.get('billingCycle')"
                            [options]="billingCycle"
                            filter="true"
                            filterBy="label"
                            optionLabel="label"
                            optionValue="label"
                            placeholder="Select a Billing Cycle"
                          ></p-dropdown>
                          <div></div>
                        </td>
                        <td>
                          <input
                            [formControl]="row.get('actualprice')"
                            class="form-control"
                            id="actualprice        "
                            min="0"
                            name="actualprice"
                            placeholder="Enter Actual Price"
                            type="number"
                            [readonly]="true"
                          />
                        </td>
                        <td>
                          <input
                            [formControl]="row.get('chargeprice')"
                            class="form-control"
                            id="chargeprice"
                            min="0"
                            name="chargeprice"
                            placeholder="Enter Charge Price"
                            type="number"
                            (input)="
                              changeActualPrice(row.value.chargeprice, row.value.id, index)
                            "
                          />
                        </td>

                        <td>
                          <input
                            data-backdrop="static"
                            data-keyboard="false"
                            data-target="#taxDetailModal"
                            data-toggle="modal"
                            [formControl]="row.get('taxamount')"
                            class="form-control"
                            id="taxamount        "
                            min="0"
                            name="taxamount"
                            placeholder="Enter Actual Price"
                            readonly
                            type="number"
                          />
                        </td>
                        <td>
                          <div
                            style="
                              box-shadow: 0px 1px 2px 0 rgb(0 0 0 / 22%);
                              border-radius: 2px;
                              border-color: #eaeaea;
                              background-color: #eeeeee;
                              display: block;
                              width: 100%;
                              height: 34px;
                              padding: 6px 12px;
                              font-size: 14px;
                              line-height: 1.42857143;
                              color: #555;
                              background-image: none;
                            "
                          >
                            <span *ngFor="let list of totalPriceData; index as j">
                              <span *ngIf="index === j">{{ list }}</span>
                            </span>
                          </div>
                        </td>

                        <td style="text-align: right">
                          <button
                            (click)="deleteConfirmonChargeField(index, row.get('id').value)"
                            id="deleteAtt"
                            [disabled]="this.ifPlanEditInput && isPlanEdit"
                            class="approve-btn"
                            style="
                              border: none;
                              background: transparent;
                              padding: 0;
                              margin-right: 3px;
                            "
                            type="button"
                            *ngIf="chargeFromArray.controls.length > 1"
                          >
                            <img src="assets/img/ioc02.jpg" />
                          </button>
                        </td>
                      </tr>
                      <tr *ngIf="chargeFromArray.controls.length !== 0">
                        <td *ngIf="chargefromgroup.controls.billingCycle.enabled"></td>
                        <td colspan="4" style="text-align: end">
                          <b>Total Price(including tax) :</b>
                        </td>
                        <td colspan="2">{{ countTotalOfferPrice }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

               
                <br />
              </div>
            </fieldset> -->
    <!-- Product -->
    <fieldset>
      <legend>Product Details</legend>
      <div class="boxWhite">
        <div [formGroup]="planProductfromgroup" *ngIf="!ifLeadQuickInput">
          <div class="row">
            <!-- <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                    <div class="form-group">
                      <input
                        class="form-control"
                        formControlName="name"
                        placeholder="Enter Name"
                        type="text"
                      />
                    </div>
                  </div> -->
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
              <div class="form-group">
                <label>Product Category</label>
                <p-dropdown
                  (onChange)="getProductbyCategory($event)"
                  [options]="commondropdownService.productCategoryList"
                  filter="true"
                  filterBy="name"
                  formControlName="productCategoryId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a Product Category"
                ></p-dropdown>
                <div
                  *ngIf="
                    planProductSubmitted && planProductfromgroup.controls.productCategoryId.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.productCategoryId.errors.required
                    "
                    class="error text-danger"
                  >
                    Product Category is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productFlag">
              <div class="form-group">
                <label>Product*</label>
                <p-dropdown
                  (onChange)="getProductDetails($event)"
                  [options]="productList"
                  filter="true"
                  filterBy="name"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a Product"
                ></p-dropdown>
                <div
                  *ngIf="planProductSubmitted && planProductfromgroup.controls.productId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.productId.errors.required
                    "
                    class="error text-danger"
                  >
                    Product is required.
                  </div>
                </div>
              </div>
            </div>
            <!-- </div> -->
            <!-- <div class="row"> -->
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productTypeFlag">
              <div class="form-group">
                <label>Product Type*</label>
                <p-dropdown
                  (onChange)="getChargeAmount($event)"
                  [options]="productType"
                  filter="true"
                  filterBy="label"
                  formControlName="product_type"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a Product Type"
                ></p-dropdown>
                <div
                  *ngIf="planProductSubmitted && planProductfromgroup.controls.product_type.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.product_type.errors.required
                    "
                    class="error text-danger"
                  >
                    Product Type is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="ownershipTypeFlag">
              <div class="form-group">
                <label>Product Quantity *</label>
                <input
                  [ngClass]="{
                    'is-invalid':
                      planProductSubmitted && planProductfromgroup.controls.productQuantity.errors
                  }"
                  class="form-control"
                  formControlName="productQuantity"
                  min="1"
                  type="number"
                />
                <div
                  *ngIf="
                    planProductSubmitted && planProductfromgroup.controls.productQuantity.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.productQuantity.errors.required
                    "
                    class="error text-danger"
                  >
                    Product Quantity is required.
                  </div>
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.productQuantity.errors.min
                    "
                    class="error text-danger"
                  >
                    Minimum 1 Allowed.
                  </div>
                </div>
              </div>
            </div>
            <!-- </div>
                  <div class="row"> -->
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="ownershipTypeFlag">
              <div class="form-group">
                <label>Ownership Type*</label>
                <p-dropdown
                  (onChange)="checkOwnership($event)"
                  [options]="ownershipType"
                  filter="true"
                  filterBy="label"
                  formControlName="ownershipType"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a Ownership Type"
                ></p-dropdown>
                <div
                  *ngIf="planProductSubmitted && planProductfromgroup.controls.ownershipType.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted && planProductfromgroup.controls.ownershipType.errors
                    "
                    class="errorWrap text-danger"
                  >
                    <div
                      *ngIf="
                        planProductSubmitted &&
                        planProductfromgroup.controls.ownershipType.errors.required
                      "
                      class="error text-danger"
                    >
                      Owership Type is required.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="revisedChargeFlag">
              <div class="form-group">
                <label>Revised Charge</label>
                <input
                  [ngClass]="{
                    'is-invalid':
                      planProductSubmitted && planProductfromgroup.controls.revisedCharge.errors
                  }"
                  [(ngModel)]="revicedAmount"
                  class="form-control"
                  formControlName="revisedCharge"
                  min="1"
                  type="number"
                  (keypress)="revicedChargeValidation($event)"
                />
                <div
                  *ngIf="planProductSubmitted && planProductfromgroup.controls.revisedCharge.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.revisedCharge.errors.required
                    "
                    class="error text-danger"
                  >
                    Revised Charge is required.
                  </div>
                  <div
                    *ngIf="
                      planProductSubmitted &&
                      planProductfromgroup.controls.revisedCharge.errors.minlength
                    "
                    class="error text-danger"
                  >
                    Minimum 0 Allowed.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productChargeFlag">
              <div class="form-group">
                <label>Product Charge</label>
                <input
                  [value]="chargeAmount"
                  class="form-control"
                  type="text"
                  required
                  value=""
                  readonly
                />
              </div>
            </div>
            <!-- </div>
                <div class="row"> -->
            <!-- <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group">
                        <button
                          (click)="onAddProductField()"
                          class="btn btn-primary"
                          id="addAtt"
                          style="object-fit: cover; padding: 5px 8px"
                        >
                          <i aria-hidden="true" class="fa fa-plus-square"></i>
                          Add
                        </button>
                      </div>
                    </div> -->
          </div>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
              <div class="form-group">
                <button
                  (click)="onAddProductField()"
                  class="btn btn-primary"
                  id="addAtt"
                  style="object-fit: cover; padding: 5px 8px"
                  [disabled]="!this.planProductfromgroup.valid"
                >
                  <i aria-hidden="true" class="fa fa-plus-square"></i>
                  Add
                </button>
              </div>
            </div>
            <div class="error text-danger" *ngIf="this.showQtyError">
              {{ this.qtyErroMsg }}
            </div>
          </div>
        </div>

        <div style="margin-top: 15px">
          <table class="table coa-table">
            <thead>
              <tr>
                <th style="text-align: center">Name</th>
                <th style="text-align: center">Product Category</th>
                <th style="text-align: center">Product</th>
                <th style="text-align: center">Product Type</th>
                <th style="text-align: center">Quantity</th>
                <th style="text-align: center">Revised Charge</th>
                <th style="text-align: center">Ownership Type</th>
                <th style="text-align: right; padding: 8px" *ngIf="!ifLeadQuickInput">Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let row of planProductMappingFromArray.controls
                    | paginate
                      : {
                          id: 'planProductFromArrayData',
                          itemsPerPage: planProductMappingItemsPerPage,
                          currentPage: currentPagePlanProductMapping,
                          totalItems: planProductMappingTotalRecords
                        };
                  let index = index
                "
              >
                <td style="padding-left: 8px">
                  <div class="form-group">
                    <p-dropdown
                      [options]="this.commondropdownService.activeProductList"
                      filter="true"
                      filterBy="name"
                      [formControl]="row.get('productId')"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a Product"
                      [disabled]="true"
                    ></p-dropdown>
                    <!-- <input
                      class="form-control"
                      [formControl]="row.get('name')"
                      placeholder="Enter Name"
                      type="text"
                      readonly
                    /> -->
                  </div>
                </td>
                <td style="padding-left: 8px">
                  <p-dropdown
                    [options]="commondropdownService.productCategoryList"
                    filter="true"
                    filterBy="name"
                    [formControl]="row.get('productCategoryId')"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Product Category"
                    [disabled]="true"
                  ></p-dropdown>
                </td>
                <td style="padding-left: 8px">
                  <p-dropdown
                    [options]="allActiveProduct"
                    filter="true"
                    filterBy="name"
                    [formControl]="row.get('productId')"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Product"
                    [disabled]="true"
                  ></p-dropdown>
                </td>
                <td>
                  <p-dropdown
                    [options]="productType"
                    filter="true"
                    filterBy="label"
                    [formControl]="row.get('product_type')"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Select a Product Type"
                    [disabled]="true"
                  ></p-dropdown>
                </td>
                <td>
                  <input
                    class="form-control"
                    [formControl]="row.get('productQuantity')"
                    min="1"
                    type="number"
                    readonly
                  />
                </td>
                <td>
                  <input
                    [formControl]="row.get('revisedCharge')"
                    class="form-control"
                    min="1"
                    placeholder="Enter Revised Charge"
                    type="number"
                    readonly
                  />
                </td>
                <td>
                  <p-dropdown
                    [options]="ownershipType"
                    filter="true"
                    filterBy="label"
                    [formControl]="row.get('ownershipType')"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Select a Ownership Type"
                    [disabled]="true"
                  ></p-dropdown>
                </td>

                <td style="text-align: right" *ngIf="!ifLeadQuickInput">
                  <button
                    (click)="deleteConfirmonPlanProductField(index, row.get('id').value)"
                    class="curson_pointer approve-btn"
                    id="deleteAtt"
                    [disabled]="this.ifPlanEditInput && isPlanEdit"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="pagination_Dropdown">
            <pagination-controls
              (pageChange)="pageChangePlanProductData($event)"
              directionLinks="true"
              id="planProductFromArrayData"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            ></pagination-controls>
          </div>
        </div>
      </div>
    </fieldset>

    <!-- CasMapping -->
    <fieldset *ngIf="ifCasMapping">
      <legend>Cas Mapping</legend>
      <div class="boxWhite">
        <div [formGroup]="planCasMappingFromGroup" class="row">
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <p-dropdown
              (onChange)="getAllCASPackage($event.value)"
              [options]="casmasterData"
              filter="true"
              filterBy="casname"
              formControlName="casId"
              optionLabel="casname"
              optionValue="id"
              placeholder="Select a CAS"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="planCasMappingSubmitted && planCasMappingFromGroup.controls.casId.errors"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">CAS is required.</div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <p-dropdown
              [options]="casPackageData"
              filter="true"
              filterBy="packageName"
              formControlName="packageId"
              optionLabel="packageName"
              optionValue="packageId"
              placeholder="Select a CAS Package"
            ></p-dropdown>
            <div></div>
            <div
              *ngIf="planCasMappingSubmitted && planCasMappingFromGroup.controls.packageId.errors"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">CAS Package is required.</div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <button
              (click)="onAddCasMappingField(this.planCasMappingFromGroup.controls.casId.value)"
              class="btn btn-primary"
              id="addAtt"
              style="object-fit: cover; padding: 5px 8px"
            >
              <i aria-hidden="true" class="fa fa-plus-square"></i>
              Add
            </button>
          </div>
        </div>
        <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
          <table class="table coa-table">
            <thead>
              <tr>
                <th style="text-align: center">CAS</th>
                <th style="text-align: center">CAS Package</th>
                <th style="text-align: center; padding: 8px">Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of planCasMappingFromArray.controls; index as index">
                <td style="padding-left: 8px">
                  <p-dropdown
                    [disabled]="true"
                    [options]="casmasterData"
                    filter="true"
                    filterBy="casname"
                    optionLabel="casname"
                    optionValue="id"
                    placeholder="Select a CAS"
                    [formControl]="row.get('casId')"
                  ></p-dropdown>

                  <div></div>
                </td>
                <td style="padding-left: 8px">
                  <p-dropdown
                    [options]="casPackegeAllData"
                    filter="true"
                    filterBy="packageName"
                    formControlName="packageId"
                    optionLabel="packageName"
                    optionValue="packageId"
                    placeholder="Select a CAS Package"
                    [disabled]="true"
                    [formControl]="row.get('packageId')"
                  ></p-dropdown>
                  <div></div>
                </td>
                <td style="text-align: center">
                  <button
                    (click)="deleteConfirmonCasMappingField(index)"
                    id="deleteAtt"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <br />
      </div>
    </fieldset>

    <fieldset *ngIf="fieldsArr.length > 0">
      <legend>Service Parameters</legend>
      <div class="boxWhite" [formGroup]="customerGroupForm">
        <div class="row" style="margin: 2px; display: flex; flex-wrap: wrap">
          <ng-container *ngFor="let item of fieldsArr; let i = index">
            <ng-container
              *ngIf="
                item.fieldType != 'select' &&
                item.fieldType != 'multi-select' &&
                item.fieldType != 'checkbox' &&
                item.fieldType != 'object' &&
                item.fieldType != 'objectList'
              "
            >
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                <div class="form-group">
                  <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                  <input
                    class="form-control"
                    name="{{ item.fieldname }}"
                    id="{{ item.fieldname }}"
                    placeholder="Enter {{ item.fieldname }}"
                    type="{{ item.fieldType }}"
                    [formControlName]="item.fieldname"
                    [(ngModel)]="item.defaultValue"
                  />
                  <div
                    *ngIf="
                      customerGroupForm.controls[item.fieldname].errors &&
                      (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                    "
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">{{ item.name }} is required.</div>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="item.fieldType == 'checkbox'">
              <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                <input
                  class="form-check-input"
                  name="{{ item.fieldname }}"
                  id="{{ item.fieldname }}"
                  placeholder="Enter {{ item.fieldname }}"
                  type="checkbox"
                  [formControlName]="item.fieldname"
                  style="width: 20px; height: 20px; margin-left: 15px; margin-top: 50px"
                  [(ngModel)]="item.defaultValue"
                />
                <div
                  *ngIf="
                    customerGroupForm.controls[item.fieldname].errors &&
                    (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                  "
                  class="errorWrap text-danger"
                >
                  <div class="error text-danger">{{ item.name }} is required.</div>
                </div>
              </div>
            </ng-container>

            <ng-container *ngIf="item.fieldType == 'select' && !item.isdependant">
              <ng-container *ngFor="let list of optionList">
                <ng-container
                  *ngIf="list.fieldname == item.fieldname && item.backendrequired == 'displayName'"
                >
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                    <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                    <div class="dropdown-container">
                      <p-dropdown
                        [options]="list.options.dataList"
                        optionValue="displayName"
                        optionLabel="displayName"
                        filter="true"
                        filterBy="displayName"
                        placeholder="Select a {{ list.fieldname }}"
                        [formControlName]="item.fieldname"
                        name="{{ i + 1 }}module"
                        [(ngModel)]="item.defaultValue"
                      >
                      </p-dropdown>
                    </div>
                    <div
                      *ngIf="
                        customerGroupForm.controls[item.fieldname].errors &&
                        (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                      "
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">{{ item.name }} is required.</div>
                    </div>
                  </div>
                </ng-container>

                <ng-container
                  *ngIf="list.fieldname == item.fieldname && item.backendrequired == 'displayId'"
                >
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                    <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                    <div class="dropdown-container">
                      <p-dropdown
                        [options]="list.options.dataList"
                        optionValue="displayId"
                        optionLabel="displayName"
                        filter="true"
                        filterBy="displayName"
                        placeholder="Select a {{ list.fieldname }}"
                        [formControlName]="item.fieldname"
                        name="{{ i + 1 }}module"
                        [(ngModel)]="item.defaultValue"
                      >
                      </p-dropdown>
                      <div
                        *ngIf="
                          customerGroupForm.controls[item.fieldname].errors &&
                          (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                        "
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">{{ item.name }} is required.</div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </ng-container>
            </ng-container>
            <ng-container *ngIf="item.fieldType == 'multi-select'">
              <ng-container *ngFor="let list of multiOptionList">
                <ng-container
                  *ngIf="list.fieldname == item.fieldname && item.backendrequired == 'displayName'"
                >
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                    <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                    <label>{{ item.name }}<span *ngIf="item.mandatoryFlag"> * </span></label>
                    <div class="multiselect">
                      <p-multiSelect
                        [options]="list.options.dataList"
                        optionValue="displayName"
                        defaultLabel="Select a {{ item.fieldname }}"
                        optionLabel="displayName"
                        [formControlName]="item.fieldname"
                      ></p-multiSelect>
                      <div
                        *ngIf="
                          customerGroupForm.controls[item.fieldname].errors &&
                          (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                        "
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">{{ item.name }} is required.</div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <ng-container
                  *ngIf="list.fieldname == item.fieldname && item.backendrequired == 'displayId'"
                >
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 marginbottm2">
                    <label>{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label>
                    <div class="multiselect">
                      <p-multiSelect
                        [options]="list.options.dataList"
                        optionValue="displayId"
                        defaultLabel="Select a {{ item.fieldname }}"
                        optionLabel="displayName"
                        [formControlName]="item.fieldname"
                      ></p-multiSelect>
                      <div
                        *ngIf="
                          customerGroupForm.controls[item.fieldname].errors &&
                          (customerGroupForm.controls[item.fieldname].touched || templateSubmitted)
                        "
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">{{ item.name }} is required.</div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <br />
        </div>
      </div>
    </fieldset>

      <!--    Charge Details    -->
      <fieldset style="margin-bottom: 2rem">
        <legend>Charge Details</legend>
        <div class="boxWhite">
          <form [formGroup]="chargeGroupForm" *ngIf="!ifLeadQuickInput">
          <div class="row">
            <div class="col-lg-4 col-md-4 col-12">
              <label>Charge Name*</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Charge Name"
                formControlName="name"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="chargeSubmitted && chargeGroupForm.controls.name.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.name.errors.required"
                >
                  Charge Name is required.
                </div>
              </div>
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-12">
              <div>
                <label>Charge Category*</label>
                <p-dropdown
                  [options]="chargeCategoryList"
                  optionValue="value"
                  optionLabel="text"
                  filter="true"
                  filterBy="text"
                  placeholder="Select Charge Category"
                  formControlName="chargecategory"
                  (onChange)="getSelChargeCategory($event)"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.chargecategory.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="
                      chargeSubmitted && chargeGroupForm.controls.chargecategory.errors.required
                    "
                  >
                    Charge Category is required.
                  </div>
                </div>
              </div>
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-12">
              <div>
                <label>Charge Type*</label>
                <p-dropdown
                  [options]="chargeType"
                  optionValue="value"
                  optionLabel="text"
                  filter="true"
                  filterBy="text"
                  placeholder="Select a Charge Type"
                  formControlName="chargetype"
                  [disabled]="isChargeEdit"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.chargetype.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="chargeSubmitted && chargeGroupForm.controls.chargetype.errors.required"
                  >
                    Charge Type is required.
                  </div>
                </div>
              </div>
              <br />
            </div>
          </div>

          <div class="row">
            <div
              class="col-lg-4 col-md-4 col-12"
              *ngIf="chargeGroupForm.controls.serviceid.enabled"
            >
              <div>
                <label>Service*</label>

                <p-multiSelect
                  id="roles"
                  [options]="this.commondropdownService.planserviceData"
                  placeholder="Select a service"
                  formControlName="serviceid"
                  optionLabel="name"
                  optionValue="id"
                  filter="true"
                  filterBy="name"
                ></p-multiSelect>
                <div
                  class="errorWrap text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.serviceid.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="chargeSubmitted && chargeGroupForm.controls.serviceid.errors.required"
                  >
                    Service is required.
                  </div>
                </div>
              </div>
              <br />
            </div>

            <div class="col-lg-4 col-md-4 col-12" *ngIf="chargeGroupForm.controls.status.enabled">
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="chargeSubmitted && chargeGroupForm.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.status.errors.required"
                >
                  Status is required.
                </div>
              </div>
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <label>Ledger ID</label>
              <input
                id="ledgerId"
                type="text"
                class="form-control"
                placeholder="Enter Ledger ID"
                formControlName="ledgerId"
              />
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-12">
              <label>Actual Price*</label>
              <input
                id="actualprice"
                type="number"
                min="1"
                class="form-control"
                placeholder="Enter Actual Price"
                formControlName="actualprice"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="chargeSubmitted && chargeGroupForm.controls.actualprice.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.actualprice.errors.required"
                >
                  Actual Price is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.actualprice.errors.pattern"
                >
                  Only numeric charcter allowed.
                </div>
                <div class="error text-danger" *ngIf="chargeValueSentence">
                  {{ chargeValueSentence }}
                </div>
              </div>
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-12">
              <label>Tax*</label>
              <div>
                <p-dropdown
                  [options]="taxListData"
                  optionValue="id"
                  optionLabel="name"
                  filter="true"
                  filterBy="name"
                  placeholder="Select a Tax"
                  formControlName="taxid"
                  (onChange)="taxRang($event)"
                  [disabled]="isChargeEdit"
                  [ngClass]="{
                    'is-invalid': submitted && chargeGroupForm.controls.taxid.errors
                  }"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1">
                        {{ data.name }}
                        <span *ngFor="let row of data.tieredList">
                          ( {{ row.name }} {{ row.rate }}%)</span
                        >
                      </span>
                    </div>
                  </ng-template>
                </p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="chargeSubmitted && chargeGroupForm.controls.taxid.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.taxid.errors.required"
                >
                  Tax is required.
                </div>
              </div>
              <br />
            </div>
          </div>
          <div class="row">
            <div
              *ngIf="chargeGroupForm.controls.billingCycle.enabled"
              class="col-lg-4 col-md-4 col-12"
            >
              <label>Billing Cycle*</label>
              <p-dropdown
                [ngClass]="{
                  'is-invalid': chargeSubmitted && chargeGroupForm.controls.billingCycle.errors
                }"
                [options]="billingCycle"
                filter="true"
                filterBy="label"
                formControlName="billingCycle"
                optionLabel="label"
                optionValue="label"
                placeholder="Select a Billing Cycle"
              ></p-dropdown>

              <div
                *ngIf="chargeSubmitted && chargeGroupForm.controls.billingCycle.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Billing Cycle is required.</div>
              </div>
              <br />
            </div>

            <!-- <div
              class="col-lg-4 col-md-4 col-12"
              *ngIf="
                this.chargeGroupForm.value.chargetype == 'ADVANCE' ||
                this.chargeGroupForm.value.chargetype == 'RECURRING'
              "
            >
              <label>Royalty Payable*</label>
              <div>
                <p-dropdown
                  [options]="royaltyPayableData"
                  optionValue="value"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Royalty Payable"
                  formControlName="royalty_payable"
                  [disabled]="isChargeEdit"
                  [ngClass]="{
                    'is-invalid':
                      submitted && chargeGroupForm.controls.royalty_payable.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && chargeGroupForm.controls.royalty_payable.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="
                    submitted && chargeGroupForm.controls.royalty_payable.errors.required
                  "
                >
                  Royalty Payable is required.
                </div>
              </div>
            </div> -->

            <div class="col-lg-4 col-md-4 col-12">
              <label>Charge Description*</label>
              <textarea
                class="form-control"
                placeholder="Enter Charge Description"
                rows="3"
                formControlName="desc"
                name="desc"
                [ngClass]="{
                  'is-invalid': chargeSubmitted && chargeGroupForm.controls.desc.errors
                }"
              ></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="chargeSubmitted && chargeGroupForm.controls.desc.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.desc.errors.required"
                >
                  Charge Description is required.
                </div>

                <div
                  class="error text-danger"
                  *ngIf="chargeSubmitted && chargeGroupForm.controls.desc.errors.pattern"
                >
                  Maximum 225 charecter required.
                </div>
              </div>
              <br />
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6">
              <label style="display: block">&nbsp;</label>
              <button
                id="addAtt"
                style="object-fit: cover; padding: 5px 8px"
                class="btn btn-primary"
                (click)="onAddChargeArray()"
              >
                <i class="fa fa-plus-square" aria-hidden="true"></i>
                Add
              </button>
            </div>
          </div>

        </form>
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <table class="table coa-table" style="margin-top: 10px">
                <thead>
                  <tr>
                    <th style="text-align: center; width: 15%">Name*</th>
                    <th style="text-align: center; width: 10%">Category*</th>
                    <th style="text-align: center; width: 10%">Type*</th>
                    <th style="text-align: center; width: 10%; padding: 8px">Ledger Id</th>
                    <th style="text-align: center; width: 10%; padding: 8px">Billing Cycle</th>
                    <th style="text-align: center; width: 10%; padding: 8px">Actual Price</th>
                    <th style="text-align: center; width: 10%; padding: 8px">Tax</th>
                    <th style="text-align: center; width: 15%; padding: 8px">Description</th>
                    <th style="text-align: center; width: 10%; padding: 8px" *ngIf="!ifLeadQuickInput">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let row of chargeGroupFormArray.controls
                        | paginate
                          : {
                              id: 'chargeData',
                              itemsPerPage: chargeIemsPerPage,
                              currentPage: currentPageChargeData,
                              totalItems: chargeDatatotalRecords
                            };
                      let index = index
                    "
                  >
                    <td style="padding-left: 8px">
                      <input
                        id="name"
                        type="text"
                        class="form-control"
                        placeholder="Enter Charge Name"
                        [formControl]="row.get('name')"
                        disabled
                      />
                    </td>
                    <td>
                      <p-dropdown
                        [options]="chargeCategoryList"
                        optionValue="value"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select Charge Category"
                        [formControl]="row.get('chargecategory')"
                        [disabled]="true"
                      ></p-dropdown>
                    </td>
                    <td>
                      <p-dropdown
                        [options]="mainChargeList"
                        optionValue="value"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a Charge Type"
                        [formControl]="row.get('chargetype')"
                        [disabled]="true"
                      ></p-dropdown>
                    </td>
                    <td>
                      <input
                        id="ledgerId"
                        type="text"
                        class="form-control"
                        placeholder="Enter Ledger ID"
                        [formControl]="row.get('ledgerId')"
                        disabled
                      />
                    </td>
                    <td>
                      <p-dropdown
                        [options]="billingCycle"
                        filter="true"
                        filterBy="label"
                        [formControl]="row?.get('billingCycle')"
                        optionLabel="label"
                        optionValue="label"
                        placeholder="Select a Billing Cycle"
                        [disabled]="true"
                      ></p-dropdown>
                    </td>
                    <td>
                      <input
                        id="actualprice"
                        type="number"
                        min="1"
                        class="form-control"
                        placeholder="Enter Actual Price"
                        [formControl]="row.get('actualprice')"
                      />
                    </td>
                    <td>
                      <p-dropdown
                        [options]="taxListData"
                        optionValue="id"
                        optionLabel="name"
                        filter="true"
                        filterBy="name"
                        placeholder="Select a Tax"
                        [formControl]="row.get('taxid')"
                        [disabled]="true"
                      >
                        <ng-template let-data pTemplate="item">
                          <div class="item-drop1">
                            <span class="item-value1">
                              {{ data.name }}
                              <span *ngFor="let row of data.tieredList">
                                ( {{ row.name }} {{ row.rate }}%)</span
                              >
                            </span>
                          </div>
                        </ng-template>
                      </p-dropdown>
                    </td>
                    <td>
                      <textarea
                        class="form-control"
                        placeholder="Enter Charge Description"
                        rows="3"
                        disabled
                        name="desc"
                        [formControl]="row.get('desc')"
                      ></textarea>
                    </td>

                    <td style="text-align: right" *ngIf="!ifLeadQuickInput">
                      <a
                        id="deleteAtt"
                        class="curson_pointer"
                        (click)="deleteConfirmonChargeField(index, row.get('id').value)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="row">
                <div class="col-md-12">
                  <pagination-controls
                    id="chargeData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedCharge($event)"
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </div>
        </div>
      </fieldset>

    <!-- <button type="submit" class="btn btn-primary" style="margin-top: 1rem">Submit</button> -->
    <div class="addUpdateBtn addeditbtntop" style="margin-top: 10px">
      <!-- <button
                (click)="addEditPostPaidPlan()"
                *ngIf="
                  loginService.hasOperationPermission(
                    AclClassConstants.ACL_POSTPAID_PLAN,
                    AclConstants.OPERATION_POSTPAID_PLAN_ADD,
                    AclConstants.OPERATION_POSTPAID_PLAN_ALL
                  ) && !isPlanEdit
                "
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Add Plan
              </button>
              <button
                (click)="addEditPostPaidPlan(viewPlanListData.id)"
                *ngIf="
                  loginService.hasOperationPermission(
                    AclClassConstants.ACL_POSTPAID_PLAN,
                    AclConstants.OPERATION_POSTPAID_PLAN_EDIT,
                    AclConstants.OPERATION_POSTPAID_PLAN_ALL
                  ) && isPlanEdit
                "
                class="btn btn-primary"
                id="submit"
                type="submit"
              >
                <i class="fa fa-check-circle"></i>
                Update Plan
              </button> -->

      <button (click)="savePlanDto()" class="btn btn-primary" type="submit">
        {{ isServiceEdit ? "Update" : "Save" }}
      </button>
    </div>
  </form>
</div>
