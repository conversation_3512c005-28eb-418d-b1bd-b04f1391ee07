<p-dialog
  header="Withdrawal Amount"
  [(visible)]="displayDialogWithDraw"
  [style]="{ width: '65%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="panel-body table-responsive">
      <div class="row">
        <form class="form-auth-small" [formGroup]="manageBalanceGroupForm">
          <!--   Manage Balance   -->
          <fieldset style="margin-top: 0px">
            <legend>Basic Information</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                  <label>Operation Mode *</label>
                  <p-dropdown
                    [options]="BalanceOpertation"
                    optionValue="value"
                    optionLabel="label"
                    [filter]="true"
                    filterBy="label"
                    placeholder="Select a Balance Opertation"
                    formControlName="operation"
                    [disabled]="true"
                    [ngClass]="{
                      'is-invalid': submitted && manageBalanceGroupForm.controls.operation.errors
                    }"
                  ></p-dropdown>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="submitted && manageBalanceGroupForm.controls.operation.errors"
                  >
                    <div
                      class="error text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.operation.errors.required"
                    >
                      Please select Balance Opertation.
                    </div>
                  </div>
                  <br />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                  <div *ngIf="this.ifwithdrawalCommision">
                    <label>Payment Mode *</label>
                    <div>
                      <p-dropdown
                        [options]="paymentmode"
                        optionValue="label"
                        optionLabel="label"
                        [filter]="true"
                        filterBy="label"
                        placeholder="Select a Mode"
                        formControlName="mode"
                        (onChange)="getOpetationMode($event)"
                        [ngClass]="{
                          'is-invalid': submitted && manageBalanceGroupForm.controls.mode.errors
                        }"
                      ></p-dropdown>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.mode.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.mode.errors.required"
                      >
                        Please select Balance Mode.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- withdrawal Commision  -->
          <div *ngIf="this.ifwithdrawalCommision">
            <fieldset>
              <legend>Withdrawal Amount</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>Withdrawal Amount*</label>
                    <input
                      id="commission"
                      type="text"
                      class="form-control"
                      placeholder="Enter Commission"
                      formControlName="commission"
                      readonly
                    />
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>Withdrawal Amount *</label>
                    <input
                      (keyup)="onKeyWithdrawalAmount()"
                      id="withdrawalAmount"
                      type="text"
                      class="form-control"
                      placeholder="Enter Withdrawal Amount "
                      formControlName="withdrawalAmount"
                      [ngClass]="{
                        'is-invalid':
                          submitted && manageBalanceGroupForm.controls.withdrawalAmount.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.withdrawalAmount.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          manageBalanceGroupForm.controls.withdrawalAmount.errors.required
                        "
                      >
                        Withdrawal Amount is required.
                      </div>
                    </div>
                    <br />
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>Remaining Wallet Amount *</label>
                    <input
                      id="ReamainingCommision"
                      type="text"
                      class="form-control"
                      placeholder="Enter Reamaining Commision "
                      formControlName="ReamainingCommision"
                      [ngClass]="{
                        'is-invalid':
                          submitted && manageBalanceGroupForm.controls.ReamainingCommision.errors
                      }"
                      readonly
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        submitted && manageBalanceGroupForm.controls.ReamainingCommision.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          manageBalanceGroupForm.controls.ReamainingCommision.errors.required
                        "
                      >
                        Reamaining Commision Amount is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row" *ngIf="ifWithdrawalCash">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div>
                      <label>Reference No *</label>
                      <input
                        id="referenceNo"
                        type="text"
                        class="form-control"
                        placeholder="Enter Reference No"
                        formControlName="referenceNo"
                        [ngClass]="{
                          'is-invalid':
                            submitted && manageBalanceGroupForm.controls.referenceNo.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.referenceNo.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && manageBalanceGroupForm.controls.referenceNo.errors.required
                          "
                        >
                          Reference No is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>remark *</label>
                    <textarea
                      id="remarks"
                      type="text"
                      class="form-control"
                      placeholder="Enter Remarks"
                      formControlName="remarks"
                      [ngClass]="{
                        'is-invalid': submitted && manageBalanceGroupForm.controls.remarks.errors
                      }"
                    ></textarea>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.remarks.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.remarks.errors.required"
                      >
                        remark is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row" *ngIf="ifWithdrawalOnlineMode">
                  <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                        <label>Payment Date *</label>
                        <input
                          id="paymentdate"
                          type="date"
                          class="form-control"
                          placeholder="Enter Payment Date"
                          formControlName="paymentdate"
                          [ngClass]="{
                            'is-invalid':
                              submitted &&
                              manageBalanceGroupForm.controls.paymentdate.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="
                            submitted &&
                            manageBalanceGroupForm.controls.paymentdate.errors
                          "
                        >
                          <div
                            class="error text-danger"
                            *ngIf="
                              submitted &&
                              manageBalanceGroupForm.controls.paymentdate.errors
                                .required
                            "
                          >
                            Payment Date is required.
                          </div>
                        </div>
                        <br />
                      </div> -->
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div>
                      <label>Bank *</label>

                      <p-dropdown
                        [options]="bankDataList"
                        optionValue="bankname"
                        optionLabel="bankname"
                        [filter]="true"
                        filterBy="bankname"
                        placeholder="Select a Bank"
                        formControlName="bank"
                        [ngClass]="{
                          'is-invalid': submitted && manageBalanceGroupForm.controls.bank.errors
                        }"
                      >
                        <ng-template let-data pTemplate="item">
                          <div class="item-drop1">
                            <span class="item-value1">
                              {{ data.bankname }} ( {{ data.accountnum }} )
                            </span>
                          </div>
                        </ng-template>
                      </p-dropdown>

                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.bank.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && manageBalanceGroupForm.controls.bank.errors.required"
                        >
                          Bank No is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>Branch *</label>
                    <input
                      id="branch"
                      type="text"
                      class="form-control"
                      placeholder="Enter Branch Name"
                      formControlName="branch"
                      [ngClass]="{
                        'is-invalid': submitted && manageBalanceGroupForm.controls.branch.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.branch.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.branch.errors.required"
                      >
                        Branch Name is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
                <div class="row" *ngIf="ifWithdrawalOnlineMode">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div>
                      <label>Payment Reference No *</label>
                      <input
                        id="referenceNo"
                        type="text"
                        class="form-control"
                        placeholder="Enter Reference No"
                        formControlName="referenceNo"
                        [ngClass]="{
                          'is-invalid':
                            submitted && manageBalanceGroupForm.controls.referenceNo.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.referenceNo.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && manageBalanceGroupForm.controls.referenceNo.errors.required
                          "
                        >
                          Reference No is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <label>remark</label>
                    <textarea
                      id="remark"
                      type="text"
                      class="form-control"
                      placeholder="Enter Remark"
                      formControlName="remarks"
                      [ngClass]="{
                        'is-invalid': submitted && manageBalanceGroupForm.controls.remarks.errors
                      }"
                    ></textarea>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && manageBalanceGroupForm.controls.remarks.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && manageBalanceGroupForm.controls.remarks.errors.required"
                      >
                        remark is required.
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </form>
        <fieldset>
          <legend>Remaining Payment Details</legend>
          <div class="boxWhite">
            <div class="row">
              <div class="col-md-12">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>
                            <input
                              (change)="checkUncheckAll()"
                              [(ngModel)]="this.masterSelected"
                              name="master-checkbox"
                              type="checkbox"
                            />
                          </th>
                          <th>Reference No.</th>
                          <th>Payment Mode</th>
                          <th>Remaining Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let data of paymentListData
                              | paginate
                                : {
                                    id: 'partnerPaymentpageData',
                                    itemsPerPage: paymentListdataitemsPerPage,
                                    currentPage: currentPagePaymentListdata,
                                    totalItems: paymentListdatatotalRecords
                                  };
                            index as i
                          "
                        >
                          <td>
                            <input
                              (change)="isAllSelected()"
                              [(ngModel)]="data.isSelected"
                              name="master-checkbox"
                              type="checkbox"
                            />
                          </td>
                          <td>{{ data.creditdocumentno }}</td>
                          <td>{{ data.paymode }}</td>
                          <td>{{ data.remainingAmount }}</td>
                        </tr>
                      </tbody>
                    </table>
                    <div style="display: flex">
                      <pagination-controls
                        (pageChange)="pageChangedForPayments($event)"
                        [directionLinks]="true"
                        id="partnerPaymentpageData"
                        [maxSize]="10"
                        nextLabel=""
                        previousLabel=""
                      ></pagination-controls>
                      <div id="itemPerPageDropdown">
                        <p-dropdown
                          (onChange)="totalItemPerPageForPayments($event)"
                          [options]="pageLimitOptions"
                          optionLabel="value"
                          optionValue="value"
                        ></p-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      (click)="submitBalance()"
      [disabled]="!manageBalanceGroupForm.valid"
      id="submit"
    >
      <i class="fa fa-check-circle"></i>
      Withdrawal Amount
    </button>
    <button
      type="button"
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeSelectStafff()"
    >
      Close
    </button>
  </div>
</p-dialog>
