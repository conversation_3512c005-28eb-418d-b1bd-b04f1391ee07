<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custDetails.title }}
            {{ custDetails.firstname }}
            {{ custDetails.lastname }} Current Plan
          </h3>
        </div>
        <div class="right">
          <button class="btn refreshbtn" type="reset" (click)="refreshChangePlan()">
            <i class="fa fa-refresh"></i>
          </button>
          <button
            aria-controls="currenPresCustPlan"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#currenPresCustPlan"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="currenPresCustPlan">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th width="10%">Service Name</th>
                    <th width="15%">Serial No</th>
                    <th width="10%">Plan Name</th>
                    <th width="10%">Plan Group</th>
                    <th width="8%">Validity</th>
                    <th width="10%">Plan Status</th>
                    <th width="10%">Start Date</th>
                    <th width="10%">Service Expiry Date</th>
                    <th width="10%">Billing End Date</th>
                    <th width="10%">Remaining Days</th>
                    <th width="10%">Promise To Pay Taken</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let plan of custServiceData
                        | paginate
                          : {
                              id: 'custCurrentPlanListData',
                              itemsPerPage: customerCurrentPlanListdataitemsPerPage,
                              currentPage: currentPagecustomerCurrentPlanListdata,
                              totalItems: customerCurrentPlanListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ plan.service }}</td>
                    <td>
                      <span
                        class="curson_pointer"
                        style="color: #f7b206"
                        (click)="openPlanConnectionModal(plan)"
                      >
                        {{ getSerialNumber(plan) !== "" ? getSerialNumber(plan) : "NA" }}
                      </span>
                    </td>
                    <td
                      class="curson_pointer"
                      style="color: #f7b206"
                      (click)="quotaPlanDetailsModel('quotaModalOpen', customerId, plan)"
                    >
                      {{ plan.planName }}
                    </td>
                    <td>{{ plan.planGroupName }}</td>
                    <td>
                      <div *ngIf="custType === 'Prepaid'">
                        <span> {{ plan.validity }} </span>
                      </div>
                      <div *ngIf="custType !== 'Prepaid'">
                        <span> N/A </span>
                      </div>
                    </td>
                    <td>
                      <span
                        [ngClass]="getStatusClass(plan.planstage, plan.custServMappingStatus)"
                        >{{ getStatus(plan.planstage, plan.custServMappingStatus) }}</span
                      >
                    </td>

                    <td>{{ plan.dbStartDate | date: "yyyy-MM-dd hh:mm a" }}</td>
                    <td>
                      <div *ngIf="custType === 'Prepaid'">
                        <span> {{ plan.dbEndDate | date: "yyyy-MM-dd hh:mm a" }} </span>
                      </div>
                      <div *ngIf="custType !== 'Prepaid'">
                        <span> N/A </span>
                      </div>
                    </td>
                    <td>
                      <div *ngIf="custType === 'Prepaid'">
                        <span> {{ plan.dbExpiryDate | date: "yyyy-MM-dd hh:mm a" }} </span>
                      </div>
                      <div *ngIf="custType !== 'Prepaid'">
                        <span> N/A </span>
                      </div>
                    </td>
                    <td>
                      <div *ngIf="custType === 'Prepaid'">
                        <span>
                          {{
                            this.findDurationFromStartDate(plan.dbStartDate, plan.dbEndDate)
                          }}</span
                        >
                      </div>
                      <div *ngIf="custType !== 'Prepaid'">
                        <span> N/A </span>
                      </div>
                    </td>
                    <td>
                      <div *ngIf="plan.isPromiseToPayTaken == false">
                        <span class="badge badge-secondary"> No </span>
                      </div>
                      <div *ngIf="plan.isPromiseToPayTaken == true">
                        <span
                          (click)="
                            promiseToPayDetailsClick(
                              'promiseToPayDataModal',
                              plan.promiseToPayStartDate,
                              plan.promiseToPayEndDate,
                              plan.promiseToPayDays
                            )
                          "
                          class="badge badge-success curson_pointer"
                        >
                          Yes
                        </span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedcustomerCurrentPlanListData($event)"
                  directionLinks="true"
                  id="custCurrentPlanListData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalCurrentPlanItemPerPage($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Change Plan Panel -->
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <!-- <button
            (click)="backButton.emit(custDetails.id)"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button> -->
          <h3 class="panel-title">
            {{ custDetails.title }}
            {{ custDetails.firstname }}
            {{ custDetails.lastname }} Change Plan
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="changePresCustPlan"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#changePresCustPlan"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="changePresCustPlan">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-6 col-xs-12">
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                  <label>Change Plan Type</label>
                  <p-dropdown
                    [ngClass]="{
                      'is-invalid': changePlanSubmitted && !changePlanTypeValue
                    }"
                    (onChange)="onChangePlanType($event)"
                    [(ngModel)]="changePlanTypeSelection"
                    [options]="changePlanType"
                    filter="true"
                    filterBy="text"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select a Plan Change Type"
                  ></p-dropdown>
                </div>

                <!-- <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="!isVasPlan">
                  <label>New Vas Pack</label>
                  <p-dropdown
                    [(ngModel)]="newVasPackId"
                    [options]="planAllData"
                    filter="true"
                    filterBy="name"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a New Vas Pack"
                    (onChange)="getPlanFromVasId($event)"
                  ></p-dropdown>
                </div> -->
              </div>
              <ng-container *ngIf="isVasPlan">
                <p-card>
                  <ng-template pTemplate="content">
                    <div class="row" *ngIf="changePlanTypeSelection !== 'Addon'">
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2">
                        <p-dropdown
                          [(ngModel)]="selectedPlanCategory"
                          (click)="$event.stopPropagation()"
                          (onChange)="selectPlanCategory($event, -1, custDetails)"
                          [options]="planDetailsCategory"
                          [disabled]="
                            !changePlanTypeSelection ||
                            (this.custServiceData != null && this.custServiceData.length == 1)
                          "
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select Plan Category"
                        ></p-dropdown>
                      </div>
                      <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2">
                        <p-dropdown
                          [(ngModel)]="newPlanGroupId"
                          (click)="$event.stopPropagation()"
                          [style]="{ 'margin-left': '20px' }"
                          (onChange)="selectPlanGroup($event, -1)"
                          [options]="planGroupChanges"
                          [disabled]="
                            !changePlanTypeSelection ||
                            selectedPlanCategory === 'individual' ||
                            (this.custServiceData != null && this.custServiceData.length == 1)
                          "
                          filter="true"
                          filterBy="planGroupName"
                          optionLabel="planGroupName"
                          optionValue="planGroupId"
                          placeholder="Select a New Plan Group"
                        ></p-dropdown>
                      </div>

                      <div
                        class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2"
                        *ngIf="custType === 'Postpaid'"
                      >
                        <p-dropdown
                          [ngClass]="{
                            'is-invalid': changePlanSubmitted && !changePlanTypeValue
                          }"
                          [(ngModel)]="ChangePLanDateSelection"
                          [options]="dateType"
                          filter="true"
                          filterBy="text"
                          optionLabel="text"
                          optionValue="value"
                          placeholder="Select Date for Change Plan*"
                        ></p-dropdown>
                        <div
                          *ngIf="changePlanSubmitted && !ChangePLanDateSelection"
                          class="errorWrap text-danger"
                        >
                          <div class="error text-danger">Change Plan date is required.</div>
                        </div>
                      </div>

                      <div
                        *ngIf="changePlanTypeSelection == 'Renew'"
                        class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2"
                        style="display: flex"
                      >
                        <div>
                          <p-checkbox
                            binary="true"
                            [(ngModel)]="isAddCharge"
                            [disabled]="isPlanSelected(custDetails.id)"
                            (onChange)="onDirectChargeChange($event, custDetails.id)"
                            name="allChecked"
                          ></p-checkbox>
                          &nbsp;&nbsp;Add Direct Charge
                        </div>
                        <div style="width: 20%">
                          <button
                            type="button"
                            [disabled]="!isAddCharge"
                            (click)="openChargeDetails(custDetails.id)"
                            class="btn btn-primary"
                            style="
                              border-radius: 5px;
                              padding: 5px 10px;
                              line-height: 1.5;
                              margin-left: 10px;
                            "
                          >
                            <i _ngcontent-nrc-c2="" class="fa fa-eye"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <p-table
                      #dt
                      [value]="custServiceData"
                      [(selection)]="selectedChangePlan"
                      dataKey="planId"
                      styleClass="p-datatable-customers"
                      [rowHover]="true"
                      [showCurrentPageReport]="true"
                      [globalFilterFields]="['connection_no', 'service', 'planName', 'status']"
                    >
                      <ng-template pTemplate="header">
                        <tr>
                          <th style="width: 3rem"></th>
                          <th>Connection No.</th>
                          <th>Service Name</th>
                          <th *ngIf="this.custDetails.plangroupid">Plan Group</th>
                          <th>Current Plan Name</th>
                          <th *ngIf="changePlanTypeSelection === 'Changeplan'">Skip Quota</th>
                          <th>Select New Plan</th>
                          <th *ngIf="changePlanTypeSelection === 'Addon'">Start Date</th>
                          <th *ngIf="changePlanTypeSelection === 'Addon'">End Date</th>
                          <th *ngIf="changePlanTypeSelection === 'Addon'">renewalForBooster</th>
                        </tr>
                      </ng-template>
                      <ng-template pTemplate="body" let-i="rowIndex" let-customer>
                        <tr class="p-selectable-row">
                          <td
                            *ngIf="
                              changePlanTypeSelection === 'Addon' ||
                              selectedPlanCategory === 'individual'
                            "
                            style="width: 3rem"
                          >
                            <p-checkbox
                              [(ngModel)]="customer.changeFlag"
                              (onChange)="changePlanSelection($event, customer, i, false, -1)"
                              [pSelectableRow]="customer.customerServiceMappingId"
                              [disabled]="
                                !changePlanTypeSelection ||
                                customer.custServMappingStatus === 'Hold'
                              "
                            ></p-checkbox>
                          </td>
                          <td
                            *ngIf="
                              changePlanTypeSelection !== 'Addon' &&
                              selectedPlanCategory !== 'individual'
                            "
                            style="width: 3rem"
                          ></td>
                          <td>
                            {{ customer.connection_no }}
                          </td>
                          <td>
                            <span class="image-text">{{ customer.service }}</span>
                          </td>
                          <td *ngIf="this.custDetails.plangroupid">
                            {{ custServiceData[0]?.planGroupName }}
                          </td>
                          <td>
                            <span class="image-text">{{ customer.planName }}</span>
                          </td>
                          <td *ngIf="changePlanTypeSelection === 'Changeplan'">
                            <p-checkbox
                              binary="true"
                              class="checkbox-align"
                              [(ngModel)]="skipQuotaUpdate"
                              name="skipQuotaUpdate"
                            ></p-checkbox>
                          </td>
                          <td style="display: flex; flex-wrap: wrap; align-items: center">
                            <div style="flex: 1; width: 10%">
                              <button
                                type="button"
                                (click)="
                                  modalOpenDetails(
                                    customer.newPlanSelection,
                                    customer.connection_no,
                                    custDetails.id,
                                    selectedPlanCategory
                                  )
                                "
                                class="btn btn-primary"
                                [disabled]="!customer.newPlanSelection"
                                style="
                                  border-radius: 5px;
                                  padding: 5px 10px;
                                  line-height: 1.5;
                                  margin-left: 10px;
                                "
                              >
                                <i _ngcontent-nrc-c2="" class="fa fa-eye"></i>
                              </button>
                            </div>
                            <div
                              *ngIf="
                                changePlanTypeSelection !== 'Addon' &&
                                selectedPlanCategory !== 'individual'
                              "
                              style="flex: 2; min-width: 68%; margin-left: 10px"
                            >
                              <p-dropdown
                                (click)="filterPlanGroup(customer.service, -1)"
                                (onChange)="selectNewPlan(i, $event, customer)"
                                [options]="planGroupData[custDetails.id]"
                                [disabled]="
                                  newPlanGroupId == null ||
                                  customer.custServMappingStatus === 'Hold' ||
                                  customer.custServMappingStatus === 'Disable'
                                "
                                filter="true"
                                filterBy="plan.displayName"
                                [(ngModel)]="customer.newPlanSelection"
                                optionLabel="plan.displayName"
                                optionValue="plan.id"
                                optionDisabled="inactive"
                                placeholder="Select a New Plan"
                              ></p-dropdown>
                              <div
                                *ngIf="changePlanSubmitted && newPlanSelection"
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">Plan is required.</div>
                              </div>
                            </div>
                            <div
                              *ngIf="
                                changePlanTypeSelection === 'Addon' ||
                                selectedPlanCategory === 'individual'
                              "
                              style="flex: 2; min-width: 68%; margin-left: 10px"
                            >
                              <p-dropdown
                                (onChange)="
                                  selectNewPlan(
                                    i,
                                    $event,
                                    customer,
                                    custDetails.id,
                                    selectedPlanCategory
                                  )
                                "
                                [disabled]="
                                  !customer.changeFlag ||
                                  ((changePlanTypeSelection == 'Renew' ||
                                    changePlanTypeSelection == 'Addon') &&
                                    customer.istrialplan) ||
                                  customer.custServMappingStatus === 'Hold'
                                "
                                [options]="newPlanData[customer.connection_no]"
                                filter="true"
                                filterBy="label"
                                [(ngModel)]="customer.newPlanSelection"
                                optionLabel="label"
                                optionValue="id"
                                placeholder="Select a New Plan"
                              ></p-dropdown>
                              <div
                                *ngIf="changePlanSubmitted && newPlanSelection"
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">Plan is required.</div>
                              </div>
                              <div
                                *ngIf="
                                  (changePlanTypeSelection == 'Renew' ||
                                    changePlanTypeSelection == 'Addon') &&
                                  customer.istrialplan
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">
                                  {{ changePlanTypeSelection }} is not allowed as service is Trail!
                                </div>
                              </div>
                            </div>
                          </td>
                          <td *ngIf="changePlanTypeSelection === 'Addon'">
                            <p-calendar
                              name="fromDate"
                              dateFormat="dd/mm/yy"
                              [showIcon]="true"
                              [showButtonBar]="true"
                              [hideOnDateTimeSelect]="true"
                              placeholder="Enter Start Date"
                              [disabled]="!customer.newPlanSelection"
                              [(ngModel)]="addOnStartDate"
                              [style]="{ width: '100%' }"
                              [minDate]="dateTime"
                              (onSelect)="onDateSelectStartDate($event)"
                            ></p-calendar>
                          </td>
                          <td *ngIf="changePlanTypeSelection === 'Addon'">
                            <p-calendar
                              name="toDate"
                              dateFormat="dd/mm/yy"
                              [showIcon]="true"
                              [showButtonBar]="true"
                              [hideOnDateTimeSelect]="true"
                              placeholder="Enter End Date"
                              [disabled]="!customer.newPlanSelection"
                              [(ngModel)]="addOnEndDate"
                              [style]="{ width: '100%' }"
                              [minDate]="dateTime"
                              (onSelect)="onDateSelect($event)"
                            ></p-calendar>
                          </td>
                          <td *ngIf="changePlanTypeSelection === 'Addon'">
                            <p-checkbox
                              binary="true"
                              class="checkbox-align"
                              [(ngModel)]="renewalForBooster"
                              name="renewalForBooster"
                            ></p-checkbox>
                          </td>
                        </tr>
                      </ng-template>
                      <ng-template pTemplate="emptymessage">
                        <tr>
                          <td colspan="5">No Current Plan found.</td>
                        </tr>
                      </ng-template>
                    </p-table>
                  </ng-template>
                </p-card>
                <p-accordion multiple="true" *ngIf="childCustList">
                  <div *ngFor="let childCust of childCustList; let childIdx = index">
                    <p-accordionTab
                      header="Child Customer: {{ childCust.custname }}"
                      [selected]="false"
                    >
                      <p-card>
                        <ng-template pTemplate="content">
                          <div class="row" *ngIf="changePlanTypeSelection !== 'Addon'">
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2">
                              <p-dropdown
                                [(ngModel)]="childCust.selectedPlanCategory"
                                (click)="$event.stopPropagation()"
                                (onChange)="selectPlanCategory($event, childIdx, childCust)"
                                [options]="planDetailsCategory"
                                [disabled]="
                                  !changePlanTypeSelection ||
                                  (childCust.serviceMappingData != null &&
                                    childCust.serviceMappingData.length == 1)
                                "
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select Plan Category"
                              ></p-dropdown>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2">
                              <p-dropdown
                                [(ngModel)]="childCust.newPlanGroupId"
                                (click)="$event.stopPropagation()"
                                (onChange)="selectPlanGroup($event, childIdx)"
                                [options]="planGroupChanges"
                                [disabled]="
                                  !changePlanTypeSelection ||
                                  childCust.selectedPlanCategory === 'individual' ||
                                  (childCust.serviceMappingData != null &&
                                    childCust.serviceMappingData.length == 1)
                                "
                                filter="true"
                                filterBy="planGroupName"
                                optionLabel="planGroupName"
                                optionValue="planGroupId"
                                placeholder="Select a New Plan Group"
                              ></p-dropdown>
                            </div>
                            <div
                              *ngIf="changePlanTypeSelection == 'Renew'"
                              class="col-lg-3 col-md-3 col-sm-6 col-xs-12 pb-2"
                              style="display: flex"
                            >
                              <div>
                                <p-checkbox
                                  binary="true"
                                  [(ngModel)]="childCust.isAddCharge"
                                  [disabled]="isPlanSelected(childCust.id)"
                                  (onChange)="onDirectChargeChange($event, childCust.id)"
                                  name="allChecked"
                                ></p-checkbox>
                                &nbsp;&nbsp;Add Direct Charge
                              </div>
                              <div style="width: 20%">
                                <button
                                  type="button"
                                  [disabled]="!childCust.isAddCharge"
                                  (click)="openChargeDetails(childCust.id)"
                                  class="btn btn-primary"
                                  style="
                                    border-radius: 5px;
                                    padding: 5px 10px;
                                    line-height: 1.5;
                                    margin-left: 10px;
                                  "
                                >
                                  <i _ngcontent-nrc-c2="" class="fa fa-eye"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                          <p-table
                            #dt
                            [value]="childCust.serviceMappingData"
                            [(selection)]="selectedChangePlan"
                            dataKey="planId"
                            styleClass="p-datatable-customers"
                            [rowHover]="true"
                            [showCurrentPageReport]="true"
                            [globalFilterFields]="[
                              'connection_no',
                              'service',
                              'planName',
                              'status'
                            ]"
                          >
                            <ng-template pTemplate="header">
                              <tr>
                                <th style="width: 3rem"></th>
                                <th>Connection No.</th>
                                <th>Service Name</th>
                                <th *ngIf="this.childCust.plangroupid">Plan Group</th>
                                <th>Current Plan Name</th>

                                <th>Select New Plan</th>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-i="rowIndex" let-customer>
                              <tr class="p-selectable-row">
                                <td
                                  *ngIf="
                                    changePlanTypeSelection === 'Addon' ||
                                    childCust.selectedPlanCategory === 'individual'
                                  "
                                >
                                  <p-checkbox
                                    [(ngModel)]="customer.changeFlag"
                                    (onChange)="
                                      changePlanSelection($event, customer, i, true, childIdx)
                                    "
                                    [pSelectableRow]="customer.customerServiceMappingId"
                                    [disabled]="
                                      !changePlanTypeSelection ||
                                      customer.custServMappingStatus === 'Hold'
                                    "
                                  ></p-checkbox>
                                </td>
                                <td
                                  *ngIf="
                                    changePlanTypeSelection !== 'Addon' &&
                                    childCust.selectedPlanCategory !== 'individual'
                                  "
                                ></td>
                                <td>
                                  {{ customer.connection_no }}
                                </td>
                                <td>
                                  <span class="image-text">{{ customer.service }}</span>
                                </td>
                                <td *ngIf="childCust.plangroupid">
                                  {{ customer.planGroupName }}
                                </td>
                                <td>
                                  <span class="image-text">{{ customer.planName }}</span>
                                </td>
                                <td style="display: flex; flex-wrap: wrap; align-items: center">
                                  <div style="flex: 1; width: 10%">
                                    <button
                                      type="button"
                                      (click)="
                                        modalOpenDetails(
                                          customer.newPlanSelection,
                                          customer.connection_no,
                                          childCust.id,
                                          childCust.selectedPlanCategory
                                        )
                                      "
                                      class="btn btn-primary"
                                      [disabled]="!customer.newPlanSelection"
                                      style="
                                        border-radius: 5px;
                                        padding: 5px 10px;
                                        line-height: 1.5;
                                        margin-left: 10px;
                                      "
                                    >
                                      <i _ngcontent-nrc-c2="" class="fa fa-eye"></i>
                                    </button>
                                  </div>
                                  <div
                                    style="flex: 2; min-width: 68%; margin-left: 10px"
                                    *ngIf="
                                      changePlanTypeSelection !== 'Addon' &&
                                      childCust.selectedPlanCategory !== 'individual'
                                    "
                                  >
                                    <p-dropdown
                                      (click)="filterPlanGroup(customer.service, childIdx)"
                                      (onChange)="selectNewPlan(i, $event, customer)"
                                      [options]="planGroupData[childCust.id]"
                                      [disabled]="
                                        childCust.newPlanGroupId == null ||
                                        customer.custServMappingStatus === 'Hold'
                                      "
                                      filter="true"
                                      filterBy="plan.displayName"
                                      [(ngModel)]="customer.newPlanSelection"
                                      optionLabel="plan.displayName"
                                      optionValue="plan.id"
                                      optionDisabled="inactive"
                                      placeholder="Select a New Plan"
                                    ></p-dropdown>
                                    <div
                                      *ngIf="changePlanSubmitted && newPlanSelection"
                                      class="errorWrap text-danger"
                                    >
                                      <div class="error text-danger">Plan is required.</div>
                                    </div>
                                  </div>
                                  <div
                                    style="flex: 2; min-width: 68%; margin-left: 10px"
                                    *ngIf="
                                      changePlanTypeSelection === 'Addon' ||
                                      childCust.selectedPlanCategory === 'individual'
                                    "
                                  >
                                    <p-dropdown
                                      (onChange)="selectNewPlan(i, $event, customer)"
                                      [disabled]="
                                        !customer.changeFlag ||
                                        ((changePlanTypeSelection == 'Renew' ||
                                          changePlanTypeSelection == 'Addon') &&
                                          customer.istrialplan) ||
                                        customer.custServMappingStatus === 'Hold'
                                      "
                                      [options]="newPlanData[customer.connection_no]"
                                      filter="true"
                                      filterBy="label"
                                      [(ngModel)]="customer.newPlanSelection"
                                      optionLabel="label"
                                      optionValue="id"
                                      placeholder="Select a New Plan"
                                    ></p-dropdown>
                                    <div
                                      *ngIf="changePlanSubmitted && newPlanSelection"
                                      class="errorWrap text-danger"
                                    >
                                      <div class="error text-danger">Plan is required.</div>
                                    </div>
                                    <div
                                      *ngIf="
                                        (changePlanTypeSelection == 'Renew' ||
                                          changePlanTypeSelection == 'Addon') &&
                                        customer.istrialplan
                                      "
                                      class="errorWrap text-danger"
                                    >
                                      <div class="error text-danger">
                                        {{ changePlanTypeSelection }} is not allowed as service is
                                        Trail!
                                      </div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                              <tr>
                                <td colspan="8">No Current Plan found.</td>
                              </tr>
                            </ng-template>
                          </p-table>
                        </ng-template>
                      </p-card>
                    </p-accordionTab>
                  </div>
                </p-accordion>
              </ng-container>
            </div>
          </div>
          <ng-container *ngIf="isVasPlan">
            <div [formGroup]="changePlanNewForm">
              <div class="row">
                <div
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12 form-group"
                  style="margin-top: 20px"
                >
                  <label>Billable To</label>
                  <br />
                  <p-dropdown
                    [disabled]="true"
                    [options]="billableCustList"
                    [showClear]="true"
                    filter="true"
                    filterBy="name"
                    formControlName="billableCustomerId"
                    optionLabel="name"
                    optionValue="id"
                    placeholder="Select a Billable"
                    styleClass="disableDropdown"
                  ></p-dropdown>
                  <button
                    type="button"
                    (click)="modalOpenParentCustomer('billable-change-plan')"
                    class="btn btn-primary"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-plus-square"></i>
                  </button>
                  <button
                    class="btn btn-danger"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                    (click)="removeSelParentCust('billable-change-plan')"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12" style="margin-top: 20px">
                  Payment Received :<br />
                  <label style="width: 150px" class="switch">
                    <input
                      class="switch-input"
                      (click)="paymentFlagToggle($event)"
                      formControlName="isPaymentReceived"
                      type="checkbox"
                      ng-model="view"
                      ng-true-value="Yes"
                      ng-false-value="No"
                      ng-checked="view == 'Yes"
                    />
                    <div class="switch-button">
                      <span class="switch-button-left">Yes</span>
                      <span class="switch-button-right">No</span>
                    </div>
                  </label>
                </div>
                <div
                  *ngIf="changePlanNewForm.value.isPaymentReceived"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12"
                  style="margin-top: 20px"
                >
                  <label>Payment Owner <span *ngIf="paymentOwnerRequired">*</span></label>
                  <p-dropdown
                    [disabled]="true"
                    [options]="staffCustList"
                    optionLabel="name"
                    optionValue="id"
                    filterBy="firstname"
                    placeholder="Select a staff"
                    [filter]="true"
                    formControlName="paymentOwnerId"
                    [showClear]="true"
                    styleClass="disableDropdown"
                  >
                    <ng-template let-data pTemplate="item">
                      <div class="item-drop1">
                        <span class="item-value1"> {{ data.name }} </span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                  <button
                    type="button"
                    (click)="modalOpenStaff()"
                    class="btn btn-primary"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-plus-square"></i>
                  </button>
                  <button
                    [disabled]="paymentOwnerId == null"
                    type="button"
                    (click)="removeSelStaff()"
                    class="btn btn-danger"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                  <div
                    *ngIf="changePlanSubmitted && changePlanNewForm.controls.paymentOwnerId.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">payment owner is required</div>
                  </div>
                </div>
                <div style="margin-top: 45px">
                  <button
                    type="button"
                    class="btn btn-primary"
                    (click)="modalOpenAmount()"
                    [disabled]="isPlanSelected(custDetails.id)"
                    style="
                      border-radius: 5px;
                      padding: 5px 10px;
                      line-height: 1.5;
                      margin-left: 10px;
                    "
                  >
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
                <div
                  class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
                  style="margin-top: 50px"
                  *ngIf="changePlanTypeSelection === 'Addon'"
                >
                  <p-checkbox
                    binary="true"
                    class="checkbox-align"
                    formControlName="isTriggerCoaDm"
                    name="isTriggerCoaDm"
                  ></p-checkbox>
                  <label
                    class="form-check-label"
                    for="isTriggerCoaDm"
                    style="margin-bottom: -8%; margin-left: 10px"
                    >Instant Trigger CoA/DM
                  </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-group">
                  <label>External Remark</label>
                  <textarea class="form-control" formControlName="externalRemark"></textarea>
                  <div
                    *ngIf="changePlanSubmitted && changePlanNewForm.controls.externalRemark.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">External Remark is required.</div>
                  </div>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-group">
                  <label>Remark*</label>
                  <textarea class="form-control" formControlName="remarks"></textarea>
                  <div
                    *ngIf="changePlanSubmitted && changePlanNewForm.controls.remarks.errors"
                    class="errorWrap text-danger"
                  >
                    <div class="error text-danger">Remark is required.</div>
                  </div>
                </div>
                <div style="text-align: center" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                  <button
                    (click)="changePlanGroupBulk()"
                    class="btn btn-primary"
                    id="submit"
                    type="submit"
                  >
                    <i class="fa fa-check-circle"></i>
                    {{
                      changePlanTypeSelection
                        ? changePlanTypeSelection == "Changeplan"
                          ? "Change"
                          : changePlanTypeSelection
                        : "Change"
                    }}
                    Plan
                  </button>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- Service Pack Details -->
          <ng-container *ngIf="!isVasPlan">
            <p-table [value]="[{}]" styleClass="p-datatable-customers" [rowHover]="true">
              <!-- Table Header -->
              <ng-template pTemplate="header">
                <tr>
                  <th style="text-align: center">Current Vas Pack</th>
                  <th style="text-align: center">New Vas Name</th>
                  <th style="text-align: center">Do you Want to Installment?</th>
                  <th style="text-align: center">Installment Type</th>
                  <th style="text-align: center">Total Installment</th>
                </tr>
              </ng-template>

              <!-- Table Body -->
              <ng-template pTemplate="body" let-rowData>
                <tr [formGroup]="servicePackForm">
                  <!-- Old Vas Pack -->
                  <td>
                    <p-dropdown
                      [(ngModel)]="oldVasPackId"
                      [options]="oldVasPackData"
                      filter="true"
                      filterBy="name"
                      optionLabel="name"
                      optionValue="id"
                      placeholder="Select a Current Pack"
                      (onChange)="onChangeOldVasPack($event)"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-dropdown>
                  </td>

                  <!-- Vas Name -->
                  <td>
                    <div style="display: flex; align-items: center">
                      <p-dropdown
                        [ngClass]="{
                          'is-invalid':
                            servicePackSubmitted && servicePackForm.controls.vasId.errors
                        }"
                        [options]="planAllData"
                        filter="true"
                        filterBy="name"
                        formControlName="vasId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Vas *"
                        (onChange)="getPlanFromVasId($event)"
                        [style]="{ width: '200px' }"
                      >
                        <ng-template let-data pTemplate="item">
                          <div class="item-drop1">
                            <span class="item-value1">
                              {{ data.name }}
                              <span *ngIf="data.category == 'Business Promotion'">
                                ( Business Promotion )
                              </span>
                            </span>
                          </div>
                        </ng-template>
                      </p-dropdown>

                      <button
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        id="view-button"
                        title="View Vas Details"
                        class="btn btn-primary"
                        (click)="getVasPlanByCustId()"
                        [disabled]="!servicePackForm.get('vasId')?.value"
                      >
                        <img
                          style="width: 25px; height: 18px; border-radius: 3px"
                          src="assets/img/eye-icon.png"
                        />
                      </button>
                    </div>
                  </td>

                  <!-- Installment Checkbox -->
                  <td style="text-align: center">
                    <p-checkbox
                      binary="true"
                      [(ngModel)]="isInstallemnt"
                      (onChange)="onChangeInstallmentType()"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-checkbox>
                  </td>

                  <!-- Installment Type -->
                  <td>
                    <p-dropdown
                      [options]="commonDropDownService.installmentTypeData"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      placeholder="Select a Installment Type"
                      formControlName="installmentFrequency"
                      [showClear]="true"
                      [disabled]="!isInstallemnt"
                      [ngClass]="{
                        'is-invalid':
                          servicePackSubmitted &&
                          servicePackForm.controls.installmentFrequency.errors
                      }"
                    ></p-dropdown>
                  </td>

                  <!-- Total Installment -->
                  <td>
                    <p-dropdown
                      [options]="totalInstallments"
                      optionValue="value"
                      optionLabel="text"
                      filter="true"
                      filterBy="text"
                      placeholder="Installments"
                      formControlName="totalInstallments"
                      [showClear]="true"
                      [disabled]="!isInstallemnt"
                      [ngClass]="{
                        'is-invalid':
                          servicePackSubmitted && servicePackForm.controls.totalInstallments.errors
                      }"
                    ></p-dropdown>
                  </td>
                </tr>

                <!-- Validation Row -->
                <tr>
                  <td>
                    <div
                      *ngIf="servicePackSubmitted && servicePackForm.controls.vasId.errors"
                      class="errorWrap text-danger"
                    >
                      Vas is required.
                    </div>
                  </td>
                  <td></td>
                  <td>
                    <div
                      *ngIf="
                        servicePackSubmitted &&
                        servicePackForm.controls.installmentFrequency.errors &&
                        isInstallemnt
                      "
                      class="errorWrap text-danger"
                    >
                      Install Type is required.
                    </div>
                  </td>
                  <td>
                    <div
                      *ngIf="
                        servicePackSubmitted &&
                        servicePackForm.controls.totalInstallments.errors &&
                        isInstallemnt
                      "
                      class="errorWrap text-danger"
                    >
                      Installment No is required.
                    </div>
                  </td>
                </tr>
              </ng-template>

              <!-- Empty Message -->
              <ng-template pTemplate="emptymessage">
                <tr>
                  <td colspan="5" style="text-align: center">No Vas Pack Data</td>
                </tr>
              </ng-template>
            </p-table>

            <!-- Submit Button -->
            <div style="text-align: center; margin-top: 1rem">
              <button (click)="addVasPackData()" class="btn btn-primary" type="submit">
                <i class="fa fa-check-circle"></i>
                Add Vas Pack
              </button>
            </div>
          </ng-container>

          <ng-container> </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Open Dialog for connection details -->
<app-plan-connection-no
  *ngIf="showPlanConnectionNo"
  [planForConnection]="planForConnection"
  (closeDialog)="closeDialog()"
></app-plan-connection-no>

<!-- Dialog for show promise to pay details -->
<app-promisetopay-details-modal
  [promiseToPayData]="promiseToPayData"
  dialogId="promiseToPayDataModal"
></app-promisetopay-details-modal>

<!-- Dialog for show quota details of the plan -->

<app-quota-details-modal
  *ngIf="visibleQuotaDetails"
  [PlanQuota]="planQuota"
  dialogId="quotaModalOpen"
  (closeDialogg)="closeModel()"
></app-quota-details-modal>
<app-customer-select
  *ngIf="showParentCustomerModel"
  [type]="custType"
  [custId]="custDetails.id"
  [selectedCust]="selectedParentCust"
  (selectedCustChange)="selectedCustChange($event)"
  (closeParentCust)="closeParentCust()"
></app-customer-select>
<app-select-staff
  *ngIf="displayDTVHistory"
  [selectedStaff]="selectedStaff"
  (selectedStaffChange)="selectedStaffChange($event)"
  (closeStaff)="closeStaff()"
  [isPaymentOwnerType]="true"
></app-select-staff>
<p-dialog
  header="Plan Due Amount"
  [(visible)]="displayAmountModel"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Amount :</label>
        <span>{{ customerChangePlanDueAmount?.Amount | number: "1.2-2" }}</span>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="closeDisplayPlanAmountDetails()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
<p-dialog
  header="Plan Details"
  [(visible)]="displayPlanDetails"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" *ngIf="planDetails">
    <div class="row">
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Quota Type :</label>
        <span>{{ planDetails.quotatype }}</span>
      </div>
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Data Quota :</label>
        <span *ngIf="planDetails.quota"> {{ planDetails.quota }}-{{ planDetails.quotaUnit }} </span>
        <span *ngIf="!planDetails.quota">-</span>
      </div>
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Time Quota :</label>
        <span *ngIf="planDetails.quotatime">
          {{ planDetails.quotatime }}-{{ planDetails.quotaunittime }}
        </span>
        <span *ngIf="!planDetails.quotatime">-</span>
      </div>
    </div>
    <div class="row">
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Price (incl. Tax) :</label>
        <span *ngIf="planDetails.category == 'Business Promotion'">{{
          planDetails.newOfferprice | number: "1.2-2"
        }}</span>
        <span *ngIf="planDetails.category !== 'Business Promotion'">{{
          planDetails.offerprice | number: "1.2-2"
        }}</span>
      </div>
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Discount (%) :</label>
        <span>{{ planDiscount | number: "1.2-2" }}</span>
      </div>
      <!-- <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
            <label class="datalbl">Final Offer Price :</label>
            <span>{{ finalOfferPrice | number : "1.2-2" }}</span>
          </div> -->
      <!-- <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
            <label class="datalbl">Refundable Amount :</label>
            <span>{{ changePlanDate.refundableAmount }}</span>
          </div> -->
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Validity :</label>
        <span> {{ planDetails.validity }} {{ planDetails.unitsOfValidity }} </span>
      </div>
      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Current Date :</label>
        <span> {{ currentDate | date: "yyyy-MM-dd HH:mm:ss a" }} </span>
      </div>

      <div class="col-lg-4 col-md-4 col-xs-12 dataGroup">
        <label class="datalbl">Expiry Date :</label>
        <span> {{ expiryDate | date: "yyyy-MM-dd HH:mm:ss a" }}</span>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="closeDisplayPlanDetails()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Dialog for add direct charge -->
<p-dialog
  header="Add Direct Charge"
  [(visible)]="showAddDirectCharge"
  [style]="{ width: '75vw' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0.5rem">
      <legend>Add Direct Charge</legend>
      <div class="boxWhite">
        <table [formGroup]="chargeGroupForm" style="width: 100%">
          <tr>
            <td style="text-align: center; padding: 0 10px 0 0">
              <p-dropdown
                (onChange)="selectcharge($event)"
                [ngClass]="{
                  'is-invalid': chargesubmitted && chargeGroupForm.controls.chargeid.errors
                }"
                [options]="commonDropDownService.chargeByTypeData"
                filter="true"
                filterBy="name"
                formControlName="chargeid"
                optionLabel="name"
                optionValue="id"
                placeholder="Select a Charge"
              ></p-dropdown>
              <div></div>
            </td>

            <td style="text-align: center; padding: 0 10px 0 0">
              <input
                *ngIf="selectchargeValueShow"
                class="form-control"
                disabled
                formControlName="actualprice"
                id="actualprice        "
                min="0"
                name="actualprice"
                placeholder="Enter Actual Price"
                type="number"
              />

              <input
                *ngIf="!selectchargeValueShow"
                [ngClass]="{
                  'is-invalid': chargesubmitted && chargeGroupForm.controls.actualprice.errors
                }"
                class="form-control"
                formControlName="actualprice"
                id="actualprice        "
                min="0"
                name="actualprice"
                placeholder="Enter Actual Price"
                type="number"
              />
            </td>

            <td
              style="text-align: center; padding: 0 10px 0 0"
              *ngIf="isStaticIPAdrress(chargeGroupForm.value.chargeid)"
            >
              <input
                class="form-control"
                type="text"
                placeholder="Enter Static IP"
                name="staticIPAdrress"
                id="staticIPAdrress"
                formControlName="staticIPAdrress"
              />
            </td>

            <td style="text-align: center; padding: 0 10px 0 0">
              <p-dropdown
                (onChange)="getPlanValidityForChagre($event)"
                [ngClass]="{
                  'is-invalid': chargesubmitted && chargeGroupForm.controls.planid.errors
                }"
                [options]="plansForCharge"
                filter="true"
                filterBy="name"
                formControlName="planid"
                optionLabel="planName"
                optionValue="planId"
                placeholder="Select a Plan *"
              ></p-dropdown>
              <div></div>
            </td>
            <!-- <td style="text-align: center; padding: 0 10px 0 0">
              <p-calendar
                dateFormat="dd-mm-yy"
                [showIcon]="true"
                [showButtonBar]="true"
                [hideOnDateTimeSelect]="true"
                placeholder="Service Expiry Date"
                formControlName="expiry"
                [minDate]="dateTime"
              ></p-calendar>
            </td> -->
            <td style="text-align: center; padding: 0 10px 0 0">
              <input
                [ngClass]="{
                  'is-invalid': chargesubmitted && chargeGroupForm.controls.price.errors
                }"
                class="form-control"
                formControlName="price"
                id="price"
                min="0"
                name="price"
                placeholder="Enter New Price"
                type="number"
              />
            </td>
            <td style="text-align: left; padding: 0 10px 0 0">
              <input
                disabled
                class="form-control"
                formControlName="discount"
                id="discount"
                min="0"
                name="discount"
                placeholder="Enter discount"
                type="number"
              />
            </td>
            <td style="text-align: center; width: 5%">
              <button
                (click)="onAddoverChargeListField()"
                class="btn btn-primary"
                style="object-fit: cover; padding: 5px 8px"
              >
                <i aria-hidden="true" class="fa fa-plus-square"></i>
                Add
              </button>
            </td>
          </tr>
          <tr>
            <td>
              <div
                *ngIf="chargesubmitted && chargeGroupForm.controls.chargeid.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Charge is required.</div>
              </div>
            </td>
            <td>
              <div
                *ngIf="chargesubmitted && chargeGroupForm.controls.actualprice.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Charge Amount is required.</div>
              </div>
            </td>
            <td *ngIf="isStaticIPAdrress(chargeGroupForm.value.chargeid)">
              <div
                class="errorWrap text-danger"
                *ngIf="chargesubmitted && chargeGroupForm.controls.staticIPAdrress.errors"
              >
                <div class="error text-danger">Static IP Address is required.</div>
              </div>
            </td>
            <td>
              <div
                *ngIf="chargesubmitted && chargeGroupForm.controls.planid.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Plan is required.</div>
              </div>
            </td>
            <td style="text-align: center; padding: 0 10px 0 0">
              <div
                class="errorWrap text-danger"
                *ngIf="chargesubmitted && chargeGroupForm.controls.expiry.errors"
              >
                <div class="error text-danger">Expiry Date is required.</div>
              </div>
            </td>
            <td style="text-align: center; padding: 0 10px 0 0">
              <div
                *ngIf="chargesubmitted && chargeGroupForm.controls.price.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">New Price is required.</div>
              </div>
            </td>
            <td style="text-align: center; padding: 0 10px 0 0"></td>
            <td style="text-align: center; width: 5%"></td>
          </tr>
        </table>

        <div
          *ngIf="this.chargeGroupForm.value.price < this.chargeGroupForm.value.actualprice"
          class="errorWrap text-danger"
        >
          <div class="error text-danger">
            New Price must not be less than the actual charge price
          </div>
        </div>

        <table class="table coa-table" style="margin-top: 3rem">
          <thead>
            <tr>
              <th style="text-align: center">Charge Name</th>
              <th style="text-align: center">Charge Amount</th>
              <th style="text-align: center">Charge Type</th>
              <th style="text-align: center">Plan Name</th>
              <th style="text-align: center">Plan Validity</th>
              <th style="text-align: center">New Price</th>
              <th style="text-align: center">Discount (%)</th>
              <th style="text-align: right; width: 5%; padding: 8px">Delete</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let row of overChargeListFromArray.controls
                  | paginate
                    : {
                        id: 'overChargeListFromArrayData',
                        itemsPerPage: overChargeListItemPerPage,
                        currentPage: currentPageoverChargeList,
                        totalItems: overChargeListtotalRecords
                      };
                let index = index
              "
            >
              <td style="padding-left: 8px">
                <p-dropdown
                  [disabled]="true"
                  [formControl]="row.get('chargeid')"
                  [options]="commonDropDownService.chargeByTypeData"
                  filter="true"
                  filterBy="name"
                  optionLabel="name"
                  optionValue="id"
                ></p-dropdown>
                <div></div>
              </td>
              <td>
                <input
                  [formControl]="row.get('actualprice')"
                  class="form-control"
                  id="actualprice        "
                  min="0"
                  name="actualprice"
                  placeholder="Enter Actual Price"
                  readonly
                  type="number"
                />
              </td>
              <td style="padding-left: 8px">
                <p-dropdown
                  [disabled]="true"
                  [formControl]="row.get('type')"
                  [options]="chargeType"
                  filter="true"
                  filterBy="label"
                  optionLabel="label"
                  optionValue="label"
                ></p-dropdown>
                <div></div>
              </td>
              <td>
                <select
                  [formControl]="row.get('planid')"
                  class="form-control"
                  disabled
                  id="planId"
                  name="planId"
                  style="width: 100%"
                >
                  <option value="">Select Plan</option>
                  <option *ngFor="let item of plansForCharge" value="{{ item.planId }}">
                    {{ item.planName }}
                  </option>
                </select>
              </td>
              <td>
                <div style="display: flex">
                  <div style="width: 40%">
                    <input
                      [formControl]="row.get('validity')"
                      class="form-control"
                      id="validity"
                      min="1"
                      placeholder="Enter Validity"
                      readonly
                      type="number"
                    />
                  </div>
                  <div style="width: 60%; height: 34px">
                    <p-dropdown
                      [disabled]="true"
                      [formControl]="row.get('unitsOfValidity')"
                      [options]="commonDropDownService.validityUnitData"
                      filter="true"
                      filterBy="label"
                      optionLabel="label"
                      optionValue="label"
                    ></p-dropdown>
                  </div>
                </div>
              </td>
              <td>
                <input
                  [formControl]="row.get('price')"
                  class="form-control"
                  id="price"
                  min="0"
                  name="price"
                  placeholder="Enter Price"
                  readonly
                  type="number"
                />
              </td>
              <td>
                <input
                  disabled
                  class="form-control"
                  [formControl]="row.get('discount')"
                  id="discount"
                  min="0"
                  name="discount"
                  placeholder="Enter discount"
                  type="number"
                />
              </td>
              <td style="text-align: right">
                <span>
                  <button
                    (click)="deleteConfirmonChargeField(index, row.get('custId'))"
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span>
                <!-- <span
                  *ngIf="
                    index > parentChargeRecurringCustList ||
                    this.customerChargeDataShowChangePlan.length == 0
                  "
                >
                  <button
                    (click)="deleteConfirmonChargeField(index, 'Charge')"
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span> -->
              </td>
            </tr>
          </tbody>
        </table>

        <div class="row">
          <div class="col-md-12">
            <pagination-controls
              (pageChange)="pageChangedOverChargeList($event)"
              directionLinks="true"
              id="overChargeListFromArrayData"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            >
            </pagination-controls>
          </div>
        </div>
        <br />
      </div>
    </fieldset>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn mr-2">
      <button
        type="button"
        class="btn btn-primary"
        (click)="saveChargeData()"
        [disabled]="overChargeListFromArray.controls.length == 0"
      >
        Save
      </button>
    </div>
    <div class="addUpdateBtn">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="closeAddCharge()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Show added charge details -->
<p-dialog
  header="Added Charges"
  [(visible)]="showChargeDetails"
  [style]="{ width: '75vw' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0.5rem">
      <legend>Add Direct Charge</legend>
      <div class="boxWhite">
        <table class="table coa-table" style="margin-top: 3rem">
          <thead>
            <tr>
              <th style="text-align: center">Charge Name</th>
              <th style="text-align: center">Charge Amount</th>
              <th style="text-align: center">Charge Type</th>
              <th style="text-align: center">Plan Name</th>
              <th style="text-align: center">Plan Validity</th>
              <th style="text-align: center">New Price</th>
              <th style="text-align: center">Discount (%)</th>
              <th style="text-align: right; width: 5%; padding: 8px">Delete</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let row of addedChargeList
                  | paginate
                    : {
                        id: 'overChargeListFromArrayData',
                        itemsPerPage: overChargeListItemPerPage,
                        currentPage: currentPageoverChargeList,
                        totalItems: overChargeListtotalRecords
                      };
                let index = index
              "
            >
              <td style="text-align: center">{{ getChargeName(row.chargeid) }}</td>
              <td>
                {{ row.actualprice }}
              </td>
              <td style="text-align: center">
                {{ row.type }}
              </td>
              <td style="text-align: center">
                {{ row.planName }}
              </td>
              <td style="text-align: center">
                {{ row.validity + " " + row.unitsOfValidity }}
              </td>
              <td style="text-align: center">
                {{ row.price }}
              </td>
              <td style="text-align: center">
                {{ row.discount }}
              </td>
              <td style="text-align: right">
                <span>
                  <button
                    (click)="deleteConfirmonChargeField(index, row.custId)"
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span>
                <!-- <span
                  *ngIf="
                    index > parentChargeRecurringCustList ||
                    this.customerChargeDataShowChangePlan.length == 0
                  "
                >
                  <button
                    (click)="deleteConfirmonChargeField(index, 'Charge')"
                    class="approve-btn"
                    id="deleteAtt"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                </span> -->
              </td>
            </tr>
          </tbody>
        </table>

        <div class="row">
          <div class="col-md-12">
            <pagination-controls
              (pageChange)="pageChangedOverChargeList($event)"
              directionLinks="true"
              id="overChargeListFromArrayData"
              maxSize="10"
              nextLabel=""
              previousLabel=""
            >
            </pagination-controls>
          </div>
        </div>
        <br />
      </div>
    </fieldset>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: flex-end">
    <div class="addUpdateBtn">
      <button
        data-dismiss="modal"
        class="btn btn-danger btn-sm"
        type="button"
        (click)="closeChargeDetaills()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Dialog for record payment during change plan -->
<p-dialog
  header="Record Payment"
  [(visible)]="displayRecordPaymentDialog"
  [modal]="true"
  [style]="{ width: '90%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  style="background: #f7b206 !important"
>
  <div class="modal-body">
    <form [formGroup]="paymentFormGroup">
      <div class="row" style="margin-bottom: 20px">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Customer*</label>
          <select class="form-control" disabled name="customerid" style="width: 100%">
            <option value="">
              {{ custDetails?.title }}
              {{ custDetails?.custname }}
            </option>
          </select>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12 mb-15">
          <label>Invoice*</label>
          <br />
          <div style="display: inline-block">
            <p-multiSelect
              placeholder="Select a Invoice"
              id="roles"
              [options]="invoiceList"
              formControlName="invoiceId"
              styleClass="disableDropdown"
              [disabled]="true"
              defaultLabel="Invoice"
              optionLabel="docnumber"
              optionValue="id"
              resetFilterOnHide="true"
              [ngClass]="{
                'is-invalid': submitted && paymentFormGroup.controls.invoiceId.errors
              }"
            ></p-multiSelect>
          </div>
          <button
            (click)="modalOpenInvoice(customerId)"
            class="btn btn-primary"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
          >
            <i class="fa fa-plus-square"></i>
          </button>

          <div
            *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors.required"
              class="error text-danger"
            >
              Invoice is required.
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Payment Mode*</label>
          <p-dropdown
            (onChange)="selPayModeRecord($event)"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.paymode.errors
            }"
            [options]="paymentMode"
            [filter]="true"
            filterBy="text"
            formControlName="paymode"
            optionLabel="text"
            optionValue="value"
            placeholder="Select a Payment Mode"
          ></p-dropdown>
          <div
            *ngIf="submitted && paymentFormGroup.controls.paymode.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.paymode.errors.required"
              class="error text-danger"
            >
              Pay Mode is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Source {{ onlineSourceData?.length > 0 ? "*" : "" }}</label>
          <p-dropdown
            [disabled]="!(onlineSourceData?.length > 0)"
            [options]="onlineSourceData"
            [filter]="true"
            filterBy="text"
            optionLabel="text"
            optionValue="value"
            placeholder="Select a Payment Mode"
            formControlName="onlinesource"
            (onChange)="selPaySourceRecord($event)"
          ></p-dropdown>
          <div
            *ngIf="submitted && paymentFormGroup.controls.onlinesource.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.onlinesource.errors.required"
              class="error text-danger"
            >
              Source is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Amount*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.amount.errors
            }"
            class="form-control"
            formControlName="amount"
            min="1"
            placeholder="Enter Amount"
            step=".01"
            type="number"
            customDecimal
            (keypress)="keypressId($event)"
            [readonly]="isShowInvoiceList"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.amount.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.required"
              class="error text-danger"
            >
              Amount is required.
            </div>
            <div
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.pattern"
              class="error text-danger"
            >
              Only numeric characters allowed.
            </div>
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.min"
            >
              Amount must be greater then 0.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Cheque No.*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.chequeno.errors
            }"
            class="form-control"
            formControlName="chequeno"
            min="1"
            placeholder="Enter Cheque No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.chequeno.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequeno.errors.required"
              class="error text-danger"
            >
              Cheque No. is required.
            </div>
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequeno.errors.pattern"
              class="error text-danger"
            >
              Only numeric characters allowed.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>{{ chequeDateName }}*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.chequedate.errors
            }"
            class="form-control"
            formControlName="chequedate"
            [placeholder]="'Enter' + chequeDateName"
            type="date"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.chequedate.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequedate.errors.required"
              class="error text-danger"
            >
              Cheque Date is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Source Bank*</label>
          <select
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.bankManagement.errors
            }"
            class="form-control"
            formControlName="bankManagement"
            style="width: 100%"
          >
            <option value="">Select Source Bank</option>
            <option *ngFor="let bank of bankDataList" value="{{ bank.id }}">
              {{ bank.bankname }} - {{ bank.accountnum }}
            </option>
          </select>

          <div
            *ngIf="submitted && paymentFormGroup.controls.bankManagement.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.bankManagement.errors.required"
              class="error text-danger"
            >
              Bank is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="destinationbank">
          <label>Destination Bank*</label>
          <select
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.destinationBank.errors
            }"
            class="form-control"
            formControlName="destinationBank"
            style="width: 100%"
          >
            <option value="">Select Destination Bank</option>
            <option *ngFor="let bankDest of bankDestination" value="{{ bankDest.id }}">
              {{ bankDest.bankname }} - {{ bankDest.accountnum }}
            </option>
          </select>

          <div
            *ngIf="submitted && paymentFormGroup.controls.destinationBank.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.destinationBank.errors.required"
              class="error text-danger"
            >
              Bank Destination is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Branch</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.branch.errors
            }"
            class="form-control"
            formControlName="branch"
            placeholder="Enter Branch"
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.branch.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.branch.errors.required"
              class="error text-danger"
            >
              Branch is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label
            >Reference No.{{
              paymentFormGroup.value.paymode !== null &&
              paymentFormGroup.value.paymode.toLowerCase() == "Online".toLowerCase()
                ? "*"
                : ""
            }}</label
          >
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.referenceno.errors
            }"
            class="form-control"
            formControlName="referenceno"
            placeholder="Enter Reference No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.referenceno.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.referenceno.errors.required"
              class="error text-danger"
            >
              Reference No. is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Receipt No</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.reciptNo.errors
            }"
            class="form-control"
            formControlName="reciptNo"
            placeholder="Enter Recipt No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.reciptNo.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.reciptNo.errors.required"
              class="error text-danger"
            >
              Recipt No. is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <span>TDS</span>
          <input
            class="form-control"
            formControlName="tdsAmount"
            placeholder="Please enter TDS amount"
            step=".01"
            type="number"
            [readonly]="isShowInvoiceList"
          />
        </div>

        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <span>ABBS</span>
          <input
            class="form-control"
            formControlName="abbsAmount"
            placeholder="Please enter ABBS amount"
            step=".01"
            type="number"
            [readonly]="isShowInvoiceList"
          />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            placeholder="Enter Remark"
            rows="3"
          ></textarea>
          <div
            *ngIf="submitted && paymentFormGroup.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="row">
    <div class="modal-body">
      <p-table [value]="selectedInvoice" [rows]="10" [paginator]="true" *ngIf="isShowInvoiceList">
        <ng-template pTemplate="header">
          <tr>
            <th>Doc Number</th>
            <th>Created BY</th>
            <th>Tax Amount</th>
            <th>Total Invoice</th>
            <th>Pending Amount</th>
            <th>Refundable Amount</th>
            <th>Amount</th>
            <th>TDS</th>
            <th>ABBS</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data>
          <tr>
            <td>{{ data.docnumber }}</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td>{{ data.testamount | number: "1.2-2" }}</td>
            <td>{{ data.tdsCheck }}</td>
            <td>{{ data.abbsCheck }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="submit"
        (click)="addPayment('')"
        [disabled]="!paymentFormGroup.valid"
        label="Add Payment"
        icon="pi pi-check-circle"
        class="btn btn-primary"
      >
        Add Payment
      </button>
      <button
        type="reset"
        (click)="closePaymentForm()"
        label="Close"
        icon="pi pi-times-circle"
        class="btn btn-danger"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Select invoice for record payment -->

<p-dialog
  header="Select Invoice"
  [(visible)]="displaySelectInvoiceDialog"
  [style]="{ width: '75%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <p-table #dt [value]="invoiceList" responsiveLayout="scroll" [(selection)]="selectedInvoice">
      <ng-template pTemplate="header">
        <tr>
          <th>
            <input
              (change)="checkUncheckAllInvoice()"
              [(ngModel)]="masterSelected"
              name="master-checkbox"
              type="checkbox"
            />
          </th>
          <th>Doc Number</th>
          <th>Created By</th>
          <th>Tax Amount</th>
          <th>Total Invoice</th>
          <th>Pending Amount</th>
          <th>Refundable Amount</th>
          <th>Amount</th>
          <th>TDS</th>
          <th>ABBS</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-invoice let-rowIndex="rowIndex">
        <tr>
          <td>
            <input
              [value]="invoice"
              (change)="isAllSelectedInvoice()"
              [(ngModel)]="invoice.isSelected"
              type="checkbox"
            />
          </td>
          <td>{{ invoice.docnumber }}</td>
          <td>-</td>
          <td>-</td>
          <td>-</td>
          <td>-</td>
          <td>-</td>
          <td>
            <input
              pInputText
              [(ngModel)]="invoice.testamount"
              type="number"
              class="small-input"
              (ngModelChange)="
                onSelectedInvoice($event, invoice, invoice.includeTds, invoice.includeAbbs)
              "
            />
          </td>
          <td>
            <div class="p-d-flex p-ai-center">
              <p-checkbox
                (onChange)="onChangeOFTDSTest($event, invoice)"
                [disabled]="!invoice.testamount"
              ></p-checkbox>
              &nbsp;
              <input
                pInputText
                [(ngModel)]="invoice.tdsCheck"
                [readonly]="!invoice.testamount"
                class="small-input"
              />
            </div>
          </td>
          <td>
            <div class="p-d-flex p-ai-center">
              <p-checkbox
                (onChange)="onChangeOFABBSTest($event, invoice)"
                [disabled]="!invoice.testamount"
              ></p-checkbox>
              &nbsp;
              <input
                pInputText
                class="small-input"
                [(ngModel)]="invoice.abbsCheck"
                [readonly]="!invoice.testamount"
              />
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        style="object-fit: cover; padding: 5px 8px"
        class="btn btn-primary"
        (click)="bindInvoice()"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button type="button" class="btn btn-danger" (click)="modalCloseInvoiceList()">Close</button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="VAS Details"
  [(visible)]="openVasDetailsByCust"
  [baseZIndex]="10000"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closedialog()"
>
  <div class="modal-body">
    <div class="panel-collapse collapse in" id="precustDetails">
      <div class="panel-body table-responsive">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="row">
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="row" *ngIf="vasPlan; else elseBlock">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Vas Name :</label>
                    <span>{{ vasPlan?.vasName }}</span>
                  </div>

                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Vas Offer Price :</label>
                    <span>{{ vasPlan?.vasOfferPrice }}</span>
                  </div>
                  <div
                    *ngIf="vasPlan?.amountPerInstallment"
                    class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                  >
                    <label class="datalbl">Per Installment Price:</label>
                    <span>{{ vasPlan?.amountPerInstallment }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Pause Days Limit :</label>
                    <span>{{ vasPlan?.pauseDaysLimit }}</span>
                  </div>

                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Pause Time Limit :</label>
                    <span>{{ vasPlan?.pauseTimeLimit }}</span>
                  </div>

                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Inventory Replace After Years :</label>
                    <span>{{ vasPlan?.inventoryReplaceAfterYears }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Inventory Paid Months :</label>
                    <span>{{ vasPlan?.inventoryPaidMonths }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Inventory Count :</label>
                    <span>{{ vasPlan?.inventoryCount }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Shift Location Years :</label>
                    <span>{{ vasPlan?.shiftLocationYears }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Shift Location Months :</label>
                    <span>{{ vasPlan?.shiftLocationMonths }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Shift Location Count :</label>
                    <span>{{ vasPlan?.shiftLocationCount }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Validity :</label>
                    <span>{{ vasPlan?.validity }} {{ vasPlan?.unitsOfValidity }}</span>
                  </div>
                  <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Units Of Validity :</label>
                    <span>{{ vasPlan?.unitsOfValidity }}</span>
                  </div> -->
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">Start Date:</label>
                    <span>{{ vasPlan?.startDate | date: "dd-MM-yyy" }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                    <label class="datalbl">End Date:</label>
                    <span>{{ vasPlan?.endDate | date: "dd-MM-yyy" }}</span>
                  </div>
                  <ng-container *ngIf="vasPlan?.installmentType">
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                      <label class="datalbl">Intallment Type:</label>
                      <span>{{ vasPlan?.installmentType }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                      <label class="datalbl">Installment Details:</label>
                      <span
                        *ngIf="vasPlan.installmentNo && vasPlan.totalInstallments; else elseBlock"
                        >{{ vasPlan?.totalInstallments || "-" }} out of
                        {{ vasPlan?.installmentNo }}</span
                      >
                      <ng-template #elseBlock><span> - </span></ng-template>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                      <label class="datalbl">Installment Start Date:</label>
                      <span>{{ vasPlan?.installmentStartDate | date: "dd-MM-yyy" }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                      <label class="datalbl">Installment Next Date:</label>
                      <span>{{ vasPlan?.installmentNextDate | date: "dd-MM-yyy" }}</span>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                      <label class="datalbl">Previous Installment Date:</label>
                      <span>{{ vasPlan?.installmentEndDate | date: "dd-MM-yyy" }}</span>
                    </div>
                  </ng-container>
                </div>
                <ng-template #elseBlock>
                  <div class="row msg-wrapper">
                    {{ servicePackMsg }}
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" (click)="closedialog()" class="btn btn-default" data-dismiss="modal">
      Close
    </button>
  </div>
</p-dialog>
