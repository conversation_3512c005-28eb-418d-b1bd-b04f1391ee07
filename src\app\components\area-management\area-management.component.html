<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ areaTitle }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataArea"
            aria-expanded="false"
            aria-controls="searchDataArea"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchDataArea" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                type="text"
                [(ngModel)]="searchAreaName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchArea()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchArea()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchArea()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ areaTitle }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataArea"
            aria-expanded="false"
            aria-controls="allDataArea"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="allDataArea" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>{{ cityTitle }}</th>
                <th>{{ stateTitle }}</th>
                <!-- <th>{{ countryTitle }}</th> -->
                <!-- <th>{{ pincodeTitle }}</th> -->
                <th>Status</th>
                <th *ngIf="deleteAccess || editAccess">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let area of areaListData
                    | paginate
                      : {
                          id: 'areaListData',
                          itemsPerPage: areaListdataitemsPerPage,
                          currentPage: currentPageAreaListdata,
                          totalItems: areaListdatatotalRecords
                        };
                  index as i
                "
              >
                <td>
                  <span
                    class="HoverEffect"
                    data-backdrop="static"
                    data-keyboard="false"
                    title="{{ areaTitle }} Details"
                    (click)="areaDataOpenModel(area)"
                    >{{ area.name }}</span
                  >
                </td>
                <td>{{ area.cityName }}</td>
                <td>{{ area.stateName }}</td>
                <!-- <td>{{ area.countryName }}</td> -->
                <!-- <td>{{ area.code }}</td> -->
                <td *ngIf="area.status == 'ACTIVE' || area.status == 'Active'">
                  <span class="badge badge-success">Active</span>
                </td>
                <td *ngIf="area.status == 'INACTIVE' || area.status == 'Inactive'">
                  <span class="badge badge-danger">Inactive</span>
                </td>
                <td class="btnAction" *ngIf="deleteAccess || editAccess">
                  <a
                    *ngIf="editAccess"
                    id="edit-button"
                    type="button"
                    href="javascript:void(0)"
                    (click)="editArea(area.id)"
                  >
                    <img src="assets/img/ioc01.jpg" />
                  </a>
                  <a
                    *ngIf="deleteAccess"
                    id="delete-button"
                    href="javascript:void(0)"
                    (click)="deleteConfirmonArea(area.id)"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </a>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="pagination_Dropdown">
            <pagination-controls
              id="areaListData"
              maxSize="10"
              directionLinks="true"
              previousLabel=""
              nextLabel=""
              (pageChange)="pageChangedAreaList($event)"
            ></pagination-controls>
            <div id="itemPerPageDropdown">
              <p-dropdown
                [(ngModel)]="areaListdataitemsPerPage"
                [options]="pageLimitOptions"
                optionLabel="value"
                optionValue="value"
                (onChange)="TotalItemPerPage($event)"
              ></p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isAreaEdit ? "Update" : "Create" }} {{ areaTitle }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataArea"
            aria-expanded="false"
            aria-controls="createDataArea"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataArea" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isAreaEdit">
          Sorry you have not privilege to create operation!
        </div>
        <div class="panel-body" *ngIf="createAccess || (isAreaEdit && editAccess)">
          <form [formGroup]="areaFormGroup">
            <label>{{ areaTitle }} Name*</label>
            <input
              type="text"
              class="form-control"
              placeholder="Enter {{ areaTitle }} Name"
              formControlName="name"
              [ngClass]="{
                'is-invalid': submitted && areaFormGroup.controls.name.errors
              }"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && areaFormGroup.controls.name.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && areaFormGroup.controls.name.errors.required"
              >
                {{ areaTitle }} Name is required.
              </div>
              <div
                class="error text-danger"
                *ngIf="submitted && areaFormGroup.controls.name.errors?.cannotContainSpace"
              >
                <p class="error">White space are not allowed.</p>
              </div>
            </div>
            <br />
            <div>
              <label>{{ pincodeTitle }}*</label>

              <p-dropdown
                [options]="pincodeListData"
                optionValue="pincodeid"
                optionLabel="pincode"
                [virtualScroll]="true"
                [itemSize]="30"
                 [scrollHeight]="getDropdownHeight()"
                filter="true"
                filterBy="pincode"
                placeholder="Select a {{ pincodeTitle }}"
                formControlName="pincodeId"
                (onChange)="getSelPincode($event)"
                [ngClass]="{
                  'is-invalid': submitted && areaFormGroup.controls.pincodeId.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && areaFormGroup.controls.pincodeId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && areaFormGroup.controls.pincodeId.errors.required"
                >
                  {{ pincodeTitle }} is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>{{ cityTitle }}*</label>
              <p-dropdown
                [options]="cityListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a {{ cityTitle }}"
                formControlName="cityId"
                [disabled]="areaInputview"
                [ngClass]="{
                  'is-invalid': submitted && areaFormGroup.controls.cityId.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && areaFormGroup.controls.cityId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && areaFormGroup.controls.cityId.errors.required"
                >
                  {{ cityTitle }} is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>{{ stateTitle }}*</label>
              <p-dropdown
                [options]="stateListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a {{ stateTitle }}"
                formControlName="stateId"
                [disabled]="areaInputview"
                [ngClass]="{
                  'is-invalid': submitted && areaFormGroup.controls.stateId.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && areaFormGroup.controls.stateId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && areaFormGroup.controls.stateId.errors.required"
                >
                  {{ stateTitle }} is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>{{ countryTitle }}*</label>
              <p-dropdown
                [options]="countryListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a {{ countryTitle }}"
                formControlName="countryId"
                [disabled]="areaInputview"
                [ngClass]="{
                  'is-invalid': submitted && areaFormGroup.controls.countryId.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && areaFormGroup.controls.countryId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && areaFormGroup.controls.countryId.errors.required"
                >
                  {{ countryTitle }} is required.
                </div>
              </div>
            </div>
            <br />
            <div>
              <label>Status*</label>
              <p-dropdown
                [options]="statusOptions"
                optionValue="label"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                formControlName="status"
                [ngClass]="{
                  'is-invalid': submitted && areaFormGroup.controls.status.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && areaFormGroup.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && areaFormGroup.controls.status.errors.required"
                >
                  {{ areaTitle }} Status is required.
                </div>
              </div>
            </div>
            <br />
            <div class="addUpdateBtn">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                *ngIf="!isAreaEdit"
                (click)="addEditArea('')"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ areaTitle }}
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                *ngIf="isAreaEdit"
                (click)="addEditArea(viewAreaListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update {{ areaTitle }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="IdareaModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h3 class="panel-title">{{ areaparticularData.name }} {{ areaTitle }}</h3>
      </div>
      <div class="modal-body">
        <div class="panel-body table-responsive" id="networkDeviceTabel">
          <table class="table">
            <tbody>
              <tr>
                <td><label class="networkLabel">Name : </label></td>
                <td>{{ areaparticularData.name }}</td>
              </tr>
              <tr>
                <td>
                  <label class="networkLabel">{{ cityTitle }}: </label>
                </td>
                <td>{{ areaparticularData.cityName }}</td>
              </tr>
              <tr>
                <td>
                  <label class="networkLabel">{{ stateTitle }} : </label>
                </td>
                <td>{{ areaparticularData.stateName }}</td>
              </tr>
              <tr>
                <td>
                  <label class="networkLabel">{{ countryTitle }} : </label>
                </td>
                <td>{{ areaparticularData.countryName }}</td>
              </tr>
              <tr>
                <td>
                  <label class="networkLabel">{{ pincodeTitle }} : </label>
                </td>
                <td>{{ areaparticularData.code }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="{{ areaparticularData.name }} {{ areaTitle }}"
  [(visible)]="areaModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeAreaModal()"
>
  <!-- <p-button (click)="closeAreaModal()" icon="pi pi-external-link" label="Show"></p-button> -->
  <!-- <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal">&times;</button>
      <h3 class="panel-title">{{ areaparticularData.name }} {{ areaTitle }}</h3>
    </div> -->
  <div class="modal-body">
    <div class="panel-body table-responsive" id="networkDeviceTabel">
      <table class="table">
        <tbody>
          <tr>
            <td><label class="networkLabel">Name : </label></td>
            <td>{{ areaparticularData.name }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">{{ cityTitle }}: </label>
            </td>
            <td>{{ areaparticularData.cityName }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">{{ stateTitle }} : </label>
            </td>
            <td>{{ areaparticularData.stateName }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">{{ countryTitle }} : </label>
            </td>
            <td>{{ areaparticularData.countryName }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">{{ pincodeTitle }} : </label>
            </td>
            <td>{{ areaparticularData.code }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <!-- <div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal" (click)="closeAreaModal()">
      Close
    </button>
  </div> -->
</p-dialog>
