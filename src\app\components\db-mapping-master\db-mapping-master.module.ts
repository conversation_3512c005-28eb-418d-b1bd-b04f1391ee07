import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { DBMappingMasterComponent } from "./db-mapping-master.component";
import { DeactivateService } from "src/app/service/deactivate.service";

const routes = [
  { path: "", component: DBMappingMasterComponent, canDeactivate: [DeactivateService] },
];

@NgModule({
  declarations: [DBMappingMasterComponent],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],
})
export class DbMappingMasterModule {}
