<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Investment Code</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataState"
            aria-expanded="false"
            aria-controls="searchDataState"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchDataState" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchIcName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchInvestment()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchInvestment()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearInvestmentData()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Investment code</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataState"
            aria-expanded="false"
            aria-controls="allDataState"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="allDataState" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div>
            <div class="row">
              <div class="col-lg-12 col-md-12">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>IC Code</th>
                      <th>Status</th>
                      <th *ngIf="deleteAccess || editAccess">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let Ic of IcListData
                          | paginate
                            : {
                                id: 'IcListDataID',
                                itemsPerPage: IcListdataitemsPerPage,
                                currentPage: currentPageIcListdata,
                                totalItems: IcListdatatotalRecords
                              };
                        index as i
                      "
                    >
                      <td>{{ Ic.icname }}</td>
                      <td>{{ Ic.iccode }}</td>
                      <td *ngIf="Ic.status == 'ACTIVE' || Ic.status == 'Active'">
                        <span class="badge badge-success">Active</span>
                      </td>
                      <td *ngIf="Ic.status == 'INACTIVE' || Ic.status == 'Inactive'">
                        <span class="badge badge-danger">Inactive</span>
                      </td>
                      <td class="btnAction" *ngIf="deleteAccess || editAccess">
                        <a
                          *ngIf="editAccess"
                          id="edit-button"
                          type="button"
                          href="javascript:void(0)"
                          (click)="editInvestment(Ic.id)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </a>
                        <a
                          *ngIf="deleteAccess"
                          id="delete-button"
                          href="javascript:void(0)"
                          (click)="deleteConfirmon(Ic)"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="pagination_Dropdown">
                  <pagination-controls
                    id="IcListDataID"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedRegionList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isIcEdit ? "Update" : "Create" }} Investment Code</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataSate"
            aria-expanded="false"
            aria-controls="createDataSate"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createDataSate" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !isIcEdit">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (isIcEdit && editAccess)">
            <form [formGroup]="investmentFormGroup">
              <label>Investment Code Name*</label>
              <input
                type="text"
                class="form-control"
                placeholder="Enter a Name"
                formControlName="icname"
                [ngClass]="{
                  'is-invalid': submitted && investmentFormGroup.controls.icname.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && investmentFormGroup.controls.icname.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && investmentFormGroup.controls.icname.errors.required"
                >
                  Ic Name is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="
                    submitted && investmentFormGroup.controls.icname.errors?.cannotContainSpace
                  "
                >
                  <p class="error">White space are not allowed.</p>
                </div>
              </div>
              <br />
              <label>Investment Code*</label>
              <input
                [readOnly]="isIcEdit"
                type="text"
                class="form-control"
                placeholder="Enter a Code"
                formControlName="iccode"
                [ngClass]="{
                  'is-invalid': submitted && investmentFormGroup.controls.iccode.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && investmentFormGroup.controls.iccode.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && investmentFormGroup.controls.iccode.errors.required"
                >
                  Ic code is required.
                </div>
              </div>
              <br />
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && investmentFormGroup.controls.status.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && investmentFormGroup.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && investmentFormGroup.controls.status.errors.required"
                >
                  Status is required.
                </div>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  *ngIf="!isIcEdit"
                  (click)="addEditInvestment('')"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Investment Code
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  *ngIf="isIcEdit"
                  (click)="addEditInvestment(viewIcListData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Investment Code
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
