<!-- <p-confirmDialog [style]="{ width: '40vw' }" [baseZIndex]="10000"></p-confirmDialog>
<div class="childComponent">
    <ngx-spinner [fullScreen]="false" type="ball-clip-rotate-multiple" size="medium">
        <p class="loading">Loading...</p>
    </ngx-spinner>
</div> -->
<div class="row">
  <div class="col-md-12">
    <!-- User Data -->
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Change Customer Plan Management</h3>
        <div class="right">
          <button type="button" class="btn-toggle-collapse">
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-body no-padding panel-udata">
        <div class="col-md-12 pcol">
          <div class="dbox">
            <a class="curson_pointer">
              <img src="../../../assets/img/i01.png" style="width: 32px;" />
              <h5>Change Plan</h5>
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- END User Data -->
  </div>
</div>
<div class="row">
  <div class="col-md-12">
    <!-- Form Design -->
    <div class="panel">
      <div class="panel-heading">
        <div class="right">
          <button type="button" class="btn-toggle-collapse">
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-body">
        <form class="form-auth-small" role="form" [formGroup]="changePlanForm">
          <div class="panel-body">
            <h3 class="page-title" *ngIf="ui_id === 'change-plan'">
              <span class="ng-star-inserted">Change</span>
              Plan
            </h3>
            <h3 class="page-title" *ngIf="ui_id === 'purchase-addon-plan'">
              <span class="ng-star-inserted">Purchase Addon</span>
              Plan
            </h3>
            <div
              *ngIf="
                showCurrentPlanDetails === true &&
                ui_id != 'purchase-addon-plan'
              "
            >
              <hr />
              <span><b>Current Plan Details</b></span>
              <hr />

              <div class="row">
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label for="normal-field">Username</label>
                    </div>
                    <div class="col-md-6">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO?.userName
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label>Current Outstanding</label>
                    </div>
                    <div class="col-md-3">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO
                            ?.currentOutstanding | number: '1.2-2'
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label for="normal-field">Plan Name</label>
                    </div>
                    <div class="col-md-6">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO
                            ?.currentPlanName
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label>Day To Expiry</label>
                    </div>
                    <div class="col-md-6">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO
                            ?.daysToExpiry
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label for="normal-field">
                        Data Quota Available/Total
                      </label>
                    </div>
                    <div class="col-md-6">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO
                            ?.totalDataQuota | number: '1.2-2'
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label>Time Quota</label>
                    </div>
                    <div class="col-md-6">
                      <strong>
                        {{
                          subscriberCurrentPlanData?.currentPlanDTO
                            ?.timeQuota || 'NA'
                        }}
                      </strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr />
            <span><b>Purchase</b></span>
            <hr />
            <div class="row">
              <div class="col-md-6">
                <div class="form-group row">
                  <div class="col-md-4">
                    <label style="margin-top: 7px;" for="normal-field">
                      Purchase Type *
                    </label>
                  </div>
                  <div class="col-md-8">
                    <ng-select
                      id="planPurchaseTypeList"
                      formControlName="planPurchaseType"
                      [items]="planPurchaseTypeList"
                      bindLabel="text"
                      bindValue="value"
                      (change)="onChangePurchaseType($event)"
                      [ngClass]="{
                        'border-danger':
                          submitted &&
                          changePlanForm.controls['planPurchaseType'].errors
                            ?.required
                      }"
                    ></ng-select>
                    <div
                      *ngIf="submitted && f.planPurchaseType.errors"
                      class="text-danger"
                    >
                      This field is required
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6" *ngIf="showAdvRenewal">
                <div class="form-group row">
                  <div class="col-md-4">
                    <label style="margin-top: 7px;">Adv. Renewal</label>
                  </div>
                  <div class="col-md-8" *ngIf="showAdvRenewal">
                    <ng-select
                      id="advRenewalList"
                      formControlName="advRenewal"
                      [items]="advRenewalList"
                      bindLabel="text"
                      bindValue="value"
                      (change)="onChangeAdvRenewal($event)"
                    ></ng-select>
                    <div
                      *ngIf="submitted && f.advRenewal.errors"
                      class="text-danger"
                    >
                      This Field is required
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group row">
                  <div class="col-md-4">
                    <label style="margin-top: 7px;">Select Plan *</label>
                  </div>
                  <div class="col-md-8 text-left">
                    <ng-select
                      id="planList"
                      formControlName="planId"
                      [items]="planList"
                      bindLabel="planName"
                      bindValue="planId"
                      (change)="onChangePlan($event)"
                      [ngClass]="{
                        'border-danger':
                          submitted &&
                          changePlanForm.controls['planId'].errors?.required
                      }"
                    ></ng-select>
                    <div
                      *ngIf="submitted && f.planId.errors"
                      class="text-danger"
                    >
                      This field is required
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mr-3">
                <div class="form-group text-right">
                  <span style="position: relative; top: 1px;">
                    <input
                      type="checkbox"
                      formControlName="isPriceOverride"
                      [(ngModel)]="isOverhead"
                      (change)="checkOverHead(isOverhead ? 'true' : 'false')"
                    />
                  </span>

                  <label class="ml-2">Override Price</label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>Quota Type</label>
                  </div>
                  <div class="col-md-6">
                    <strong>{{ selectedPlan?.quotaType || '-' }}</strong>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label for="normal-field">Data Quota</label>
                  </div>
                  <div class="col-md-6">
                    <strong>
                      {{
                        selectedPlan?.dataQuota === -1
                          ? 'Unlimited'
                          : (selectedPlan?.dataQuota | number: '1.2-2')
                      }}-{{ selectedPlan?.quotaUnit }}
                    </strong>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>Time Quota</label>
                  </div>
                  <div class="col-md-6">
                    <strong>
                      {{
                        selectedPlan?.timeQuota === -1
                          ? 'Unlimited'
                          : selectedPlan?.timeQuota
                      }}-{{ selectedPlan?.quotaunittime }}
                    </strong>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label for="normal-field">Validity</label>
                  </div>
                  <div class="col-md-6">
                    <strong>{{ selectedPlan?.validity || '-' }}</strong>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div
                  class="form-group row"
                  *ngIf="!addonStart && ui_id !== 'purchase-addon-plan'"
                >
                  <div class="col-md-6">
                    <label for="normal-field">Activation Date</label>
                  </div>
                  <div class="col-md-6">
                    <strong>
                      {{ displayActivateDate | date: 'dd-MM-yyyy' || '-' }}
                    </strong>
                  </div>
                </div>
                <div
                  class="form-group row"
                  *ngIf="addonStart && isaddon === false"
                >
                  <div class="col-md-5 mt-3">
                    <label for="normal-field">StartDate *</label>
                  </div>
                  <div class="col-md-7">
                    <input
                      ngbDatepicker
                      [(ngModel)]="addonStartDate"
                      class="form-control"
                      style="display: inline-block; width: 90%;"
                      (dateSelect)="onAddonSelect($event)"
                      placeholder="DD/MM/YYYY"
                      formControlName="addonStartDate"
                      [ngClass]="{
                        'border-danger':
                          submitted &&
                          changePlanForm.controls['addonStartDate'].errors
                            ?.required
                      }"
                    />

                    <div
                      *ngIf="submitted && f.addonStartDate.errors"
                      class="text-danger"
                    >
                      required
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="col-md-4"
                *ngIf="ui_id !== 'purchase-addon-plan' || isaddon === false"
              >
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>Expiry Date</label>
                  </div>
                  <div class="col-md-6">
                    <strong *ngIf="ui_id !== 'purchase-addon-plan'">
                      {{ displayExpiryDate | date: 'dd-MM-yyyy' || '-' }}
                    </strong>
                    <strong *ngIf="ui_id === 'purchase-addon-plan'">
                      {{ endDate }}
                    </strong>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4" *ngIf="isOverhead">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>Price Override</label>
                  </div>
                  <div class="col-md-6" style="margin-top: -20px;">
                    <span style="position: relative; top: 1px;">
                      <input
                        type="checkbox"
                        formControlName="isIncludeTax"
                        [(ngModel)]="isChecked"
                        (change)="
                          checkIncludingTax(isChecked ? 'true' : 'false')
                        "
                      />
                    </span>

                    <label class="ml-2">Including Tax</label>
                    <input
                      type="text"
                      (keypress)="keypress($event)"
                      (blur)="onOverridePrice($event.target.value)"
                      class="form-control"
                      placeholder="Override Price"
                      formControlName="sellPrice"
                    />
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>
                      Price
                      <span style="font-size: 12px;">(Incl.Tax)</span>
                    </label>
                  </div>
                  <div class="col-md-6">
                    <strong *ngIf="ui_id === 'change-plan'">
                      {{
                        planPrice + taxDetailsByPlanData
                          | number: '1.2-2' || '-'
                      }}
                    </strong>
                    <strong *ngIf="ui_id === 'purchase-addon-plan'">
                      {{ purchaseaddonprice | number: '1.2-2' || '-' }}
                    </strong>
                  </div>
                </div>
              </div>

              <div
                class="col-md-4"
                *ngIf="
                  advanceRenewFlag === false &&
                  purchaseType === 'Renew' &&
                  ui_id !== 'purchase-addon-plan'
                "
              >
                <div class="form-group row">
                  <div class="col-md-6">
                    <label for="normal-field">Refund Amount</label>
                  </div>
                  <div class="col-md-6">
                    <strong>
                      {{
                        subscriberCurrentPlanData?.currentPlanDTO
                          ?.refundableAmount | number: '1.2-2' || '-'
                      }}
                    </strong>
                    <input
                      style="margin-left: 8px;"
                      id="chkSkip"
                      class="form-check-input"
                      (change)="onSkipCheck()"
                      type="checkbox"
                      value="skip"
                      formControlName="skip"
                    />
                    <label
                      style="margin-left: 25px;"
                      class="form-check-label"
                      for="chkSkip"
                    >
                      Skip
                    </label>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label>Final Payable Amount</label>
                  </div>
                  <div class="col-md-6">
                    <span *ngIf="ui_id === 'change-plan'">
                      <strong
                        *ngIf="
                          advanceRenewFlag === true &&
                          (changePlanForm.value.skip === null ||
                            changePlanForm.value.skip === false)
                        "
                      >
                        {{ planPrice + taxDetailsByPlanData | number: '1.2-2' }}
                      </strong>
                      <strong
                        *ngIf="
                          advanceRenewFlag === false &&
                          changePlanForm.value.skip === true
                        "
                      >
                        {{ planPrice + taxDetailsByPlanData | number: '1.2-2' }}
                      </strong>
                      <strong *ngIf="purchaseType === 'Upgrade'">
                        {{ planPrice + taxDetailsByPlanData | number: '1.2-2' }}
                      </strong>
                      <strong *ngIf="purchaseType === 'New'">
                        {{ planPrice + taxDetailsByPlanData | number: '1.2-2' }}
                      </strong>
                      <strong
                        *ngIf="
                          purchaseType !== 'Upgrade' &&
                          advanceRenewFlag === false &&
                          (changePlanForm.value.skip === null ||
                            changePlanForm.value.skip === false)
                        "
                      >
                        {{ refundCal() }}
                      </strong>
                    </span>
                    <span *ngIf="ui_id === 'purchase-addon-plan'">
                      <strong>
                        {{ purchaseaddonprice | number: '1.2-2' || '-' }}
                      </strong>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="form-group row">
                  <div class="col-md-2">
                    <label for="normal-field">Remarks *</label>
                  </div>
                  <div class="col-md-10">
                    <textarea
                      class="form-control"
                      rows="4"
                      cols="60"
                      formControlName="remarks"
                      placeholder="Enter Remarks"
                      [ngClass]="{
                        'border-danger':
                          submitted &&
                          changePlanForm.controls['remarks'].errors?.required
                      }"
                    ></textarea>
                    <div
                      *ngIf="submitted && f.remarks.errors"
                      class="text-danger"
                    >
                      This field is required
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-10"></div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group row">
                  <div class="col-md-6">
                    <label for="normal-field">Payment Received</label>
                  </div>
                  <div class="col-md-6">
                    <input
                      (change)="onPaymentTypeChange('YES')"
                      type="radio"
                      name="isPaymentReceived"
                      value="yes"
                      formControlName="isPaymentReceived"
                    />
                    &nbsp;&nbsp;Yes
                    <input
                      (change)="onPaymentTypeChange('NO')"
                      type="radio"
                      name="isPaymentReceived"
                      value="no"
                      formControlName="isPaymentReceived"
                      class="ml-3"
                    />
                    &nbsp;&nbsp;No
                  </div>
                </div>
              </div>
            </div>

            <div
              *ngIf="changePlanForm.get('isPaymentReceived').value === 'yes'"
              formGroupName="recordPaymentDTO"
            >
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4 mt-1">
                      <label for="normal-field">Amount</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        (keypress)="keypress($event)"
                        class="form-control"
                        placeholder="Enter Amount"
                        formControlName="amount"
                        min="0"
                        [ngClass]="{ 'border-danger': amountValid === false }"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4 mt-1">
                      <label>Payment Date</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        ngbDatepicker
                        class="form-control"
                        placeholder="DD/MM/YYYY"
                        formControlName="payment_date"
                        class="form-control"
                        style="display: inline-block; width: 90%;"
                        [ngClass]="{
                          'border-danger': paymentDateValid === false
                        }"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4 mt-1">
                      <label for="normal-field">Payment Mode</label>
                    </div>
                    <div class="col-md-8">
                      <ng-select
                        [items]="paymentModeList"
                        formControlName="payment_mode"
                        bindLabel="text"
                        bindValue="value"
                        (change)="onChangePaymentMode($event)"
                        placeholder="Select Payment Mode"
                        [ngClass]="{
                          'border-danger': paymentModeValid === false
                        }"
                      ></ng-select>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label>Reference No.</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Reference Number"
                        formControlName="referenceNo"
                        [ngClass]="{
                          'border-danger': referenceNoValid === false
                        }"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="row" *ngIf="selected_payment_mode_value === 'cheque'">
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label for="normal-field">Cheque No</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Cheque Number"
                        formControlName="chequeNo"
                        [ngClass]="{ 'border-danger': chequeNoValid === false }"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label>Bank Name *</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Bank Name"
                        formControlName="bankName"
                        [ngClass]="{ 'border-danger': bankNameValid === false }"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="row" *ngIf="selected_payment_mode_value === 'cheque'">
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label for="normal-field">Branch*</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Branch"
                        formControlName="branch"
                        [ngClass]="{ 'border-danger': branchValid === false }"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label for="normal-field">Cheque Date*</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        ngbDatepicker
                        class="form-control"
                        placeholder="DD/MM/YYYY"
                        formControlName="chequeDate"
                        style="display: inline; width: auto; padding: 0px;"
                        [ngClass]="{
                          'border-danger': chequeDateValid === false
                        }"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div
                    class="form-group row"
                    *ngIf="selected_payment_mode_value === 'TDS'"
                  >
                    <div class="col-md-4">
                      <label for="normal-field">TDS Received Againts</label>
                    </div>
                    <div class="col-md-8">
                      <ng-select
                        [items]="tdsPendingPaymentsList"
                        formControlName="credit_doc_id"
                        bindLabel="display_text"
                        bindValue="id"
                        placeholder="Select TDS Pending"
                      ></ng-select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row" *ngIf="selected_payment_mode_value !== 'TDS'">
                <div class="col-md-6">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label for="normal-field">TDS Deducted</label>
                    </div>
                    <div class="col-md-8">
                      <ng-select
                        formControlName="tdsDeducted"
                        (change)="onChangeTdsDeducted($event)"
                      >
                        <ng-option value="">Select TDS Deducted</ng-option>
                        <ng-option [value]="true">Yes</ng-option>
                        <ng-option [value]="false">No</ng-option>
                      </ng-select>
                    </div>
                  </div>
                </div>
                <div class="col-md-6" *ngIf="selected_tds_deducted === true">
                  <div class="form-group row">
                    <div class="col-md-4">
                      <label for="normal-field">TDS Amount *</label>
                    </div>
                    <div class="col-md-8">
                      <input
                        type="number"
                        class="form-control"
                        placeholder="Enter TDS Amount"
                        formControlName="tdsAmount"
                        [ngClass]="{
                          'border-danger': tdsAmountValid === false
                        }"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group row">
                  <div
                    class="col-md-3"
                    *ngIf="
                      checkeligiblity?.isConvertToAddon === false ||
                      (checkeligiblity?.isConvertToAddon === true &&
                        checkeligiblity?.isValidActivePlan === true &&
                        ui_id === 'purchase-addon-plan')
                    "
                  >
                    <button
                      class="btn btn-success"
                      (click)="onClickUpdatePaymentForm()"
                    >
                      Save
                    </button>
                  </div>
                  <div class="col-md-3" *ngIf="ui_id !== 'purchase-addon-plan'">
                    <button
                      class="btn btn-success"
                      (click)="onClickUpdatePaymentForm()"
                    >
                      Save
                    </button>
                  </div>

                  <!-- <div class=" col-md-3">
                                        <button class="btn btn-primary" (click)="closeModal(ui_id);">Close</button>
                                    </div> -->
                </div>
              </div>
              <span
                *ngIf="
                  checkeligiblity?.isConvertToAddon === true &&
                  checkeligiblity?.isValidActivePlan === false
                "
                style="color: red;"
              >
                *You are not eligible to pruchase addon
              </span>
            </div>
          </div>
        </form>
      </div>
    </div>
    <!-- END Form Design -->
  </div>
</div>
