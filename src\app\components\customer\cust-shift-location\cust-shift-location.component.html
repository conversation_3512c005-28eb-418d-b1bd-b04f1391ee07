<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }} {{ custData.lastname }} My Address
          </h3>
        </div>
        <div class="right" *ngIf="addShiftLocationAccess">
          <button
            (click)="openShiftLocationForm()"
            class="btn btn-primary statusbtn"
            data-backdrop="static"
            data-keyboard="false"
            data-title="Shift Location"
            data-toggle="modal"
            style="
              background-color: #f7b206 !important;
              font-size: 16px;
              padding: 3px 12px;
              margin-top: 10px;
            "
            type="submit"
            [disabled]="disableShiftButton"
          >
            Shift Location
          </button>
        </div>
      </div>
      <div class="panel-body table-responsive" id="customerAdrress">
        <!-- <div *ngIf="newCustomerAddressDataForCustometr.status == 'NewActivation'"> -->
        <div class="panel-collapse collapse in" id="shiftLocation">
          <div class="panel-body table-responsive">
            <div class="row">
              <div class="col-lg-12 col-md-12">
                <table class="table">
                  <thead>
                    <tr>
                      <th>New Address</th>
                      <th>Requested By</th>
                      <th>Requested Date</th>
                      <th>Status</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody *ngIf="newCustomerAddressDataForCustometr?.length > 0">
                    <tr *ngFor="let address of newCustomerAddressDataForCustometr">
                      <td>
                        <div style="padding: 8px">
                          {{ address?.fullAddress }}
                        </div>
                      </td>
                      <td>{{ address?.requestedByName }}</td>
                      <td>{{ address?.requestedDate | date }}</td>
                      <td>{{ address?.status }}</td>
                      <td>
                        <button
                          class="approve-btn"
                          style="
                            border: none;
                            background: transparent;
                            padding: 0;
                            margin-right: 3px;
                          "
                          type="button"
                          title="Pick"
                          (click)="pickModalOpen(address)"
                          [disabled]="
                            address?.status != 'NewActivation' || address?.nextStaff != null
                          "
                        >
                          <img width="30px" src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                        </button>

                        <button
                          (click)="shiftLocationApproved(address)"
                          [disabled]="
                            address?.nextStaff == null ||
                            address?.nextStaff != loggedInStaffId ||
                            address?.status != 'NewActivation'
                          "
                          class="approve-btn"
                          title="Approve"
                          type="button"
                        >
                          <img src="assets/img/assign.jpg" />
                        </button>

                        <button
                          (click)="shiftLocationRejected(address)"
                          [disabled]="
                            address?.nextStaff == null ||
                            address?.nextStaff != loggedInStaffId ||
                            address?.status != 'NewActivation'
                          "
                          class="approve-btn"
                          title="Reject"
                          type="button"
                        >
                          <img src="assets/img/reject.jpg" />
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Shift Location Details"
  [(visible)]="displayShiftLocationDetails"
  [style]="{ width: '90%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeShiftLocation()"
>
  <div class="modal-body">
    <fieldset>
      <legend>New Address Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <label>Service Area *</label>
            <p-dropdown
              (onChange)="selServiceArea($event, true)"
              [(ngModel)]="shiftLocationDTO.updateAddressServiceAreaId"
              [disabled]="serviceAreaDisable"
              [options]="commondropdownService.serviceAreaList"
              filter="true"
              filterBy="name"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Servicearea"
            ></p-dropdown>
            <div></div>
            <br />
          </div>
          <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <label>POP</label>
            <p-dropdown
              [options]="commondropdownService.popListData"
              [(ngModel)]="shiftLocationPopId"
              filter="true"
              filterBy="name"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a POP"
            ></p-dropdown>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <label>OLT</label>
            <p-dropdown
              [options]="oltDevices"
              [(ngModel)]="shiftLocationOltId"
              filter="true"
              filterBy="name"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a OLT devices"
            ></p-dropdown>
          </div>
        </div>
        <div [formGroup]="presentGroupForm">
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>Address *</label>
              <input
                [ngClass]="{
                  'is-invalid': submitted && presentGroupForm.controls.landmark.errors
                }"
                class="form-control"
                formControlName="landmark"
                id="landmark"
                name="landmark"
                placeholder="Enter Address"
                type="text"
              />
              <div
                *ngIf="submitted && presentGroupForm.controls.landmark.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Address is required.</div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ pincodeTitle }} *</label>
              <p-dropdown
                (onChange)="selectPINCODEChange($event, 'present')"
                [ngClass]="{
                  'is-invalid': submitted && presentGroupForm.controls.pincodeId.errors
                }"
                [options]="pincodeDD"
                filter="true"
                filterBy="pincode"
                formControlName="pincodeId"
                id="pincodeId"
                optionLabel="pincode"
                optionValue="pincodeid"
                placeholder="Select a {{ pincodeTitle }}"
              ></p-dropdown>
              <div></div>
              <div
                *ngIf="submitted && presentGroupForm.controls.pincodeId.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">{{ pincodeTitle }} is required.</div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ areaTitle }} *</label>
              <p-dropdown
                (onChange)="selectAreaChange($event, 'present')"
                [ngClass]="{
                  'is-invalid': submitted && presentGroupForm.controls.areaId.errors
                }"
                [options]="AreaListDD"
                filter="true"
                filterBy="areaId"
                formControlName="areaId"
                id="areaId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select a {{ areaTitle }}"
              ></p-dropdown>
              <div
                *ngIf="submitted && presentGroupForm.controls.areaId.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">{{ areaTitle }} is required.</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ subareaTitle }} </label>
              <p-dropdown
                id="areatitl"
                (onChange)="onChangeSubArea($event, 'present')"
                [options]="subAreaListDD"
                filter="true"
                filterBy="name"
                formControlName="subareaId"
                id="subareaId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select a {{ subareaTitle }}"
                [virtualScroll]="true"
                [itemSize]="30"
                [scrollHeight]="'200px'"
              >
                <ng-template let-option pTemplate="item">
                  <span>
                    {{ option.name }}
                    &nbsp;
                    <span
                      *ngIf="option.isUnderDevelopment"
                      class="badge badge-info underDevelopBadge"
                    >
                      UnderDevelopment
                    </span>
                  </span>
                </ng-template></p-dropdown
              >
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ buildingTitle }}</label>
              <p-dropdown
                id="areatitl"
                (onChange)="onChangeBuildingArea($event, 'present')"
                [options]="buildingListDD"
                filter="true"
                filterBy="buildingName"
                formControlName="building_mgmt_id"
                id="building_mgmt_id"
                optionLabel="buildingName"
                optionValue="buildingMgmtId"
                placeholder="Select a {{ buildingTitle }}"
                [virtualScroll]="true"
                [itemSize]="30"
                [scrollHeight]="'200px'"
              ></p-dropdown>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>Building Number</label>
              <p-dropdown
                id="areatitl"
                [options]="buildingNoDD"
                filter="true"
                filterBy="buildingNumber"
                formControlName="buildingNumber"
                id="buildingNumber"
                optionLabel="buildingNumber"
                optionValue="buildingNumber"
                placeholder="Select a Building Number"
                [virtualScroll]="true"
                [itemSize]="30"
                [scrollHeight]="'200px'"
              ></p-dropdown>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ cityTitle }} *</label>
              <select
                class="form-control"
                disabled
                formControlName="cityId"
                id="cityId"
                name="cityId"
                style="width: 100%"
              >
                <option value="">Select {{ cityTitle }}</option>
                <option
                  *ngFor="let item of commondropdownService.cityListData"
                  value="{{ item.id }}"
                >
                  {{ item.name }}
                </option>
              </select>
              <div
                *ngIf="submitted && presentGroupForm.controls.cityId.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">{{ cityTitle }} is required.</div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 top">
              <label>{{ stateTitle }} *</label>
              <select
                class="form-control"
                disabled
                formControlName="stateId"
                id="stateId"
                name="stateId"
                style="width: 100%"
              >
                <option value="">Select {{ stateTitle }}</option>
                <option
                  *ngFor="let item of commondropdownService.stateListData"
                  value="{{ item.id }}"
                >
                  {{ item.name }}
                </option>
              </select>
              <div
                *ngIf="submitted && presentGroupForm.controls.stateId.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">{{ stateTitle }} is required.</div>
              </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
              <label>{{ countryTitle }} *</label>
              <select
                class="form-control"
                disabled
                formControlName="countryId"
                id="countryId"
                name="countryId"
                style="width: 100%"
              >
                <option value="">Select {{ countryTitle }}</option>
                <option
                  *ngFor="let item of commondropdownService.countryListData"
                  value="{{ item.id }}"
                >
                  {{ item.name }}
                </option>
              </select>
              <div
                *ngIf="submitted && presentGroupForm.controls.countryId.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">{{ countryTitle }} is required.</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
              <label>Latitude</label>
              <input
                [readonly]="iflocationFill"
                class="form-control"
                formControlName="latitude"
                id="latitude"
                placeholder="Enter latitude"
                type="text"
              />
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
              <label>Longitude</label>
              <input
                [readonly]="iflocationFill"
                class="form-control"
                formControlName="longitude"
                id="longitude"
                placeholder="Enter longitude"
                type="text"
              />
            </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
              <div style="margin-bottom: 1rem">
                <span
                  (click)="mylocation()"
                  class="HoverEffect"
                  style="border-bottom: 1px solid #f7b206"
                  title="Get Current Location"
                >
                  <img
                    class="LocationIcon LocationIconMargin"
                    src="assets/img/B_Find-My-current-location_Y.png"
                  />
                </span>
                <!-- TODO need to make seperate Dialog -->
                <span
                  (click)="openSearchModel()"
                  class="HoverEffect"
                  data-backdrop="static"
                  data-keyboard="false"
                  data-target="#searchLocationModal"
                  data-toggle="modal"
                  style="margin-left: 8px; border-bottom: 1px solid #f7b206"
                  title="Search Location"
                >
                  <img
                    class="LocationIcon LocationIconMargin"
                    src="assets/img/C_Search-location_Y.png"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
            <label>Requested By </label>
            <br />
            <!-- <p-dropdown
                  [options]="staffData"
                  optionLabel="fullName"
                    optionValue="id"
                    [(ngModel)]="requestedByID"
                    filterBy="fullName"
                    placeholder="Select a staff"
                    filter="true"
                    (onChange)="branchChange($event)"
                  ></p-dropdown> -->

            <p-dropdown
              [disabled]="true"
              [options]="staffCustList"
              optionLabel="name"
              optionValue="id"
              filterBy="firstname"
              placeholder="Select a staff"
              [filter]="true"
              [(ngModel)]="requestedByID"
              [showClear]="true"
              styleClass="disableDropdown"
            >
              <ng-template let-data pTemplate="item">
                <div class="item-drop1">
                  <span class="item-value1"> {{ data.name }} </span>
                </div>
              </ng-template>
            </p-dropdown>

            <button
              type="button"
              (click)="modalOpenStaff('requestedBy')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              [disabled]="requestedByID == null"
              type="button"
              (click)="removeSelStaff('requestedBy')"
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-trash"></i>
            </button>

            <div *ngIf="submitted && requestedByID == 0" class="errorWrap text-danger">
              <div class="error text-danger">Requested By is required.</div>
            </div>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top" *ngIf="isBranchShiftLocation">
            <label>Branch/Partner *</label>
            <p-dropdown
              [options]="branchData"
              [(ngModel)]="branchID"
              optionValue="id"
              optionLabel="name"
              filter="true"
              filterBy="name"
              placeholder="Select a branch"
            ></p-dropdown>
            <div *ngIf="submitted && branchID == 0" class="errorWrap text-danger">
              <div class="error text-danger">Branch is required.</div>
            </div>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top" *ngIf="!isBranchShiftLocation">
            <label>Branch/Partner *</label>
            <p-dropdown
              [options]="this.partnerListByServiceArea"
              [(ngModel)]="shiftLocationDTO.shiftPartnerid"
              optionValue="id"
              optionLabel="name"
              filter="true"
              filterBy="name"
              placeholder="Select a partner"
            >
            </p-dropdown>
            <div
              *ngIf="submitted && shiftLocationDTO.shiftPartnerid == 0"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">Partner is required.</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
            <label>Wallet Amount</label>
            <span>
              <input type="text" class="form-control" disabled="true" [(ngModel)]="walletValue" />
            </span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
            <label>Prepaid Value</label>
            <span>
              <input type="text" class="form-control" disabled="true" [(ngModel)]="prepaid" />
            </span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 top">
            <label>Due Value</label>
            <span>
              <input type="text" class="form-control" disabled="true" [(ngModel)]="dueValue" />
            </span>
          </div>
        </div>
        <br />
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <div class="form-group form-check">
              <input
                [(ngModel)]="shiftLocationDTO.isPaymentAddresSame"
                [checked]="shiftLocationDTO.isPaymentAddresSame"
                class="form-check-input"
                id="paymentAddress"
                type="checkbox"
              />
              <label class="form-check-label" for="permanentAddress" style="margin-left: 1rem">
                set <b>Payment Address</b> same as a present address
              </label>
            </div>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <div class="form-group form-check">
              <input
                [(ngModel)]="shiftLocationDTO.isPermanentAddress"
                [checked]="shiftLocationDTO.isPermanentAddress"
                class="form-check-input"
                id="permanentAddress"
                type="checkbox"
              />
              <label class="form-check-label" for="permanentAddress" style="margin-left: 1rem">
                set <b>Permanent Address</b> same as a present address
              </label>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset *ngIf="!isFreeShift">
      <legend>Charge Details</legend>
      <div [formGroup]="shiftLocationChargeGroupForm" class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
            <label>Billable To</label>
            <br />
            <p-dropdown
              [disabled]="true"
              [options]="billableCustList"
              [showClear]="true"
              filter="true"
              filterBy="name"
              formControlName="billableCustomerId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Billable"
              styleClass="disableDropdown"
            ></p-dropdown>
            <button
              type="button"
              (click)="modalOpenParentCustomer('billable-shift-location')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
              (click)="removeSelParentCust('billable-shift-location')"
            >
              <i class="fa fa-trash"></i>
            </button>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
            <label>Payment Owner *</label>
            <br />
            <p-dropdown
              [disabled]="true"
              [options]="staffCustList"
              optionLabel="name"
              optionValue="id"
              filterBy="firstname"
              placeholder="Select a staff"
              [filter]="true"
              formControlName="paymentOwnerId"
              [showClear]="true"
              styleClass="disableDropdown"
            >
              <ng-template let-data pTemplate="item">
                <div class="item-drop1">
                  <span class="item-value1"> {{ data.name }} </span>
                </div>
              </ng-template>
            </p-dropdown>

            <button
              type="button"
              (click)="modalOpenStaff('paymentCharge')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              [disabled]="paymentOwnerId == null"
              type="button"
              (click)="removeSelStaff('paymentCharge')"
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-trash"></i>
            </button>
            <div
              *ngIf="
                ifUpdateAddressSubmited &&
                shiftLocationChargeGroupForm.controls.paymentOwnerId.errors
              "
              class="errorWrap text-danger"
            >
              <div class="error text-danger">Payment Owner is required</div>
            </div>
          </div>
        </div>
        <br />
        <table style="width: 100%">
          <tr>
            <td style="padding-right: 10px">
              <label>Charge *</label>
              <p-dropdown
                (onChange)="selectcharge($event, 'shiftLocation')"
                [ngClass]="{
                  'is-invalid':
                    ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.chargeid.errors
                }"
                [options]="commondropdownService.chargeByTypeData"
                filter="true"
                filterBy="name"
                formControlName="chargeid"
                optionLabel="name"
                optionValue="id"
                placeholder="Select a Charge"
              ></p-dropdown>
              <div></div>
            </td>

            <td style="padding-right: 10px">
              <label>Actual Price *</label>
              <input
                *ngIf="selectchargeValueShow"
                class="form-control"
                disabled
                formControlName="actualprice"
                id="actualprice"
                min="0"
                name="actualprice"
                placeholder="Enter Actual Price"
                type="number"
              />

              <input
                *ngIf="!selectchargeValueShow"
                [ngClass]="{
                  'is-invalid':
                    ifUpdateAddressSubmited &&
                    shiftLocationChargeGroupForm.controls.actualprice.errors
                }"
                class="form-control"
                formControlName="actualprice"
                id="actualprice"
                min="0"
                name="actualprice"
                placeholder="Enter Actual Price"
                type="number"
              />
            </td>

            <td style="padding-right: 10px">
              <label>Charge Type *</label>
              <p-dropdown
                (onChange)="selectTypecharge($event)"
                [ngClass]="{
                  'is-invalid':
                    ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.type.errors
                }"
                [options]="chargeType"
                filter="true"
                filterBy="name"
                formControlName="type"
                optionLabel="label"
                optionValue="label"
                placeholder="Select a Type"
              ></p-dropdown>
              <div></div>
            </td>

            <td style="padding-right: 10px">
              <label>New Price *</label>
              <input
                [ngClass]="{
                  'is-invalid':
                    ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.price.errors
                }"
                class="form-control"
                formControlName="price"
                id="price"
                min="0"
                name="price"
                placeholder="Enter New Price"
                type="number"
              />
            </td>
            <td>
              <label>Discount</label>
              <input
                class="form-control"
                formControlName="discount"
                id="discount"
                min="0"
                name="discount"
                placeholder="Enter discount"
                type="number"
              />
            </td>
          </tr>
          <tr>
            <td>
              <div
                *ngIf="
                  ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.chargeid.errors
                "
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Charge is required.</div>
              </div>
            </td>
            <td>
              <div
                *ngIf="
                  ifUpdateAddressSubmited &&
                  shiftLocationChargeGroupForm.controls.actualprice.errors
                "
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Charge Amount is required.</div>
              </div>
            </td>
            <td style="text-align: center; padding: 0 10px 0 0">
              <div
                *ngIf="ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.type.errors"
                class="errorWrap text-danger"
              >
                <div class="error text-danger">Charge Type is required.</div>
              </div>
            </td>
            <!-- <td
                    *ngIf="shiftLocationChargeGroupForm.value.type == 'Recurring'"
                    style="text-align: center; padding: 0 10px 0 0"
                  >
                    <div
                      *ngIf="
                        ifUpdateAddressSubmited &&
                        shiftLocationChargeGroupForm.controls.billingCycle.errors
                      "
                      class="errorWrap text-danger"
                    >
                      <div class="error text-danger">Billing Cycle is required.</div>
                    </div>
                  </td> -->
            <td style="text-align: center; padding: 0 10px 0 0">
              <div
                *ngIf="
                  ifUpdateAddressSubmited && shiftLocationChargeGroupForm.controls.price.errors
                "
                class="errorWrap text-danger"
              >
                <div class="error text-danger">New Price is required.</div>
              </div>
            </td>
          </tr>
        </table>

        <div
          *ngIf="
            shiftLocationChargeGroupForm.value.price <
            shiftLocationChargeGroupForm.value.actualprice
          "
          class="errorWrap text-danger"
        >
          <div class="error text-danger">
            New Price must not be less than the actual charge price
          </div>
        </div>
        <br />
      </div>
    </fieldset>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveShiftLocation()"
        class="btn btn-primary"
        data-title="Shift Location"
        type="button"
      >
        save
      </button>
      <button type="button" class="btn btn-default" (click)="closeShiftLocation()">Close</button>
    </div>
  </div>
</p-dialog>
<p-dialog
  header="{{ AppRjecHeader }} Shift Location"
  [(visible)]="rejectApproveShiftLocationModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="assignAppRejectShiftLocationForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid':
                assignShiftLocationsubmitted &&
                assignAppRejectShiftLocationForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="
              assignShiftLocationsubmitted &&
              assignAppRejectShiftLocationForm.controls.remark.errors
            "
            class="errorWrap text-danger"
          >
            <div
              *ngIf="
                assignShiftLocationsubmitted &&
                assignAppRejectShiftLocationForm.controls.remark.errors.required
              "
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="assignAddressApprove()"
      [disabled]="!assignAppRejectShiftLocationForm.valid"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      {{ AppRjecHeader }}
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeDisplayShiftLocationDetails()"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
<p-dialog
  header="Assign Staff"
  [(visible)]="rejectCustomerInventoryModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <button aria-label="Close" class="close" data-dismiss="modal" type="button">
    <span aria-hidden="true">&times;</span>
  </button>

  <div class="modal-body">
    <div class="row">
      <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="card">
          <h5>Select Staff</h5>
          <p-table
            [(selection)]="selectStaffReject"
            [value]="rejectInventoryData"
            responsiveLayout="scroll"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToStaff(false)"
      *ngIf="reject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      (click)="closeDisplayShiftLocationDetails()"
      class="btn btn-default"
      data-dismiss="modal"
      type="submit"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Assign Staff"
  [(visible)]="assignShiftLocation1"
  [style]="{ width: '35%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignCustomerInventoryModal"
  role="dialog"
  tabindex="-1"
> -->
  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Staff</h4>
      </div> -->
  <div class="modal-body">
    <div class="row">
      <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="row">
          <div class="col-md-6">
            <input
              id="searchStaffName"
              type="text"
              name="username"
              class="form-control"
              placeholder="Global Search Filter"
              [(ngModel)]="searchStaffDeatil"
              (keydown.enter)="searchStaffByName()"
              [ngModelOptions]="{ standalone: true }"
            />
          </div>
          <div class="col-lg-6 col-md-6 col-sm-12">
            <button
              (click)="searchStaffByName()"
              class="btn btn-primary"
              id="searchbtn"
              type="submit"
              [disabled]="!searchStaffDeatil"
            >
              <i class="fa fa-search"></i>
              Search
            </button>
            <button (click)="clearSearchForm()" class="btn btn-default" id="searchbtn" type="reset">
              <i class="fa fa-refresh"></i>
              Clear
            </button>
          </div>
        </div>
        <div class="card">
          <h5>Select Staff</h5>
          <p-table
            [(selection)]="selectStaff"
            [value]="approveInventoryData"
            responsiveLayout="scroll"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"></th>
                <th>Name</th>
                <th>Username</th>
              </tr>
            </ng-template>
            <ng-template let-product pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                </td>
                <td>{{ product.fullName }}</td>
                <td>
                  {{ product.username }}
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button
      (click)="assignToStaff(true)"
      *ngIf="approved"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      (click)="closeStaffModel(true)"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
  <!-- </div>
  </div> -->
  <!-- </div> -->
</p-dialog>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="rejectCustomerInventoryModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Staff</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [(selection)]="selectStaffReject"
                [value]="rejectInventoryData"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body">
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <!-- <input type="file" formControlName="fileName" name="fileName"> -->
      </div>
      <div class="modal-footer">
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>

<div
  class="modal fade"
  id="reAssignPLANModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Approve Shift Location
        </h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="assignDocForm">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [value]="approvableStaff"
                  [(selection)]="selectStaff"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-product>
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                class="form-control"
                name="remark"
                formControlName="remark"
                [ngClass]="{
                  'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
                }"
              ></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                >
                  Remark is required.
                </div>
              </div>
              <br />
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" id="submit" (click)="reassignWorkflow()">
          <i class="fa fa-check-circle"></i>
          Assign
        </button>

        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<app-select-staff
  *ngIf="isSelectStaff"
  [isPaymentOwnerType]="true"
  [selectedStaff]="selectedStaff"
  (selectedStaffChange)="selectedStaffChange($event)"
  (closeStaff)="closeStaff()"
></app-select-staff>
<app-customer-select
  *ngIf="showParentCustomerModel"
  [type]="custType"
  [custId]="custData.id"
  [selectedCust]="selectedParentCust"
  (selectedCustChange)="selectedCustChange($event)"
  (closeParentCust)="closeParentCust()"
></app-customer-select>
<app-workflow-audit-details-modal
  *ngIf="ifModelIsShow"
  [auditcustid]="auditcustid"
  dialogId="custauditWorkflowModal"
  (closeParentCustt)="closeParentCustt()"
></app-workflow-audit-details-modal>

<div *ngIf="ifsearchLocationModal">
  <div class="modal fade" id="searchLocationModal" role="dialog">
    <div class="modal-dialog searchLocationModalWidth">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Search Location</h3>
        </div>
        <div class="modal-body">
          <form name="searchLocationForm" [formGroup]="searchLocationForm">
            <div class="form-group">
              <label for="searchLocationname">Search Location Name:</label>
              <div class="row">
                <div class="col-lg-7 col-md-6">
                  <input
                    type="searchLocationname"
                    class="form-control"
                    id="searchLocationname"
                    placeholder="Enter Location Name"
                    formControlName="searchLocationname"
                  />
                </div>
                <div class="col-lg-5 col-md-6" style="padding: 0 10px !important">
                  <button
                    type="submit"
                    class="btn btn-primary btn-sm"
                    id="closeModal"
                    (click)="searchLocation()"
                    [disabled]="!searchLocationForm.valid"
                  >
                    <i class="fa fa-search"></i>
                    Search
                  </button>
                  <button
                    id="btn"
                    type="button"
                    class="btn btn-default btn-sm"
                    (click)="clearLocationForm()"
                    style="margin-left: 8px"
                  >
                    <i class="fa fa-refresh"></i>
                    Clear
                  </button>
                </div>
              </div>
            </div>
          </form>
          <div class="row">
            <div class="col-lg-12 col-md-12" style="margin-top: 3rem">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 35%">Name</th>
                    <th style="width: 65%">Address</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of searchLocationData
                        | paginate
                          : {
                              id: 'searchpageData',
                              itemsPerPage: searchLocationItemPerPage,
                              currentPage: currentPagesearchLocationList,
                              totalItems: searchLocationtotalRecords
                            };
                      index as i
                    "
                  >
                    <td
                      class="HoverEffect"
                      (click)="filedLocation(data.placeId)"
                      data-toggle="tooltip"
                      data-placement="bottom"
                      title="Set value Latitude & Longitude"
                      style="width: 35%"
                    >
                      {{ data.name }}
                    </td>
                    <td style="width: 65%">{{ data.address }}</td>
                  </tr>
                </tbody>
              </table>
              <pagination-controls
                id="searchpageData"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedSearchLocationList($event)"
              ></pagination-controls>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="addUpdateBtn">
            <button
              type="button"
              class="btn btn-danger btn-sm"
              #closebutton
              data-dismiss="modal"
              (click)="clearsearchLocationData()"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Shift Location Message"
  [(visible)]="isDisplayShiftLocationMsg"
  [style]="{ width: '35%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignCustomerInventoryModal"
  role="dialog"
  tabindex="-1"
> -->
  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Assign Staff</h4>
      </div> -->
  <div class="modal-body">
    <div class="row">
      {{ shiftLocationMsg }}
    </div>
    <!-- <input type="file" formControlName="fileName" name="fileName"> -->
  </div>
  <div class="modal-footer">
    <button type="submit" class="btn btn-primary" id="submit" (click)="clickToProceed()">
      <i class="fa fa-check-circle"></i>
      Proceed
    </button>
    <button
      class="btn btn-default"
      (click)="closeShiftLocationMsg(true)"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
  <!-- </div>
  </div> -->
  <!-- </div> -->
</p-dialog>
