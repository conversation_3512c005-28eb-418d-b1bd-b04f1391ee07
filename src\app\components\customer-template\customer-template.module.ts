import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CustomerTemplateComponent } from "./customer-template.component";
import { DeactivateService } from "src/app/service/deactivate.service";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

const routes = [
  { path: "", component: CustomerTemplateComponent, canDeactivate: [DeactivateService] },
];

@NgModule({
  declarations: [CustomerTemplateComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class CustomerTemplateModule {}
