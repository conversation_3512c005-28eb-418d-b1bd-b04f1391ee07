<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">KPI Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#kpiSearch"
            aria-expanded="false"
            aria-controls="kpiSearch"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="kpiSearch" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row" *ngIf="listView">
            <div class="col-lg-3 col-md-3">
              <input
                type="text"
                [(ngModel)]="searchVal"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchKPI()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchKPI()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchKPI()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>

        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol" *ngIf="createAccess">
            <div class="dbox">
              <a (click)="createKPIView()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create KPI</h5>
                <!-- <p>Create Charge</p> -->
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="searchKPIView()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search KPI</h5>
                <!-- <p>Search Charge</p> -->
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12" *ngIf="listView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">KPI</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#chargelist"
            aria-expanded="false"
            aria-controls="chargelist"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="chargelist" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Status</th>
                    <th *ngIf="editAccess || deleteAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let KPI of KPIListData
                        | paginate
                          : {
                              id: 'KPIListpageData',
                              itemsPerPage: kpiListdataitemsPerPage,
                              currentPage: currentPageKpiListdata,
                              totalItems: kpiListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ KPI.name }}</td>
                    <td>{{ KPI.startDate | date : "dd/MM/yyyy" }}</td>
                    <td>{{ KPI.endDate | date : "dd/MM/yyyy" }}</td>
                    <td>
                      <span *ngIf="KPI.status == 'ACTIVE'" class="badge badge-success">
                        Active
                      </span>
                      <span *ngIf="KPI.status == 'INACTIVE'" class="badge badge-danger">
                        Inactive
                      </span>
                    </td>
                    <td class="btnAction" *ngIf="editAccess || deleteAccess">
                      <a
                        *ngIf="editAccess"
                        id="edit-button"
                        type="button"
                        href="javascript:void(0)"
                        (click)="editKPI(KPI.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="deleteAccess"
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmonKPI(KPI)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="KPIListpageData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedKPIList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [(ngModel)]="showItemPerPage"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12" *ngIf="createView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isKPIEdit ? "Update" : "Create" }} KPI</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createKPI"
            aria-expanded="false"
            aria-controls="createKPI"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createKPI" class="panel-collapse collapse in">
        <div class="panel-body">
          <div>
            <form [formGroup]="KPIGroupForm">
              <!--    Charge Details    -->
              <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
                <legend>Basic Details</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-4 col-md-4 col-12">
                      <label>KPI Name*</label>
                      <input
                        id="name"
                        type="text"
                        class="form-control"
                        placeholder="Enter KPI Name"
                        formControlName="name"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && KPIGroupForm.controls.name.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && KPIGroupForm.controls.name.errors.required"
                        >
                          KPI Name is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-4 col-md-4 col-12">
                      <label>Start Date*</label>
                      <input
                        id="startDate"
                        type="date"
                        formControlName="startDate"
                        placeholder="DD/MM/YYYY"
                        class="form-control"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && KPIGroupForm.controls.startDate.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && KPIGroupForm.controls.startDate.errors.required"
                        >
                          Start Date is required.
                        </div>
                      </div>
                      <br />
                    </div>

                    <div class="col-lg-4 col-md-4 col-12">
                      <label>End Date*</label>
                      <input
                        id="endDate"
                        type="date"
                        formControlName="endDate"
                        placeholder="DD/MM/YYYY"
                        class="form-control"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && KPIGroupForm.controls.endDate.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && KPIGroupForm.controls.endDate.errors.required"
                        >
                          End Date is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>KPI Status*</label>
                      <div>
                        <p-dropdown
                          [options]="statusOptions"
                          optionValue="val"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select a Status"
                          formControlName="status"
                        ></p-dropdown>
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && KPIGroupForm.controls.status.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && KPIGroupForm.controls.status.errors.required"
                        >
                          KPI Status is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                </div>
              </fieldset>

              <!--    Additional Details    -->
              <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
                <legend>Additional Details</legend>
                <div class="boxWhite">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Area</th>
                        <th>No#</th>
                        <th>Amount</th>
                        <th>Amount %</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let row of KPIMappingFormArray.controls; let i = index">
                        <td>
                          <span *ngIf="i == 0"> Renewal </span>
                          <span *ngIf="i == 1"> Activation - inactive </span>
                          <span *ngIf="i == 2"> Activation - Suspend </span>
                          <span *ngIf="i == 3"> Lead Conversion </span>
                          <span *ngIf="i == 4"> Termination(Clouser) </span>
                        </td>
                        <td>
                          <input type="text" class="form-control" [formControl]="row.get('no')" />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('no').errors?.required
                            "
                          >
                            <div class="error text-danger">No is required.</div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted && KPIMappingFormArray.controls[i].get('no').errors?.pattern
                            "
                          >
                            <div class="error text-danger">Only Numeric Value Allowed.</div>
                          </div>
                        </td>
                        <td>
                          <input
                            type="text"
                            (keyup)="totalAmountFun()"
                            class="form-control"
                            [formControl]="row.get('amount')"
                          />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amount').errors?.required
                            "
                          >
                            <div class="error text-danger">Amount is required.</div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amount').errors?.pattern
                            "
                          >
                            <div class="error text-danger">Only Numeric Value Allowed.</div>
                          </div>
                        </td>
                        <td>
                          <input
                            type="text"
                            class="form-control"
                            (keyup)="totalAmountPerFun()"
                            [formControl]="row.get('amountPercentage')"
                          />
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amountPercentage').errors
                                ?.required
                            "
                          >
                            <div class="error text-danger">Amount Percentage is required.</div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amountPercentage').errors
                                ?.pattern
                            "
                          >
                            <div class="error text-danger">Only Numeric Value Allowed.</div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amountPercentage').errors?.min
                            "
                          >
                            <div class="error text-danger">Minimum 0 value are allowed.</div>
                          </div>
                          <div
                            class="errorWrap text-danger"
                            *ngIf="
                              submitted &&
                              KPIMappingFormArray.controls[i].get('amountPercentage').errors?.max
                            "
                          >
                            <div class="error text-danger">Maximum 99 value are allowed.</div>
                          </div>
                        </td>
                      </tr>
                      <!-- <tr>
                          <td>Activation - inactive</td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                        
                        </tr>
                        <tr>
                          <td>Activation - Suspend</td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                        
                        </tr>
                        <tr>
                          <td>Lead Conversion</td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                        </tr>
                        <tr>
                          <td>Termination(Clouser)</td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                          <td><input type="text" class="form-control"></td>
                        </tr> -->
                      <tr>
                        <td>Total</td>
                        <td></td>
                        <td>
                          <input
                            type="text"
                            readonly
                            [(ngModel)]="totalVal"
                            class="form-control"
                            [ngModelOptions]="{ standalone: true }"
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            readonly
                            [(ngModel)]="totalAmountPer"
                            class="form-control"
                            [ngModelOptions]="{ standalone: true }"
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </fieldset>

              <div class="addUpdateBtn" style="margin: 3.5rem 0 2rem">
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="!isKPIEdit"
                  id="submit"
                  (click)="addEditKPI('')"
                >
                  <i class="fa fa-check-circle"></i>
                  Add KPI
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isKPIEdit"
                  id="submit"
                  (click)="addEditKPI(viewKPIDetailData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update KPI
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12" *ngIf="detailView">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to Charge Details"
            (click)="listCharge()"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">KPI Detail</h3>
        </div>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#KPIDetail"
            aria-expanded="false"
            aria-controls="KPIDetail"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="KPIDetail" class="panel-collapse collapse in">
        <div class="panel-body"></div>
      </div>
    </div>
  </div>
</div>
