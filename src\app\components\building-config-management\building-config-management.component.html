<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCountry"
            aria-expanded="false"
            aria-controls="allDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Building Config Mapping</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let country of countryListData
                        | paginate
                          : {
                              id: 'countryListData',
                              itemsPerPage: countryitemsPerPage,
                              currentPage: currentPageCountrySlab,
                              totalItems: countrytotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ country.name }}</td>
                    <td>{{ country.mappingFrom }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isCountryEdit ? "Update" : "Create" }} {{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCountry"
            aria-expanded="false"
            aria-controls="createDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isCountryEdit">
          Sorry you have not privilege to create operation!
        </div>
        <div class="panel-body" *ngIf="createAccess || (isCountryEdit && editAccess)">
          <form [formGroup]="buildingconfFormGroup">
            <label>{{ title }} Name*</label>
            <input
              id="name"
              type="text"
              class="form-control"
              placeholder="Enter {{ title }} Name"
              formControlName="name"
              [ngClass]="{ 'is-invalid': submitted && buildingconfFormGroup.controls.name.errors }"
              maxLength="250"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && buildingconfFormGroup.controls.name.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && buildingconfFormGroup.controls.name.errors.required"
              >
                {{ title }} Name is required.
              </div>
              <div
                class="position"
                *ngIf="submitted && buildingconfFormGroup.controls.name.errors?.cannotContainSpace"
              >
                <p class="error">White space are not allowed.</p>
              </div>
            </div>
            <br />
            <div>
              <label>{{ title }} Mapping*</label>
              <p-dropdown
                [options]="dunningData"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Select a {{ title }} Mapping"
                formControlName="mappingFrom"
                [disabled]="countryListData && countryListData.length >= 1"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && buildingconfFormGroup.controls.mappingFrom.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && buildingconfFormGroup.controls.mappingFrom.errors.required"
                >
                  {{ title }} Mapping is required.
                </div>
              </div>
              <br />
            </div>
            <div class="addUpdateBtn">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditCountry('')"
                [disabled]="countryListData && countryListData.length >= 1"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ title }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
