import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { DeactivateService } from "src/app/service/deactivate.service";
import { BuidingConfigManagement } from "./building-config-management.component";

const routes = [
  { path: "", component: BuidingConfigManagement, canDeactivate: [DeactivateService] }
];

@NgModule({
  declarations: [BuidingConfigManagement],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule]
})
export class BuildingConfigManagementModule {}
