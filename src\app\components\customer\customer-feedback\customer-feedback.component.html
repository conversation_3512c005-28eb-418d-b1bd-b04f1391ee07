<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Customer Feedback</h3>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panal-body table-responsive">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <p-table [value]="feedbackListData">
              <ng-template pTemplate="header">
                <tr>
                  <th>Event</th>
                  <th>Rating</th>
                  <th>Action</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-knowledgeBase let-i="rowIndex">
                <tr>
                  <td>{{ knowledgeBase.event }}</td>
                  <td>{{ knowledgeBase.avgRating }}/5</td>
                  <td class="btnAction">
                    <button
                      type="button"
                      class="approve-btn"
                      title="Show Feedback"
                      (click)="showknowledgetDocData(knowledgeBase)"
                    >
                      <img src="assets/img/eye-icon.png" />
                    </button>
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Feedback Details"
  [(visible)]="viewFeedbackModel"
  [style]="{ width: '600px' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModal()"
>
  <div class="modal-body">
    <div class="feedback-card" *ngFor="let feedback of feedbackViewData?.feedbackDetails">
      <!-- <div class="feedback-field">
        <label>Event:</label>
        <span>{{ feedback?.event }}</span>
      </div> -->

      <div class="feedback-field rating-field">
        <label>Rating:</label>
        <ng-container *ngIf="feedback.rating !== null; else noRating">
          <span class="star-display">
            <ng-container *ngFor="let i of getStars(feedback.rating, 5)">
              <span [ngClass]="{ filled: i < feedback.rating }">★</span>
            </ng-container>
          </span>
        </ng-container>
        <ng-template #noRating>
          <em class="text-muted">No Rating</em>
        </ng-template>
      </div>

      <div class="feedback-field">
        <label>Feedback Message:</label>
        <p class="feedback-message" [ngClass]="getFeedbackColorClass(feedback.rating)">
          {{ feedback?.feedbackMessage || "—" }}
        </p>
      </div>
      <div class="feedback-field">
        <label>Feedback Date:</label>
        <p class="feedback-message">
          {{ feedback?.createdDate || "—" }}
        </p>
      </div>
    </div>
  </div>

  <div class="modal-footer text-right">
    <button (click)="closeModal()" class="btn btn-danger btn-sm" type="button">Close</button>
  </div>
</p-dialog>
