<!-- <div class="panel-heading">
    <h3 class="panel-title">{{ isChargeEdit ? "Update" : "Create" }} Charge</h3>
    <div class="right">
      <button
        type="button"
        class="btn-toggle-collapse"
        data-toggle="collapse"
        data-target="#createcharge"
        aria-expanded="false"
        aria-controls="createcharge"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div> -->

<div>
  <form [formGroup]="chargeGroupForm">
    <!--    Charge Details    -->
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Charge Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-12">
            <label>Charge Name*</label>
            <input
              id="name"
              type="text"
              class="form-control"
              placeholder="Enter Charge Name"
              formControlName="name"
              [ngClass]="{
                'is-invalid': submitted && chargeGroupForm.controls.name.errors
              }"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.name.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.name.errors.required"
              >
                Charge Name is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-4 col-md-4 col-12">
            <div>
              <label>Charge Category*</label>
              <p-dropdown
                [options]="chargeCategoryList"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Select Charge Category"
                formControlName="chargecategory"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.chargecategory.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && chargeGroupForm.controls.chargecategory.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && chargeGroupForm.controls.chargecategory.errors.required"
                >
                  Charge Category is required.
                </div>
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-4 col-md-4 col-12">
            <div>
              <label>Charge Type*</label>
              <p-dropdown
                [options]="chargeType"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Select a Charge Type"
                formControlName="chargetype"
                [disabled]="isChargeEdit"
                (onChange)="eventChargeType($event)"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.chargetype.errors
                }"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && chargeGroupForm.controls.chargetype.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && chargeGroupForm.controls.chargetype.errors.required"
                >
                  Charge Type is required.
                </div>
              </div>
            </div>
            <br />
          </div>
        </div>

        <div class="row">
          <div class="col-lg-4 col-md-4 col-12" *ngIf="chargeGroupForm.controls.serviceid.enabled">
            <div>
              <label>Service*</label>

              <p-multiSelect
                id="roles"
                [options]="this.commondropdownService.planserviceData"
                placeholder="Select a service"
                formControlName="serviceid"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.serviceid.errors
                }"
              ></p-multiSelect>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && chargeGroupForm.controls.serviceid.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && chargeGroupForm.controls.serviceid.errors.required"
                >
                  Service is required.
                </div>
              </div>
            </div>
            <br />
          </div>

          <div class="col-lg-4 col-md-4 col-12">
            <label>Status*</label>
            <div>
              <p-dropdown
                [options]="statusOptions"
                optionValue="label"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                formControlName="status"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.status.errors
                }"
              ></p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.status.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.status.errors.required"
              >
                Status is required.
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
            <label>Ledger ID</label>
            <input
              id="ledgerId"
              type="text"
              class="form-control"
              placeholder="Enter Ledger ID"
              formControlName="ledgerId"
            />
          </div>
          <div class="col-lg-4 col-md-4 col-12">
            <label>Charge Description*</label>
            <textarea
              class="form-control"
              placeholder="Enter Charge Description"
              rows="3"
              formControlName="desc"
              name="desc"
              [ngClass]="{
                'is-invalid': submitted && chargeGroupForm.controls.desc.errors
              }"
            ></textarea>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.desc.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.desc.errors.required"
              >
                Charge Description is required.
              </div>

              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.desc.errors.pattern"
              >
                Maximum 225 charecter required.
              </div>
            </div>
            <br />
          </div>
        </div>
        <div class="row" *ngIf="chargeGroupForm.controls.royalty_payable.enabled">
          <div
            class="col-lg-4 col-md-4 col-12"
            *ngIf="
              this.chargeGroupForm.value.chargetype == 'ADVANCE' ||
              this.chargeGroupForm.value.chargetype == 'RECURRING'
            "
          >
            <label>Royalty Payable*</label>
            <div>
              <p-dropdown
                [options]="royaltyPayableData"
                optionValue="value"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Royalty Payable"
                formControlName="royalty_payable"
                [disabled]="isChargeEdit"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.royalty_payable.errors
                }"
              ></p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.royalty_payable.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.royalty_payable.errors.required"
              >
                Royalty Payable is required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>

    <!--    Additional Details    -->
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Additional Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-12">
            <label>Actual Price*</label>
            <input
              *ngIf="isChargeEdit"
              id="actualprice"
              type="number"
              min="1"
              class="form-control"
              placeholder="Enter Actual Price"
              formControlName="actualprice"
              readonly
            />

            <input
              *ngIf="!isChargeEdit"
              id="actualprice"
              type="number"
              min="1"
              class="form-control"
              placeholder="Enter Actual Price"
              formControlName="actualprice"
              (keyup)="onKey($event)"
              [ngClass]="{
                'is-invalid': submitted && chargeGroupForm.controls.actualprice.errors
              }"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.actualprice.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.actualprice.errors.required"
              >
                Actual Price is required.
              </div>
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.actualprice.errors.pattern"
              >
                Only numeric charcter allowed.
              </div>
            </div>

            <div class="error text-danger" *ngIf="chargeValueSentence">
              {{ chargeValueSentence }}
            </div>
            <br />
          </div>
          <div class="col-lg-4 col-md-4 col-12">
            <label>SAC Code*</label>
            <input
              id="saccode"
              type="text"
              class="form-control"
              placeholder="Enter SAC Code"
              formControlName="saccode"
              [ngClass]="{
                'is-invalid': submitted && chargeGroupForm.controls.saccode.errors
              }"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.saccode.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.saccode.errors.required"
              >
                SAC Code is required.
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-4 col-md-4 col-12">
            <label>Tax*</label>
            <div>
              <p-dropdown
                [options]="taxListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a Tax"
                formControlName="taxid"
                (onChange)="taxRang($event)"
                [disabled]="isChargeEdit"
                [ngClass]="{
                  'is-invalid': submitted && chargeGroupForm.controls.taxid.errors
                }"
              >
                <ng-template let-data pTemplate="item">
                  <div class="item-drop1">
                    <span class="item-value1">
                      {{ data.name }}
                      <span *ngFor="let row of data.tieredList">
                        ( {{ row.name }} {{ row.rate }}%)</span
                      >
                    </span>
                  </div>
                </ng-template>
              </p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && chargeGroupForm.controls.taxid.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && chargeGroupForm.controls.taxid.errors.required"
              >
                Tax is required.
              </div>
            </div>
            <br />
          </div>
        </div>
      </div>
    </fieldset>

    <div class="addUpdateBtn" style="margin: 3.5rem 0 2rem">
      <!-- <button
                type="submit"
                class="btn btn-primary"
                *ngIf="
                  !isChargeEdit &&
                  loginService.hasOperationPermission(
                    AclClassConstants.ACL_CHARGE,
                    AclConstants.OPERATION_CHARGE_ADD,
                    AclConstants.OPERATION_CHARGE_ALL
                  )
                "
                id="submit"
                (click)="addEditCharge('')"
              >
                <i class="fa fa-check-circle"></i>
                Add Charge
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="
                  isChargeEdit &&
                  loginService.hasOperationPermission(
                    AclClassConstants.ACL_CHARGE,
                    AclConstants.OPERATION_CHARGE_EDIT,
                    AclConstants.OPERATION_CHARGE_ALL
                  )
                "
                id="submit"
                (click)="addEditCharge(viewChargeListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update Charge
              </button> -->
      <button class="btn btn-primary" (click)="saveChargeDto()" type="submit">Save Charge</button>
      <br />
    </div>
  </form>
</div>
