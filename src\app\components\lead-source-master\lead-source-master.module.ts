import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { LeadSourceMasterComponent } from "./lead-source-master.component";
import { DeactivateService } from "src/app/service/deactivate.service";

const routes = [
  { path: "", component: LeadSourceMasterComponent, canDeactivate: [DeactivateService] },
];

@NgModule({
  declarations: [LeadSourceMasterComponent],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],
})
export class LeadSourceMasterModule {}
