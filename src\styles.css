/* You can add global styles to this file, and also import other style files */

@import "~@ng-select/ng-select/themes/default.theme.css";
@import "~@angular/material/prebuilt-themes/deeppurple-amber.css";
/* @import "~@ng-select/ng-select/themes/material.theme.css"; */

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

#navbar ul i {
  margin-right: -8px;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

a .icon {
  width: 30px;
  height: 30px;
}

label {
  font-size: 14px;
}

.menu-button {
  top: 0;
  left: 0;
  z-index: 1000;
  position: fixed;
}

.right {
  padding-left: 5px !important;
}

.left {
  padding-right: 5px !important;
}

.top {
  margin-bottom: 10px !important;
}

.searchbtn {
  text-align: center;
}

.searchForm .searchbtn button {
  margin: 0.5%;
}

.childComponent {
  background-color: #dddddd;
}

.example-full-width {
  width: 100%;
  height: 10%;
}

.addUpdateBtn {
  text-align: center;
  margin-top: 3.5rem;
}

.btnAction a {
  margin-right: 3px;
}

.wlan-guest-box {
  border-right: none !important;
}

.is-active {
  border-bottom: 5px solid #f7b206;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background: #ffffff;
  border-color: #f7b206 !important;
  color: #f7b206 !important;
}

.p-tabview .p-tabview-nav li {
  margin-right: 0;
  width: 33.33%;
}

.p-button1 {
  background-color: #f7b206 !important;
  border-color: #f7b206 !important;
  color: #000 !important;
}

/* 
.p-button.p-button-text {
  color: #000 !important;
} */

.p-component {
  font-size: 1.5rem !important;
}

.p-dialog .p-dialog-header .p-dialog-title {
  font-size: 1.5rem !important;
  color: #000;
}

.p-inputtext {
  font-size: 14px !important;
}

.p-dropdown {
  width: 100%;
  /* height: 100%; */
  background: #fcfcfc !important;
  box-shadow: 0px 1px 2px 0 rgb(0 0 0 / 10%) !important;
  border-color: #eaeaea !important;
}

.p-multiselect {
  width: 100% !important;
}

.panel .table > thead > tr > td:first-child,
.panel .table > thead > tr > th:first-child,
.panel .table > tbody > tr > td:first-child,
.panel .table > tbody > tr > th:first-child,
.panel .table > tfoot > tr > td:first-child,
.panel .table > tfoot > tr > th:first-child {
  padding-left: 0px;
}

.popover {
  font-size: 15px;
}

.popover-title {
  font-size: 15px;
}

.popover .confirm-btns .confirm-btn-container .btn.btn-block.btn-outline-secondary {
  color: #fff;
  background-color: #000;
  border-color: #000;
}

.popover .confirm-btns .confirm-btn-container .btn.btn-block.btn-danger {
  transition: none;
  color: white;
  /* background-color: yellow ; */
  background-color: #f7b206 !important;
  border-color: #f7b206 !important;
}

.ngx-pagination .current {
  background-color: #f7b206 !important;
}

.ngx-pagination > li {
  border: 1px solid #ddd !important;
}

.ngx-pagination {
  padding-left: 0;
}

.sidebar .nav > li > a[data-toggle="collapse"] .icon-submenu {
  float: right;
}

table {
  border-collapse: collapse;
  table-layout: fixed;
}

table td {
  word-wrap: break-word !important;
}

#searchbtn {
  margin-left: 5px;
}

.detailOnAnchorClick {
  cursor: pointer;
}

td a:hover {
  color: #f7b206;
}

.coa-table {
  border: 1px solid #ddd !important;
}

#viewDetail {
  word-wrap: break-word;
}

.invalid-feedback {
  color: red;
}

.is-invalid {
  border-color: red;
}

.selected-row-background {
  background-color: #dddddd;
  /* background-color: #c3e6cb; */
  /* background-color: #d6d8db; */
  /* background-color: #bee5eb; */
  /* background-color: #ffeeba;  */
}

.remove-row-background {
  background-color: white;
}

.modal-header {
  background: #f7b206 !important;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.statusbtn {
  margin-right: 3px;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.ulleft {
  margin-left: -22px !important;
}

#selectArea .mat-form-field {
  display: inline-block;
  position: relative;
  text-align: left;
  width: 100%;
}

#selectArea .mat-form-field-infix {
  border-top: 0;
}

#selectArea .mat-form-field-appearance-legacy .mat-form-field-underline {
  height: 0;
}

#selectArea .mat-form-field-appearance-legacy .mat-form-field-label {
  top: 2em;
  left: 15px;
}

.curson_pointer {
  cursor: pointer;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.boxWhite {
  border: 2px solid #ddd;
  background: white !important;
  border-radius: 4px;
  padding: 1.5rem;
}

legend {
  width: auto !important;
  display: block;
  padding-inline-start: 2px !important;
  padding-inline-end: 2px !important;
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  margin-bottom: 0;
  font-size: 21px;
  line-height: inherit;
  color: #7f7b7bed;
}

fieldset {
  margin-top: 3.5rem;
  display: block;
  margin-inline-start: 2px;
  margin-inline-end: 2px;
  padding-block-start: 0.35em;
  padding-inline-start: 0.75em;
  padding-inline-end: 0.75em;
  padding-block-end: 0.625em;
  min-inline-size: min-content;
  border-width: 2px;
  border-style: solid;
  border-color: #ddd;
  border-radius: 4px;
  background-color: rgb(176 172 172 / 14%);
}

.discInfo {
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn:focus {
  outline: 0;
  outline-offset: 0;
}

:focus-visible {
  outline: 0;
}

#Createradius {
  width: 100%;
  min-height: 2vh;
  max-height: 26rem;
  overflow-y: scroll;
}

.scrollbardocument {
  width: 100%;
  height: 200px;
  overflow-y: scroll;
  border: 1px solid #b1aaaaab;
}

.scrollbarPlangroupMappingList {
  border: none;
  width: 100%;
  min-height: 2vh;
  max-height: 45vh;
  overflow-y: scroll;
}

.scrollbarInvoiceAmount {
  border: none;
  width: 100%;
  min-height: 2vh;
  max-height: 50vh;
  overflow-y: scroll;
}

.btn-success:active {
  color: #fff;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.marginTopSearchBtn {
  margin: 0;
}

.marginTopSearchinput {
  margin: 0;
}

select[readonly]:-moz-read-only {
  /* For Firefox */
  pointer-events: none;
}

select[readonly]:read-only {
  pointer-events: none;
}

.btn:active:focus,
.btn:focus {
  outline: 0;
  outline-offset: 0;
}

.submenuIcon {
  width: 18px;
  margin-right: 8px;
}

.sidebar .nav .nav > li > a {
  padding-left: 45px;
  padding-top: 10px;
  padding-bottom: 10px;
}

@media only screen and (max-width: 992px) {
  .marginTopSearchBtn {
    margin: 1.5rem -5px 0;
  }

  .marginTopSearchinput {
    margin: 1.5rem 0 0;
  }
}

#itemPerPageDropdown .p-dropdown {
  border-radius: 0px;
  margin-left: 1rem;
  height: 27px;
}

#itemPerPageDropdown .p-dropdown .p-dropdown-label {
  color: black;
  font-weight: 400;
  margin-top: -3px;
}

#itemPerPageDropdown .p-dropdown .p-dropdown-panel {
  top: -167px !important;
}

a .icon-ex {
  width: 40px;
  height: 35px;
}

.p-toast {
  z-index: 100001 !important;
}

.displayflex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.displayflexdis {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: #eee;
  cursor: not-allowed;
  color: #eee;
}

.displayflex button {
  margin-right: 12px;
  box-shadow: none;
  margin-left: 0;
}

.panel .panel-heading .panel-title {
  font-size: 24px;
}

.singleMenu {
  width: 25px;
  margin-right: 10px;
}

.activeMenu {
  width: 25px;
  margin-right: 10px;
}

a.active img.navi {
  display: none;
}

a img.navi {
  display: none;
}

a.collapsed img.navi {
  display: inline-flex;
}

a.active img.activeMenu {
  display: inline-flex;
}

a.collapsed img.activeMenu {
  display: none;
}

.IconVerifyBtn {
  background-color: #f6b205;
}

ng-select.is-invalid .ng-select-container {
  border-color: red !important;
}

#wrapper .main {
  min-height: 100vh !important;
}

/*............. side bar............ */
.sidebarMenu {
  width: 230px !important;
}

.sidebarIcon {
  width: 80px !important;
}

.IconHideMain {
  width: calc(100% - 80px) !important;
}

.IconWidthMain {
  width: calc(100% - 230px) !important;
}

.paddingLeftsubmenu {
  padding-left: 28px !important;
}

.paddingLeft45PX {
  padding-left: 45px !important;
}

.innerMenuICON {
  padding-left: 40px !important;
}

.paddingmenuIcon {
  padding-top: 5px;
  margin-right: -8px;
}

.sidebar-scroll {
  height: 100% !important;
}

#accordion {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

#accordion > li {
  display: block;
}

#accordion li a {
  padding: 18px 10px 18px 10px;
  font-size: 14px;
}

#accordion > li > a {
  padding: 18px 30px;
  color: #606060;
  border-top: 1px solid #f4f4f4;
}

#accordion > li > a {
  display: block;
  padding: 10px 15px;
}

li a img.navi {
  width: 25px;
}

#accordion > li > a i {
  margin-right: -11px;
  font-size: 15px;
  font-weight: normal;
}

#accordion li > a > i > .icon-submenu {
  margin-right: 0;
}

#accordion li > a[data-toggle="collapse"] .icon-submenu {
  float: right;
}

.sidebar #style-1 #accordion > li > a:hover,
.sidebar #style-1 #accordion > li > a:focus {
  color: #f7b206;
  background-color: transparent;
}

#accordion > li > a:hover {
  text-decoration: none;
}

.sidebar #style-1 #accordion > li > a:focus {
  background-color: #494948;
}

.sidebar #style-1 #accordion > li > a.active[aria-expanded="true"] {
  background-color: #494948 !important;
  color: #f7b206;
}

.sidebar #style-1 #accordion > li > a[aria-expanded="true"] {
  color: #f7b206;
  background-color: #494948 !important;
}

.sidebar #style-1 #accordion > li > a[aria-expanded="false"] {
  background-color: transparent !important;
  color: #606060;
}

#submenuNavbar {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

#submenuNavbar > li {
  display: block;
}

#submenuNavbar li a {
  padding: 18px 10px 18px 10px;
  font-size: 14px;
}

#submenuNavbar > li > a {
  padding: 18px 30px;
  color: #606060;
  border-top: 1px solid #f4f4f4;
}

#submenuNavbar > li > a {
  display: block;
  padding: 10px 15px;
}

#submenuNavbar > li > a:hover,
#submenuNavbar > li > a:focus,
#submenuNavbar > li > a.active span {
  color: #f7b206;
  background-color: transparent;
}

/* .................. */
/* tooltip side bar  */

li a .tooltips {
  position: absolute;
  visibility: hidden;
  min-width: 143px;
  max-width: 300px;
  padding: 5px;
  background: #494948 !important;
  right: -145px;
  border-radius: 5px;

  text-align: center;
  z-index: 1;
  transition: 0.3s;
  transform: translate3d(0px, 20px, 0px);
}

li a:hover .tooltips {
  visibility: visible;
  opacity: 1;
  transform: translate3d(0px, 0px, 0px);
}

/* ....................... */

/* scroll side bar */
.scrollbar {
  overflow-y: auto;
  height: 75vh;
  flex-direction: column;
  overflow-x: hidden;
}

.scrollbarIcon {
  overflow-y: auto;
  height: 85vh;
  flex-direction: column;
  overflow-x: hidden;
}

#style-1::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgb(202, 198, 198);
  border-radius: 10px;
}

#style-1::-webkit-scrollbar {
  width: 3px;
  background-color: #dddddd;
}

#style-1::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgb(138, 133, 133);
}

/* ..................... */
#accordion .panel {
  margin-bottom: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

@media screen and (max-width: 1024px) {
  #wrapper .sidebar {
    left: 0px;
  }
}

::ng-deep .mat-tooltip {
  font-size: 15px;
  white-space: pre-wrap;
  border: 1px solid #494948;
  max-width: unset !important;
  background-color: #494948;
  color: #f7b206;
}

@media screen and (max-width: 1706px) {
  .scrollbar {
    height: 69vh;
  }
}

@media screen and (max-width: 1396px) {
  .scrollbar {
    height: 66vh;
  }
}

@media screen and (max-width: 1356px) {
  .scrollbar {
    height: 65vh;
  }
}

@media screen and (max-width: 1228px) {
  .scrollbar {
    height: 60vh;
  }
}

@media screen and (max-width: 1024px) {
  .scrollbar {
    height: 55vh;
  }
}

.HoverEffect {
  color: #f7b206;
  /* font-weight: 500; */
}

.HoverEffect:hover {
  cursor: pointer;
}

.networkdeviceTR:hover {
  background-color: #6060600d;
  cursor: pointer;
}

.searchLocationModalWidth {
  width: 45%;
}

.nearSearchModalLocation {
  width: 63%;
}

.LocationIcon {
  height: 30px;
  width: 30px;
}

.LocationIconMargin {
  margin-top: 2.8rem;
}

.pagination_Dropdown {
  display: flex;
}

@keyframes p-progress-spinner-color {
  100%,
  0% {
    stroke: #d62d20;
  }

  40% {
    stroke: #0057e7;
  }

  66% {
    stroke: #008744;
  }

  80%,
  90% {
    stroke: #ffa700;
  }
}

.table-responsive {
  min-height: 0.01%;
  overflow-x: initial !important;
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px !important;
}

.activeSubMenu {
  border-bottom: 3px solid #f7b206 !important;
}
.inactiveSubMenu {
  border-bottom: 1px solid #000 !important;
}

.pcol .dbox {
  min-height: 47px;
  display: flex;
}

.panel-udata .pcol {
  padding: 5px 5px 0;
}

.pcol .dbox a {
  display: flex;
}

.dbox h5 {
  margin-left: 1.5rem;
  color: #5f5f5f;
  margin-top: 7px;
  font-weight: 500;
  margin-bottom: 0px;
  font-size: 17px;
}

.pcol .dbox a img {
  width: 30px !important;
  height: 30px !important;
}

.pcol .dbox a i {
  font-size: 3rem !important;
}

.btnAction a img {
  height: 25px !important;
  width: 25px !important;
  margin-bottom: 2px;
}

.btnAction button img {
  height: 25px !important;
  width: 25px !important;
  margin-bottom: 2px;
}

.btn {
  border-radius: 25px;
}

.widthCheckboxColom {
  width: 3% !important;
}

@media only screen and (max-width: 1920px) {
  .btnAction a img {
    height: 25px !important;
    width: 25px !important;
  }

  .btnAction button img {
    height: 25px !important;
    width: 25px !important;
  }
}

.approve-btn {
  box-shadow: none;
  border: none;
  padding: 0 4px 0 0;
  background: white;
}

@media only screen and (max-width: 1536px) {
  .btnAction a img {
    height: 25px !important;
    width: 25px !important;
  }

  .btnAction button img {
    height: 25px !important;
    width: 25px !important;
  }

  .sidebarMenu {
    width: 195px !important;
  }

  li a img.navi {
    width: 20px;
  }

  .sidebarIcon {
    width: 75px !important;
  }

  .IconHideMain {
    width: calc(100% - 75px) !important;
  }

  #accordion li a {
    padding: 13px 7px 8px 9px;
    font-size: 12px;
  }

  #submenuNavbar li a {
    padding: 11px 8px 8px 8px;
    font-size: 11px;
  }

  .IconWidthMain {
    width: calc(100% - 195px) !important;
  }

  #accordion > li > a i {
    margin-right: -6px;
    font-size: 11px;
    font-weight: normal;
  }

  .singleMenu {
    width: 20px;
    margin-right: 10px;
  }

  .table > tbody > tr > td,
  .table > tbody > tr > th,
  .table > tfoot > tr > td,
  .table > tfoot > tr > th,
  .table > thead > tr > td,
  .table > thead > tr > th {
    font-size: 12px;
  }

  .panel .table > thead > tr > th:last-child,
  .panel .table > tbody > tr > td:last-child,
  .panel .table > tbody > tr > th:last-child,
  .panel .table > tfoot > tr > td:last-child,
  .panel .table > tfoot > tr > th:last-child {
    padding-left: 12px;
  }

  .pcol .dbox {
    min-height: 36px;
  }

  .badge {
    padding: 4px 4px;
    font-size: 9px;
    font-weight: 500;
    min-width: 60px;
  }

  .pcol .dbox a img {
    width: 24px !important;
    height: 25px !important;
  }

  .pcol .dbox a i {
    font-size: 2rem !important;
  }

  .dbox h5 {
    font-weight: 500;
    margin-bottom: 0px;
    font-size: 17px;
    margin-left: 1rem;
    margin-top: 2px;
  }
}

@media only screen and (max-width: 1397px) {
  .widthCheckboxColom {
    width: 5% !important;
  }

  .dbox h5 {
    font-weight: 500;
    margin-bottom: 0px;
    font-size: 13px;
  }

  .panel-udata .pcol .dbox a img {
    vertical-align: middle;
    width: 17px !important;
    height: 17px !important;
  }
}

@media only screen and (max-width: 1396px) {
  .btnAction a img {
    height: 25px !important;
    width: 25px !important;
  }

  .btnAction button img {
    height: 25px !important;
    width: 25px !important;
  }
}

my-app {
  display: block;
  box-sizing: border-box;
  padding: 30px;
}

my-app > .k-icon.k-i-loading {
  font-size: 64px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.example-wrapper {
  min-height: 280px;
  align-content: flex-start;
}

.example-wrapper p,
.example-col p {
  margin: 0 0 10px;
}

.example-wrapper p:first-child,
.example-col p:first-child {
  margin-top: 0;
}

.example-col {
  display: inline-block;
  vertical-align: top;
  padding-right: 20px;
  padding-bottom: 20px;
}

.example-config {
  margin: 0 0 20px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.event-log {
  margin: 0;
  padding: 0;
  max-height: 100px;
  overflow-y: auto;
  list-style-type: none;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #fff;
}

.event-log li {
  margin: 0;
  padding: 0.3em;
  line-height: 1.2em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.event-log li:last-child {
  margin-bottom: -1px;
}

#templateTabel thead tr th {
  color: #495057;
  border: solid #e9ecef;
  border-width: 0 0 1px;
  padding: 1rem;
  font-weight: 600;
}

#templateTabel tbody > tr > td {
  text-align: left;
  border: solid #e9ecef;
  border-width: 0 0 1px;
  padding: 1rem;
}

#templateTabel {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
}

#templateTabel thead > tr > th,
#templateTabel tbody > tr > td {
  text-align: left;
  transition: box-shadow 0.2s;
}

#templateTabel thead > tr > th,
#templateTabel tbody > tr > td {
  border-width: 1px;
}

#templateTabel tbody > tr {
  background: #fff;
  color: #495057;
  transition: box-shadow 0.2s;
  outline-color: #a6d5fa;
}

#templateTabel tbody > tr > td {
  text-align: left;
  border: solid #e9ecef;
  /* border-width: 0 0 1px; */
  padding: 1rem;
}

#templateTabel tbody > tr > td > textarea {
  width: 100% !important;
  min-height: 8rem;
}

.p-organizationchart.company .p-organizationchart-line-top {
  border-top: 1px solid black !important;
  border-color: black !important;
}

.p-organizationchart.company .p-organizationchart-line-left {
  border-right: 1px solid black !important;
  border-color: black !important;
}

.p-organizationchart.company .p-organizationchart-line-down {
  background: black !important;
  margin-top: 0.5rem !important;
}

.p-organizationchart.company .p-organizationchart-node-content {
  padding: 0 !important;
}

.p-organizationchart.company .node-header {
  padding: 1rem !important;
}

.p-organizationchart.company .node-header {
  background-color: white !important;
  border: 1px solid black !important;
  font-size: 15px !important;
  width: 147px !important;
  font-weight: 500 !important;
  color: black !important;
}

.p-organizationchart.company .p-organizationchart-node-content .p-node-toggler {
  background: white !important;
  color: black;
  border: 1px solid black !important;
  border-radius: 50%;
}

.p-organizationchart.company .p-organizationchart-line-down {
  height: 38px !important;
  margin: 0 auto;
  width: 1px;
}

.p-organizationchart.company .p-organizationchart-node-content.p-highlight .p-node-toggler i {
  color: black !important;
}

.p-organizationchart.company .p-organizationchart-node-content .p-node-toggler {
  bottom: -1.75rem !important;
}

.p-organizationchart.company
  .p-organizationchart-node-content
  .p-node-toggler
  .p-node-toggler-icon {
  position: relative;
  top: -0.5rem !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

/* .pi-chevron-down:before {
  content: '\002D' !important;
} */

/* .pi-chevron-up:before {
  content: '\002B' !important;
} */
.p-organizationchart.company
  .p-organizationchart-node-content
  .p-node-toggler
  .p-node-toggler-icon.pi-chevron-down:before {
  content: "\002D" !important;
}

.p-organizationchart.company
  .p-organizationchart-node-content
  .p-node-toggler
  .p-node-toggler-icon.pi-chevron-up:before {
  content: "\002B" !important;
}

span.p-dropdown-trigger-icon.pi.pi-chevron-down.pi-chevron-down:before {
  content: "\e902" !important;
}

#networkDeviceTabel .table > tbody > tr > td {
  border-top: none;
}

/* network */

.networkdetailsWidth {
  width: 50%;
}

.networkHiearachyWidth {
  width: 72%;
}

.p-rating .p-rating-icon {
  font-size: 2rem !important;
}

.p-rating .p-rating-icon.pi-star {
  color: #f7b206 !important;
}

.dataGroup .p-rating {
  display: inline-block;
  padding-left: 10px;
  vertical-align: middle;
}

/* .............rotate............... */
.rotate {
  animation: rotation 8s infinite linear;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

/* ....................... */
.offlineStatus {
  color: #0000009c !important;
  background-color: #bbbfc240 !important;
  padding: 6px 13px;
  border-radius: 17px;
  box-shadow: 0px 1px 2px 0 rgb(0 0 0 / 20%);
}

/* ....................... */
.inputPassword {
  border: none !important;
  box-shadow: none !important;
  width: 98% !important;
  padding-left: 0px !important;
}

i.fa.fa-eye {
  cursor: pointer;
}

i.fa.fa-eye-slash {
  cursor: pointer;
}

/* ....................... */
a:focus {
  outline: 0;
}

.p-multiselect-label {
  cursor: pointer;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal !important;
  height: 32px !important;
}

/* ....................... */

/* .p-dropdown {
  margin-bottom: 2rem;
} */

.customerplanShowCSS {
  display: block;
}

.customerplanHideCSS {
  display: none;
}

.leadplanShowCSS {
  display: block;
}

.leadplanHideCSS {
  display: none;
}

.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 1px solid #ddd;
}

.p-dropdown .p-dropdown-label.p-placeholder {
  color: #999 !important;
}

.stffMvnoEditCSS {
  display: none;
}

/* ....... / / ..... */

.panel {
  min-height: 4rem !important;
}

button.btn-toggle-collapse i.fa-minus-circle:before {
  content: "\f056";
}

button.btn-toggle-collapse.collapsed i.fa-minus-circle:before {
  content: "\f067";
}

.p-dropdown.p-disabled {
  opacity: 1;
  background: #eee !important;
}

.disableDropdown.p-dropdown {
  width: calc(100% - 90px) !important;
}

.disabledCustSubmenu {
  background: #eee;
  opacity: 0.5;
  cursor: not-allowed;
}

.disabledCustSubmenu a {
  cursor: not-allowed;
}

.p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: #f7b206 !important;
  background: #f7b206 !important;
}

.p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
  width: 5px !important;
  height: 5px !important;
  transition-duration: 0.2s;
  background-color: #ffffff;
}

.p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
  border-color: #f7b206 !important;
}

#deletecharge .p-datepicker table {
  border-collapse: collapse;
  width: 100% !important;
}

#deletecharge .p-datepicker table th {
  padding: 0.5rem;
  text-align: center;
}

.approve-btn {
  box-shadow: none;
  border: none;
  padding: 0 4px 0 0;
}

.approve-btn[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.yellowBtn {
  background-color: #f7b206 !important;
  font-size: 16px;
  padding: 3px 12px;
  margin: 10px 12px 10px 0;
  border: none;
  color: white;
  font-weight: 400;
  border-radius: 14px;
  min-width: 80px;
}

#edit-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#addNotes[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#approveLead[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#rejectLead[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#closeLead[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#operateLeadStatus[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

#reopenLead[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

/* tabel */
.gridtable tbody {
  display: block;
  max-height: 266px;
  overflow-y: scroll;
}

.gridtable thead,
.gridtable tbody tr {
  display: table;
  table-layout: fixed;
  width: 100%;
}

.gridtable thead {
  width: calc(100% - 1.1em);
}

.gridtable thead {
  position: relative;
}

.gridtable thead th:last-child:after {
  content: " ";
  position: absolute;
  background-color: #337ab7;
  height: 38px;
  right: -1.3em;
  top: 0;
  border-bottom: 2px solid #ddd;
}

.gridtable .table > thead > tr > th {
  text-align: center;
}

.p-multiselect.p-disabled {
  opacity: 1;
  background: #eee !important;
}

/*  */

.inputcheckboxCenter {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.inputcheckbox {
  background: #ffffff;
  width: 20px;
  height: 20px;
  color: #495057;
  border-radius: 3px;
  transition:
    background-color 0.2s,
    color 0.2s,
    border-color 0.2s,
    box-shadow 0.2s;
  border: 2px solid #ced4da;
}

.networkLabel {
  font-size: 14px;
  font-weight: 600;
}
@media only screen and (min-width: 1240px) {
  .modal-lg.modalXl {
    width: 1200px;
  }
}
.p-panel .p-panel-content {
  height: 100%;
  margin-bottom: 20px;
}
.padding-non {
  padding: 0;
}

.custTable.p-datatable .p-datatable-thead > tr > th {
  font-weight: bold;
  color: #676a6d;
  background: transparent;
  word-break: break-word;
  font-size: 14px;
  font-family:
    Roboto,
    Helvetica Neue,
    sans-serif;
}

.custTable.p-datatable table {
  font-family:
    Roboto,
    Helvetica Neue,
    sans-serif;
  font-size: 14px;
}

.staggImg {
  border-radius: 73px;
  height: 138px;
  width: 138px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border: 3px solid #ddd;
}

.backbtn {
  box-shadow: none !important;
  margin-right: 1.5rem;
}

input[type="checkbox"][readonly] {
  pointer-events: none;
}

.myclass {
  border: 1px solid #dadad6;
  text-align: center;
}

.checkBoxMainWrap {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
}

.checkBoxWrap {
  margin-right: 15px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.checkBoxWrap label {
  margin-bottom: 0;
  margin-left: 10px;
}

.chipsInttxt .p-chips {
  display: block;
  width: 100%;
}

.chipsInttxt .p-chips .p-inputtext {
  padding: 6px 12px;
  font-size: 14px !important;
  color: #555;
  border: 1px solid #ccc;
  background: none;
  box-shadow: 0px 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.chipsInttxt .p-chips .p-chips-token {
  margin-bottom: 5px;
}
.p-badge.p-badge-success {
  background-color: #22c55e !important;
  color: #ffffff;
}
.p-badge.p-badge-xl {
  font-size: 2.5rem !important;
  min-width: 3rem;
  height: auto !important;
  padding: 10px !important;
  line-height: 3rem;
}

/* Button Group Switch
   ========================================================================== */

.switch {
  display: inline-block;
  margin-bottom: 0.5rem;
}

.switch-button {
  /* background and border when in "off" state */
  background: #f7b206;
  border: 2px solid #f7b206;
  display: grid;
  grid-template-columns: 1fr 1fr;
  border-radius: 6px;
  color: #ffffff;
  position: relative;
  cursor: pointer;
  outline: 0;
  user-select: none;
}

.switch-input:not(:checked) + .switch-button .switch-button-left {
  /* text color when in "off" state */
  color: #f7b206;
}

.switch-input {
  display: none;
}

.switch-button span {
  font-size: 1.5rem;
  padding: 0.2rem 0.7rem;
  text-align: center;
  z-index: 2;
  color: #ffffff;
  transition: color 0.2s;
}

.switch-button::before {
  content: "";
  position: absolute;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  border-radius: 4px;
  top: 0;
  left: 0;
  height: 100%;
  width: 50%;
  z-index: 1;
  transition:
    left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1),
    padding 0.2s ease,
    margin 0.2s ease;
}

.switch-button:hover::before {
  will-change: padding;
}

.switch-button:active::before {
  padding-right: 0.4rem;
}

/* "On" state
   ========================== */

.switch-input:checked + .switch-button {
  /* background and border when in "on" state */
  background-color: #606060;
  border-color: #606060;
}

.switch-input:checked + .switch-button .switch-button-right {
  /* text color when in "on" state */
  color: #606060;
}

.switch-input:checked + .switch-button::before {
  left: 50%;
}

.switch-input:checked + .switch-button:active::before {
  margin-left: -0.4rem;
}

/* Checkbox in disabled state
   ========================== */

.switch-input[type="checkbox"]:disabled + .switch-button {
  opacity: 0.6;
  cursor: not-allowed;
}

a:focus {
  outline: 0;
}

.captcha {
  box-shadow: none !important;
  width: auto !important;
}

audio,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
  width: 100% !important;
}

canvas {
  display: inline-block;
  vertical-align: baseline;
  width: 80% !important;
}

.cpt-btn {
  font-size: 24px !important;
}

.btn {
  border-radius: 12px;
}

.p-dialog .p-dialog-header {
  background: #f7b206 !important;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  color: #000;
}

.p-dialog.p-confirm-dialog .p-confirm-dialog-message {
  margin-left: 1rem;
  margin-top: 1rem;
}

.p-dialog.p-confirm-dialog .p-confirm-dialog-icon {
  font-size: 2rem;
  margin-top: 1rem;
}

/* ----------------------------------------------------------------------- */
/* Add custom styles for FullCalendar */
.custom-calendar {
  border: 1px solid grey;
}

.custom-calendar .fc-header-toolbar {
  background-color: #f7b206;
  color: white;
  padding: 10px;
  font-size: 16px;
}

.custom-calendar .fc-daygrid-event {
  background-color: #3788d8;
  color: #fff;
  border-radius: 5px;
  padding: 2px;
  text-align: left;
  font-size: 12px;
}

.custom-calendar .fc-daygrid-day {
  background-color: #f8f9fa;
  border: 1px solid #e3e3e3;
}

.custom-calendar .fc-daygrid-day.fc-day-today {
  background-color: #e9ecef;
  font-weight: bold;
}

.custom-calendar .fc-toolbar-title {
  font-family: "Arial", sans-serif;
  font-weight: bold;
  font-size: 18px;
  color: white !important;
}

.custom-calendar .fc-button {
  background-color: #007bff;
  color: white;
  border-radius: 5px;
  border: none;
}

.custom-calendar .fc-button:hover {
  background-color: #0056b3;
}

.fc-header-toolbar {
  background-color: #f7b206;
  color: white;
  padding: 10px;
  font-size: 16px;
}
.fc-toolbar-chunk {
  display: flex !important;
}

.grid-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.grid-item {
  padding: 10px;
  text-align: center;
  font-size: 16px;
  color: #333;
}

.h2header {
  text-align: center;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .grid-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-list {
    grid-template-columns: 1fr;
  }
}

.msg-wrapper {
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
}
