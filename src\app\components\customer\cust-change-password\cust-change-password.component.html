<p-dialog
  header="Change Password"
  [(visible)]="displayChangePassword"
  [style]="{ width: '30%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="changePasswordForm" name="changePasswordForm">
      <div class="form-group">
        <label for="newpassword">New Password :</label>
        <div class="form-control displayflex">
          <div style="width: 95%">
            <input
              [type]="_passwordNewType"
              class="inputPassword"
              formControlName="newpassword"
              id="password"
              name="newpassword"
              placeholder="Enter New Password"
            />
          </div>
          <div style="width: 5%">
            <div *ngIf="showNewPassword">
              <i
                (click)="showNewPassword = false; _passwordNewType = 'password'"
                class="fa fa-eye"
              ></i>
            </div>
            <div *ngIf="!showNewPassword">
              <i
                (click)="showNewPassword = true; _passwordNewType = 'text'"
                class="fa fa-eye-slash"
              ></i>
            </div>
          </div>
        </div>
        <!-- <input
              type="password"
              class="form-control"
              id="newpassword"
              placeholder="Enter New Password"
              name="newpassword"
              formControlName="newpassword"
            /> -->
      </div>
      <!-- <div class="form-group">
                        <label for="selfcarepwd">Confirm New Password :</label>
                        <input type="password" class="form-control" id="selfcarepwd"
                            placeholder="Enter new password again" name="selfcarepwd" formControlName="selfcarepwd" />
                        <div *ngIf="changeSubmitted ">
                            <div *ngIf="changePasswordForm.controls.selfcarepwd.value.length > 0 ">
                                <div *ngIf=" changePasswordForm.controls.selfcarepwd.value !== changePasswordForm.controls.newpassword.value "
                                    style="color: red;">
                                    New password & Confirm password do not match!
                                </div>
                            </div>
                        </div>


                    </div> -->
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="changePasswordWithpopup()"
        [disabled]="!changePasswordForm.valid"
        class="btn btn-primary btn-sm"
        id="closeModal"
        type="submit"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button
        (click)="clearChangePasswordForm()"
        class="btn btn-default btn-sm"
        id="btn"
        type="button"
      >
        <i class="fa fa-refresh"></i>
        Clear
      </button>
      <button #closebutton class="btn btn-danger btn-sm" (click)="close()" type="button">
        Close
      </button>
    </div>
  </div>
</p-dialog>
