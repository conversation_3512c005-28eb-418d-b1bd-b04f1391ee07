// :host ::ng-deep {

// }

.row {
  display: flex;
  flex-wrap: wrap;
}

.header {
  font-size: 20px;
  font-weight: bolder;
}

:host ::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border: 1px solid;
  padding: 10px;
  box-shadow: 2px 2px #f7b206 !important;
}

.modal-body-location {
  position: relative;
  height: 40vh;
}

:host ::ng-deep .p-dialog .p-dialog-header {
  background: #f7b206 !important;
}

.checkbox-align {
  padding-top: 8%;
  padding-right: 10%;
}

.vas-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.vas-header {
  background: #f4f3f3;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
  font-size: 16px;
}

.vas-body {
  padding: 12px 15px;
}

.vas-row {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px dashed #eee;
}

.vas-row:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #555;
}

.value {
  font-weight: 500;
  color: #222;
}
