<div class="childComponent">
  <div class="row">
    <div class="col-md-12">
      <!-- User Data -->
      <div class="panel top">
        <div class="panel-heading">
          <h3 class="panel-title">MVNO Document Management</h3>
          <div class="right">
            <button type="button" class="btn-toggle-collapse">
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a class="curson_pointer" (click)="openMvnoDocCreateMenu()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Mvno Document</h5>
                <!-- <p>Create Staff</p> -->
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a class="curson_pointer" (click)="openMvnoDocListMenu()">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Mvno Document List</h5>
              </a>
            </div>
          </div>
        </div>
      </div>
      <!-- END User Data -->
    </div>
  </div>
  <div class="row" *ngIf="isMvnoDocList">
    <div class="col-md-12">
      <!-- Data Table -->
      <div class="panel">
        <div class="panel-heading">
          <div class="displayflex">
            <button
              type="button"
              class="btn btn-secondary backbtn"
              data-toggle="tooltip"
              data-placement="bottom"
              title="Go to Customer List"
              (click)="listMvno()"
            >
              <i
                class="fa fa-arrow-circle-left"
                style="color: #f7b206 !important; font-size: 28px"
              ></i>
            </button>
            <h3 class="panel-title">Mvno Documents</h3>
            <a
              class="detailOnAnchorClick"
              style="margin-left: 10px"
              title="Download All Documents"
              (click)="downloadAll()"
            >
              <img class="icon" src="assets/img/E_Status_Y.png" />
            </a>
          </div>
          <div class="right">
            <button type="button" class="btn-toggle-collapse">
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>
        <div class="panel-body table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Documents Type</th>
                <th>Documents Sub Type</th>
                <th>Document Number</th>
                <th>Documents Status</th>
                <th>File Name</th>
                <th>Remark</th>
                <th>Document Mode</th>
                <th>End Date</th>
                <th style="width: 15%">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let mvnoDoc of mvnoDocList
                    | paginate
                      : {
                          id: 'listing_docListdata',
                          itemsPerPage: itemsPerPage,
                          currentPage: currentPage,
                          totalItems: totalRecords
                        };
                  index as i
                "
              >
                <td>{{ mvnoDoc.docType | titlecase }}</td>
                <td>{{ mvnoDoc.docSubType }}</td>
                <td>
                  <span *ngIf="mvnoDoc.documentNumber">
                    {{ mvnoDoc.documentNumber }}
                  </span>
                  <span *ngIf="!mvnoDoc.documentNumber">-</span>
                </td>
                <td>{{ mvnoDoc.docStatus }}</td>
                <td *ngIf="mvnoDoc.filename">
                  <a
                    href="javascript:void(0)"
                    style="color: blue"
                    (click)="downloadDoc(mvnoDoc.filename, mvnoDoc.docId, mvnoDoc.mvnoId)"
                  >
                    {{ mvnoDoc.filename }}
                  </a>
                </td>
                <td *ngIf="!mvnoDoc.filename">-</td>
                <td>
                  <span *ngIf="mvnoDoc.remark">{{ mvnoDoc.remark }}</span>
                  <span *ngIf="!mvnoDoc.remark">-</span>
                </td>
                <td>{{ mvnoDoc.mode }}</td>
                <td>{{ mvnoDoc.endDate }}</td>

                <td class="btnAction" style="width: 15%">
                  <button
                    *ngIf="editAccess"
                    class="approve-btn"
                    type="button"
                    [disabled]="mvnoDoc.docStatus == 'Rejected'"
                    (click)="editCustDocById(mvnoDoc.docId, i)"
                  >
                    <img src="assets/img/ioc01.jpg" />
                  </button>
                  <button *ngIf="deleteAccess" class="approve-btn" (click)="deleteConfirm(mvnoDoc)">
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                  <button
                    *ngIf="mvnoDoc.mode === 'Online'"
                    class="approve-btn"
                    title="Verify Document"
                    (click)="verifyDocument(mvnoDoc)"
                    [disabled]="
                      (mvnoDoc.docStatus == 'Verified' && mvnoDoc.mode === 'Online') ||
                      mvnoDoc.docStatus == 'verified'
                    "
                  >
                    <img class="IconVerifyBtn" src="assets/img/verifyicon.svg" />
                  </button>
                  <button
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                    title="Pick"
                    *ngIf="mvnoDoc.docStatus == 'pending'"
                    [disabled]="mvnoDoc.nextStaff == staffID"
                    (click)="pickModalOpen(mvnoDoc)"
                  >
                    <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                  </button>
                  <button
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    type="button"
                    title="Pick"
                    *ngIf="mvnoDoc.docStatus == 'verified'"
                    [disabled]="mvnoDoc.nextStaff != staffID"
                    (click)="pickModalOpen(mvnoDoc)"
                  >
                    <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                  </button>
                  <button
                    (click)="approvestatusModalOpen(mvnoDoc)"
                    [disabled]="mvnoDoc.nextStaff != this.staffID"
                    class="btn btn-success gridbtn"
                    style="padding: 5px 1px"
                    title="Approve"
                    type="button"
                  >
                    <img src="assets/img/assign.jpg" />
                  </button>
                  <button
                    (click)="rejectstatusModalOpen(mvnoDoc)"
                    [disabled]="mvnoDoc.nextStaff != this.staffID"
                    class="btn btn-danger gridbtn"
                    style="padding: 5px 3px"
                    title="Reject"
                    type="button"
                  >
                    <img src="assets/img/reject.jpg" />
                  </button>
                  <!-- <button
                    (click)="openWorkFlowAudit('customerDocumentWorkflowAuidt', mvnoDoc.docId)"
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Audit & Documents Details"
                    type="button"
                  >
                    <img width="32" height="32" src="assets/img/05_inventory-to-customer_Y.png" />
                  </button> -->
                  <!-- <button
                    (click)="StaffReasignList(mvnoDoc)"
                    class="approve-btn"
                    id="assign-button"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    title="Reassign docId"
                    type="button"
                    [disabled]="mvnoDoc.nextStaff != staffID"
                  >
                    <img width="32" height="32" alt="Assign docId" src="assets/img/icons-02.png" />
                  </button> -->
                </td>
              </tr>
            </tbody>
          </table>
          <br />
          <div class="row">
            <div class="col-md-12">
              <pagination-controls
                id="listing_docListdata"
                maxSize="10"
                directionLinks="true"
                previousLabel
                nextLabel
                (pageChange)="pageChanged($event)"
              ></pagination-controls>
            </div>
          </div>
        </div>
      </div>
      <!-- END Data Table -->
    </div>
  </div>
  <div class="row" *ngIf="isMvnoDocCreateOrEdit">
    <div class="col-md-12">
      <!-- Form Design -->
      <div class="panel">
        <form class="form-auth-small" [formGroup]="insertCustomerDocumentForm">
          <div class="row" *ngIf="isMvnoDocCreateOrEdit">
            <div class="col-md-12">
              <!-- Form Design -->
              <div class="panel">
                <div class="panel-heading">
                  <h3 class="panel-title">{{ editMode ? "Update" : "Create" }} Mvno Document</h3>
                  <div class="right">
                    <button type="button" class="btn-toggle-collapse">
                      <i class="fa fa-minus-circle"></i>
                    </button>
                  </div>
                </div>
                <div class="panel-body">
                  <div class="form-group row">
                    <div class="col-md-4 ml-15">
                      <label for="mode">Verification Mode *</label>
                      <p-dropdown
                        [options]="VerificationModeValue"
                        optionValue="text"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a Verification Mode"
                        formControlName="mode"
                        [disabled]="editMode"
                        (onChange)="documentType($event.value)"
                        [ngClass]="{
                          'is-invalid': submitted && insertCustomerDocumentForm.controls.mode.errors
                        }"
                      ></p-dropdown>

                      <!-- (onChange)="OnVerificationMode($event)" -->
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.mode.errors"
                      >
                        <div class="error text-danger">Verification Mode is required.</div>
                      </div>
                    </div>
                    <div class="col-md-4 ml-15">
                      <label for="docType">Document Type *</label>

                      <p-dropdown
                        [options]="docTypeList"
                        optionValue="value"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a Document Type"
                        formControlName="docType"
                        [disabled]="editMode"
                        (onChange)="documentSubType($event.value)"
                        [ngClass]="{
                          'is-invalid':
                            submitted && insertCustomerDocumentForm.controls.docType.errors
                        }"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.docType.errors"
                      >
                        <div class="error text-danger">Document Type is required.</div>
                      </div>
                    </div>
                    <div class="col-md-4 ml-15">
                      <label for="docSubType">Document Sub Type *</label>
                      <p-dropdown
                        [options]="docSubTypeList"
                        optionValue="text"
                        optionLabel="text"
                        filter="true"
                        filterBy="text"
                        placeholder="Select a Document SubType"
                        formControlName="docSubType"
                        [ngClass]="{
                          'is-invalid':
                            submitted && insertCustomerDocumentForm.controls.docSubType.errors
                        }"
                      ></p-dropdown>

                      <!-- (onChange)="OnDocumentType($event)" -->
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.docSubType.errors"
                      >
                        <div class="error text-danger">Document Sub Type is required.</div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-4 ml-15" *ngIf="ifModeInput">
                      <label for="docStatus">Document Number *</label>
                      <input
                        id="documentNumber"
                        type="text"
                        class="form-control"
                        placeholder="Enter Document No. "
                        formControlName="documentNumber"
                        [ngClass]="{
                          'is-invalid':
                            submitted && insertCustomerDocumentForm.controls.documentNumber.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && insertCustomerDocumentForm.controls.documentNumber.errors
                        "
                      >
                        Please Document number required.
                      </div>
                    </div>
                    <div class="col-md-4 ml-15" *ngIf="!ifModeInput">
                      <label>Select Document *</label>
                      <input
                        type="file"
                        id="txtSelectDocument"
                        class="form-control"
                        formControlName="file"
                        placeholder="Select Document"
                        style="padding: 2px; width: 100%"
                        (change)="onFileChange($event)"
                      />
                      <span *ngIf="this.mvnoDoc.file">
                        {{ this.mvnoDoc.file }}
                      </span>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.file.errors"
                      >
                        <div class="error text-danger">Document is required.</div>
                      </div>
                      <div class="errorWrap text-danger" *ngIf="isFileSizeExceed">
                        <div class="error text-danger">Document size is greater then 10MB.</div>
                      </div>
                    </div>
                    <div class="col-md-4 ml-15" *ngIf="ifModeInput">
                      <label>Select Document</label>
                      <input
                        type="file"
                        id="txtSelectDocument"
                        class="form-control"
                        formControlName="file"
                        placeholder="Select Document"
                        style="padding: 2px; width: 100%"
                        (change)="onFileChange($event)"
                      />
                      <span *ngIf="this.custmerDoc.file">
                        {{ this.mvnoDoc.file }}
                      </span>
                    </div>
                    <div class="col-md-4 ml-15">
                      <label for="docStatus">Document Status *</label>
                      <div *ngIf="isEnableStatus">
                        <p-dropdown
                          [options]="documentStatusList"
                          optionLabel="value"
                          optionValue="value"
                          formControlName="docStatus"
                          [readonly]="isstatus"
                          filter="true"
                          filterBy="value"
                        ></p-dropdown>
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.docStatus.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            insertCustomerDocumentForm.controls.docStatus.errors.required
                          "
                        >
                          <div class="error text-danger">Document Status is required.</div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 ml-15" *ngIf="!ifModeInput">
                      <label>Start Date*</label>
                      <input
                        type="date"
                        class="form-control"
                        placeholder="Enter Start Date"
                        formControlName="startDate"
                        [max]="maxDate | date : 'yyyy-MM-dd'"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.startDate.errors"
                      >
                        Start Date is required.
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-4 ml-15" *ngIf="ifModeInput">
                      <label>Start Date*</label>
                      <input
                        type="date"
                        class="form-control"
                        placeholder="Enter Start Date"
                        formControlName="startDate"
                        [max]="maxDate | date : 'yyyy-MM-dd'"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.startDate.errors"
                      >
                        Start Date is required.
                      </div>
                    </div>
                    <div class="col-md-4 ml-15">
                      <label>End Date*</label>
                      <input
                        type="date"
                        class="form-control"
                        placeholder="Enter End Date"
                        formControlName="endDate"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && insertCustomerDocumentForm.controls.endDate.errors"
                      >
                        End Date is required.
                      </div>
                    </div>
                    <div class="col-md-4 ml-15">
                      <label>Remarks</label>
                      <textarea
                        class="form-control"
                        id="textarea"
                        rows="3"
                        placeholder="Enter Remarks"
                        formControlName="remark"
                        maxlength="100"
                      ></textarea>
                    </div>
                  </div>
                  <div class="addUpdateBtn" style="margin-top: 1rem">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      (click)="addDocument()"
                      [disabled]="isFileSizeExceed"
                    >
                      <i class="fa fa-check-circle"></i>
                      {{ editMode ? "Update Document" : "Add Document" }}
                    </button>
                    <br />
                  </div>
                </div>
              </div>
              <!-- END Form Design -->
            </div>
          </div>
        </form>

        <div
          class="modal fade"
          id="remarkModal"
          tabindex="-1"
          role="dialog"
          aria-labelledby="myModalLabel"
        >
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
                <h4>Add Remark</h4>
              </div>
              <div class="modal-body">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group row">
                      <div class="col-md-2">
                        <label for="normal-field">Remarks *</label>
                      </div>
                      <div class="col-md-10">
                        <textarea
                          class="form-control"
                          rows="4"
                          cols="60"
                          placeholder="Enter Remarks"
                          [ngClass]="{
                            'border-danger': !remark && remarksubmit
                          }"
                        ></textarea>
                        <div *ngIf="!remark && remarksubmit" class="text-danger">
                          This field is required
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-10"></div>
                </div>
              </div>
              <div class="modal-footer">
                <button
                  type="submit"
                  class="btn btn-primary"
                  (click)="approvedMvnoDoc(approveDataobj)"
                >
                  Approve
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="Approve Document "
  [(visible)]="assignCustomerDocumentForApproval"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="margin: 10px">
    <form [formGroup]="this.assignDocForm">
      <div class="row">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [(selection)]="this.assignedStaff"
                [value]="assignStaffListData"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body">
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-primary"
      id="submitButtonForApprove"
      type="submit"
      (click)="assignToStaff()"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      (click)="closeAssignCustomerDocumentForApproval()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
<p-dialog
  header="{{ ifApproveStatus ? 'Approve Status' : 'Reject Status' }}"
  [(visible)]="ApproveRejectModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Remark</label>
        <textarea
          placeholder="Remarks"
          [(ngModel)]="approveRejectRemark"
          class="form-control"
          name="remark"
        ></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="statusApporevedRejected()"
      [disabled]="!approveRejectRemark"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Save
    </button>

    <button
      class="btn btn-default"
      (click)="closeStatusApporevedRejected()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
<app-workflow-audit-details-modal
  *ngIf="ifModelIsShow"
  [auditcustid]="auditcustid"
  dialogId="custauditWorkflowModal"
  (closeParentCustt)="closeParentCustt()"
></app-workflow-audit-details-modal>
<div
  class="modal fade"
  id="reAssignPLANModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Approve Document
        </h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="assignDocForm">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [value]="approvableStaff"
                  [(selection)]="selectStaff"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-product>
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                class="form-control"
                name="remark"
                formControlName="remark"
                [ngClass]="{
                  'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
                }"
              ></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                >
                  Remark is required.
                </div>
              </div>
              <br />
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" id="submit" (click)="reassignWorkflow()">
          <i class="fa fa-check-circle"></i>
          Assign
        </button>

        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
