<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Business vertical Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchDataCountry" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchBusinessVName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchBusinessV()"
              />
            </div>
            <!-- (keydown.enter)="searchCountry()" -->
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchBusinessV()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchCountry()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Business vertical</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCountry"
            aria-expanded="false"
            aria-controls="allDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Business Vertical Name</th>
                    <th>Status</th>
                    <th *ngIf="deleteAccess || editAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let business of businessVisData
                        | paginate
                          : {
                              id: 'countryListData',
                              itemsPerPage: businessVitemsPerPage,
                              currentPage: currentPageBusinessVSlab,
                              totalItems: businessVtotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ business.vname }}</td>
                    <td *ngIf="business.status == 'ACTIVE' || business.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="business.status == 'INACTIVE' || business.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td class="btnAction" *ngIf="deleteAccess || editAccess">
                      <a
                        *ngIf="editAccess"
                        id="edit-button"
                        href="javascript:void(0)"
                        type="button"
                        (click)="editRegion(business.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="deleteAccess"
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmBusinessV(business.id)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    id="countryListData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedCountryList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isBusinessVEdit ? "Update" : "Create" }} {{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCountry"
            aria-expanded="false"
            aria-controls="createDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCountry" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !isBusinessVEdit">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (isBusinessVEdit && editAccess)">
            <form [formGroup]="businessVerticalFormGroup">
              <!-- <label>{{ title }} Name*</label> -->
              <label>Business vertical Name*</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Business vertical Name"
                formControlName="vname"
                [ngClass]="{
                  'is-invalid': submitted && businessVerticalFormGroup.controls.vname.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && businessVerticalFormGroup.controls.vname.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && businessVerticalFormGroup.controls.vname.errors.required"
                >
                  {{ title }} Name is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="
                    submitted && businessVerticalFormGroup.controls.vname.errors?.cannotContainSpace
                  "
                >
                  <p class="error">White space are not allowed.</p>
                </div>
              </div>
              <br />
              <div>
                <label>Region</label>
                <div>
                  <p-multiSelect
                    (onChange)="getSelectCustomerSector($event)"
                    id="id"
                    [options]="regionSector"
                    formControlName="region_id"
                    defaultLabel="Select Region Name"
                    optionValue="id"
                    optionLabel="rname"
                    filter="true"
                    filterBy="rname"
                    [ngClass]="{
                      'is-invalid': submitted && businessVerticalFormGroup.controls.region_id.errors
                    }"
                  ></p-multiSelect>
                </div>
                <br />
              </div>
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && businessVerticalFormGroup.controls.status.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && businessVerticalFormGroup.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && businessVerticalFormGroup.controls.status.errors.required"
                >
                  {{ title }} Status is required.
                </div>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  (click)="addEdit('')"
                  class="btn btn-primary"
                  *ngIf="!isBusinessVEdit"
                  id="submit"
                >
                  <i class="fa fa-check-circle"></i>
                  Add {{ title }}
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isBusinessVEdit"
                  id="submit"
                  (click)="addEdit(viewBusinessVListData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update {{ title }}
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
