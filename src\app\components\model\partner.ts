export interface partnerManagement {
  address1: string;
  address2: string;
  addresstype: string;
  city: number;
  cityName: string;
  commdueday: number;
  commrelvalue: number;
  commtype: string;
  commissionShareType: string;
  country: number;
  countryName: string;
  countryCode: string;
  createdById: number;
  createdByName: string;
  createdate: string;
  email: string;
  id: number;
  isDelete: boolean;
  lastModifiedById: number;
  lastModifiedByName: string;
  lastbilldate: string;
  mobile: string;
  name: string;
  nextbilldate: string;
  outcomeBalance: number;
  parentPartnerName: string;
  parentpartnerid: number;
  pincode: string;
  pricebookId: number;
  pricebookname: string;
  totalCustomerCount: number;
  renewCustomerCount: number;
  newCustomerCount: number;
  serviceAreaIds: [number];
  serviceAreaNameList: [string];
  state: number;
  stateName: string;
  status: string;
  taxName: string;
  taxid: number;
  updatedate: string;
  branch: number;
  region: number;
  businessvertical: number;
  isVisibleToIsp:boolean;
}
