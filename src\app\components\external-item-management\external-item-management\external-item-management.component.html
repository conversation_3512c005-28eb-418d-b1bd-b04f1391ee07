<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">External Item Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchExternalItem"
            aria-expanded="false"
            aria-controls="searchExternalItem"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchExternalItem" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchExternalItem"
                class="form-control"
                placeholder="External Item Number"
                (keydown.enter)="searchExternalItemData()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="externalsearchbtn"
                (click)="searchExternalItemData()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="externalclearbtn"
                (click)="clearSearchExternalItem()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
        <div class="panel-body no-padding panel-udata">
          <div class="pcol col-md-6" *ngIf="createAccess">
            <div class="dbox">
              <a (click)="createWareHouse()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create External Item</h5>
                <!-- <p>Create Partner </p> -->
              </a>
            </div>
          </div>
          <div class="pcol col-md-6">
            <div class="dbox">
              <a (click)="this.clearSearchExternalItem()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search External Item</h5>
                <!-- <p>Search Partner </p> -->
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12" *ngIf="this.listView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">External Items</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#listExternalItems"
            aria-expanded="false"
            aria-controls="listExternalItems"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="listExternalItems" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div>
            <div class="row">
              <div class="col-lg-12 col-md-12">
                <table class="table">
                  <thead>
                    <tr>
                      <th>External Item Number</th>
                      <th>Product Name</th>
                      <th>Type</th>
                      <th>Qty.</th>
                      <th>Available Qty.</th>
                      <th>InTransit Qty.</th>
                      <th>Status</th>
                      <th>Approval Status</th>
                      <th
                        *ngIf="
                          deleteAccess ||
                          editAccess ||
                          addMacAddressAccess ||
                          showMacAddressAccess ||
                          createAccess
                        "
                      >
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let externalItem of this.externalItemListData
                          | paginate
                            : {
                                id: 'externalItemListData',
                                itemsPerPage: productListdataitemsPerPage,
                                currentPage: currentPageProductListdata,
                                totalItems: productListdatatotalRecords
                              };
                        index as i
                      "
                    >
                      <td
                        class="curson_pointer"
                        (click)="getExternalItemDetails(externalItem.id)"
                        style="color: #f7b206"
                      >
                        {{ externalItem.externalItemGroupNumber }}
                      </td>

                      <td>{{ externalItem.productId.name }}</td>
                      <td>{{ externalItem.ownershipType }}</td>
                      <td>{{ externalItem.qty }}</td>
                      <td>{{ externalItem.unusedQty }}</td>
                      <td>{{ externalItem.inTransitQty }}</td>
                      <td *ngIf="externalItem.status == 'ACTIVE'">
                        <span class="badge badge-success">Active</span>
                      </td>
                      <td *ngIf="externalItem.status == 'INACTIVE'">
                        <span class="badge badge-danger">Inactive</span>
                      </td>
                      <td *ngIf="externalItem.approvalStatus == null">
                        <span>-</span>
                      </td>
                      <td *ngIf="externalItem.approvalStatus == 'Pending'">
                        <span class="badge badge-primary">Pending</span>
                      </td>
                      <td *ngIf="externalItem.approvalStatus == 'Approve'">
                        <span class="badge badge-success">Approve</span>
                      </td>
                      <td *ngIf="externalItem.approvalStatus == 'Rejected'">
                        <span class="badge badge-danger">Rejected</span>
                      </td>
                      <td
                        class="btnAction"
                        *ngIf="
                          deleteAccess ||
                          editAccess ||
                          addMacAddressAccess ||
                          showMacAddressAccess ||
                          createAccess
                        "
                      >
                        <button
                          class="approve-btn"
                          id="edit-button"
                          *ngIf="editAccess"
                          [disabled]="externalItem.approvalStatus != 'Pending'"
                          type="button"
                          href="javascript:void(0)"
                          (click)="editExternalItem(externalItem.id)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </button>
                        <button
                          *ngIf="deleteAccess"
                          id="delete-button"
                          class="approve-btn"
                          href="javascript:void(0)"
                          [disabled]="
                            externalItem.approvalStatus != 'Pending' ||
                            externalItem.outwardId != null
                          "
                          (click)="deleteConfirmExternalItem(externalItem.id)"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </button>
                        <button
                          data-target="#MACAssignModal"
                          data-title="Add MAC address"
                          title="Add MAC address"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          *ngIf="addMacAddressAccess || createAccess"
                          (click)="addMAC(externalItem.id)"
                          [disabled]="
                            (externalItem.approvalStatus == 'Pending' &&
                              externalItem.status == 'INACTIVE') ||
                            externalItem.approvalStatus == 'Approve' ||
                            externalItem.approvalStatus == 'Rejected'
                          "
                          class="approve-btn"
                        >
                          <img src="assets/img/icons-02.png" />
                        </button>
                        <a
                          data-target="#MACShowModal"
                          data-title="Add MAC address"
                          title="Show MAC address"
                          data-toggle="modal"
                          data-backdrop="static"
                          data-keyboard="false"
                          *ngIf="showMacAddressAccess && !externalItem.approvalStatus != 'Pending'"
                          (click)="showMac(externalItem.id)"
                          class="curson_pointer"
                        >
                          <img src="assets/img/E_Status_Y.png" />
                        </a>
                        <button
                          type="button"
                          style="
                            border: none;
                            background: transparent;
                            padding: 0;
                            margin-right: 3px;
                          "
                          title="Approve"
                          *ngIf="createAccess"
                          [disabled]="
                            (externalItem.approvalStatus == 'Pending' &&
                              externalItem.status == 'INACTIVE') ||
                            externalItem.inTransitQty != externalItem.totalMacSerial ||
                            externalItem.createdById != loggedInStaffId ||
                            (externalItem.approvalStatus != 'Pending' &&
                              externalItem.status == 'ACTIVE')
                          "
                          class="approve-btn"
                          (click)="approveChangeStatus(externalItem.id)"
                        >
                          <img src="assets/img/assign.jpg" />
                        </button>
                        <button
                          type="button"
                          style="
                            border: none;
                            background: transparent;
                            padding: 0;
                            margin-right: 3px;
                          "
                          title="Rejected"
                          *ngIf="createAccess"
                          [disabled]="
                            (externalItem.approvalStatus == 'Pending' &&
                              externalItem.status == 'INACTIVE') ||
                            externalItem.inTransitQty != externalItem.totalMacSerial ||
                            externalItem.createdById != loggedInStaffId ||
                            (externalItem.approvalStatus != 'Pending' &&
                              externalItem.status == 'ACTIVE')
                          "
                          class="approve-btn"
                          (click)="rejectChangeStatus(externalItem.id)"
                        >
                          <img src="assets/img/reject.jpg" />
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="pagination_Dropdown">
                  <pagination-controls
                    id="externalItemListData"
                    [maxSize]="10"
                    [directionLinks]="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedProductList((currentPageProductListdata = $event))"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [(ngModel)]="productListdataitemsPerPage"
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12" *ngIf="this.createView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ this.editMode ? "Update" : "Create" }} External Item</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createExternalItem"
            aria-expanded="false"
            aria-controls="createExternalItem"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createExternalItem" class="panel-collapse collapse in">
        <div class="panel-body">
          <form [formGroup]="externalItemManagementFormGroup">
            <fieldset style="margin-top: 0px">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="form-group">
                      <label>Product*</label>
                      <p-dropdown
                        [options]="this.products"
                        formControlName="productId"
                        optionLabel="name"
                        optionValue="id"
                        filter="true"
                        filterBy="name"
                        [disabled]="this.editMode"
                        placeholder="Select Product"
                        (onChange)="getUnit($event)"
                      >
                      </p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && externalItemManagementFormGroup.controls.productId.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.productId.errors.required
                          "
                        >
                          Product is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="form-group">
                      <label>Service Area*</label>
                      <p-dropdown
                        [options]="serviceAreaList"
                        formControlName="serviceAreaId"
                        optionLabel="name"
                        optionValue="id"
                        filter="true"
                        filterBy="name"
                        [disabled]="this.editMode"
                        placeholder="Select Service Area"
                        (onChange)="getOwnerType()"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && externalItemManagementFormGroup.controls.serviceAreaId.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.serviceAreaId.errors.required
                          "
                        >
                          Service Area is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="form-group">
                      <label>Quantity in {{ this.unit }} *</label>
                      <label *ngIf="this.editMode">
                        &nbsp; (Total added Mac Quantity :- {{ this.totalMacSerial }})
                      </label>
                      <input
                        type="number"
                        (keypress)="quantityInValidation($event)"
                        class="form-control"
                        placeholder="Enter Quantity"
                        formControlName="inTransitQty"
                        [ngClass]="{
                          'is-invalid':
                            submitted && externalItemManagementFormGroup.controls.qty.errors
                        }"
                      />
                      <div class="error text-danger" *ngIf="this.editMode && this.showQtyError">
                        Quantity must be greater than used quantity.
                      </div>
                      <div
                        class="error text-danger"
                        *ngIf="this.editMode && this.showIntransitQtyError"
                      >
                        Quantity must be greater than total added Mac Serial.
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && externalItemManagementFormGroup.controls.inTransitQty.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.inTransitQty.errors.required
                          "
                        >
                          Quantity is required.
                        </div>
                        <div
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.inTransitQty.errors.pattern
                          "
                          class="error text-danger"
                        >
                          Only Numeric value are allowed.
                        </div>
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.inTransitQty.errors.min
                          "
                        >
                          Please enter more than 0 quantity.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="form-group">
                      <label>Ownership Type*</label>
                      <p-dropdown
                        [options]="this.ownershipType"
                        formControlName="ownershipType"
                        optionLabel="label"
                        optionValue="value"
                        filter="true"
                        filterBy="label"
                        [disabled]="this.editMode"
                        placeholder="Select Ownership type"
                        (onChange)="
                          this.getDestinations(
                            this.externalItemManagementFormGroup.controls.ownershipType.value
                          )
                        "
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && externalItemManagementFormGroup.controls.ownershipType.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.ownershipType.errors.required
                          "
                        >
                          Ownership Type is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="col-lg-4 col-md-4 col-sm-6 col-xs-12"
                    *ngIf="getOwnerFlag && !ownerShow"
                  >
                    <div class="form-group">
                      <label>Owner*</label>
                      <!-- <p-dropdown
                          *ngIf="!isDestAStaffOrCustomer"
                          [options]="selectOwner"
                          formControlName="ownerId"
                          optionLabel="name"
                          optionValue="id"
                          filter="true"
                          filterBy="name"
                          [disabled]="this.editMode"
                          placeholder="Select Owner"
                        ></p-dropdown> -->
                      <div>
                        <p-dropdown
                          *ngIf="!isDestAStaffOrCustomer"
                          [options]="ownerSelectList"
                          formControlName="ownerId"
                          optionLabel="name"
                          optionValue="id"
                          filter="true"
                          filterBy="name"
                          [disabled]="true || this.editMode"
                          placeholder="Select Owner"
                          [showClear]="true"
                          styleClass="disableDropdown"
                        >
                          <ng-template let-data pTemplate="item">
                            <div class="item-drop1">
                              <span class="item-value1"> {{ data.name }} </span>
                            </div>
                          </ng-template>
                        </p-dropdown>
                        <button
                          *ngIf="!isDestAStaffOrCustomer"
                          type="button"
                          (click)="modalOpenSelectOwner('Partner Owned')"
                          class="btn btn-primary"
                          id="selectownerbtn"
                          style="
                            border-radius: 5px;
                            padding: 5px 10px;
                            line-height: 1.5;
                            margin-left: 10px;
                          "
                        >
                          <i class="fa fa-plus-square"></i>
                        </button>
                        <!-- <button *ngIf="!isDestAStaffOrCustomer" [disabled]="ownerId == null" type="button" (click)="removeSelectOwner()"
                            class="btn btn-danger"
                            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px">
                            <i class="fa fa-trash"></i>
                          </button> -->
                      </div>
                      <div>
                        <p-dropdown
                          *ngIf="isDestAStaffOrCustomer"
                          [options]="ownerSelectList"
                          formControlName="ownerId"
                          optionLabel="name"
                          optionValue="id"
                          filter="true"
                          filterBy="name"
                          [disabled]="true || this.editMode"
                          placeholder="Select Owner"
                          [showClear]="true"
                          styleClass="disableDropdown"
                        >
                          <ng-template let-data pTemplate="item">
                            <div class="item-drop1">
                              <span class="item-value1"> {{ data.name }} </span>
                            </div>
                          </ng-template>
                        </p-dropdown>
                        <button
                          *ngIf="isDestAStaffOrCustomer"
                          type="button"
                          (click)="modalOpenSelectOwner('Customer Owned')"
                          class="btn btn-primary"
                          style="
                            border-radius: 5px;
                            padding: 5px 10px;
                            line-height: 1.5;
                            margin-left: 10px;
                          "
                        >
                          <i class="fa fa-plus-square"></i>
                        </button>
                        <!-- <button *ngIf="isDestAStaffOrCustomer" [disabled]="ownerId == null" type="button"
                            (click)="removeSelectOwner()" class="btn btn-danger"
                            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px">
                            <i class="fa fa-trash"></i>
                          </button> -->
                      </div>
                      <!-- <p-dropdown
                          *ngIf="isDestAStaffOrCustomer"
                          [options]="selectOwner"
                          formControlName="ownerId"
                          [disabled]="this.editMode"
                          optionLabel="name"
                          optionValue="id"
                          filter="true"
                          filterBy="name"
                          placeholder="Select Owner"
                        ></p-dropdown> -->
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && externalItemManagementFormGroup.controls.ownerId.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.ownerId.errors.required
                          "
                        >
                          Owner is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12" *ngIf="ownerShow">
                    <div class="form-group">
                      <label>Owner</label>
                      <div>
                        <p-dropdown
                          [options]="ownerSelectList"
                          formControlName="ownerId"
                          optionLabel="name"
                          optionValue="id"
                          filter="true"
                          filterBy="name"
                          [disabled]="true || this.editMode"
                          placeholder="Select Owner"
                          [showClear]="true"
                          styleClass="disableDropdown"
                        >
                          <ng-template let-data pTemplate="item">
                            <div class="item-drop1">
                              <span class="item-value1"> {{ data.name }} </span>
                            </div>
                          </ng-template>
                          >
                        </p-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="form-group">
                      <label>Status*</label>
                      <p-dropdown
                        [options]="status"
                        formControlName="status"
                        optionLabel="label"
                        optionValue="value"
                        filter="true"
                        filterBy="label"
                        placeholder="Select status"
                      ></p-dropdown>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && externalItemManagementFormGroup.controls.status.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            externalItemManagementFormGroup.controls.status.errors.required
                          "
                        >
                          External Item status is required.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
            <br />
            <!-- </div> -->
            <div class="addUpdateBtn">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="submit()"
                [disabled]="!externalItemManagementFormGroup.valid"
              >
                <i class="fa fa-check-circle"></i>
                {{ editMode ? "Update" : "Add" }} External Item
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="this.addMACaddress">
    <p-dialog
      header="External Item MAC Mapping"
      [(visible)]="MACAssignModal"
      [style]="{ width: '60%' }"
      [modal]="true"
      [responsive]="true"
      [draggable]="false"
      [closable]="true"
      (onHide)="clearMacMapping()"
    >
      <div class="modal-body" [formGroup]="macForm">
        <p-table
          #dt1
          [value]="this.externalItemMacList"
          responsiveLayout="scroll"
          [scrollable]="true"
          scrollHeight="300px"
        >
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 45%" *ngIf="this.hasMac">
                <input
                  pInputText
                  type="text"
                  placeholder="Pleas enter MAC Address"
                  class="p-column-filter form-control"
                  formControlName="macAddress"
                />
              </th>
              <th style="width: 45%" *ngIf="this.hasSerial">
                <input
                  pInputText
                  type="text"
                  placeholder="Pleas enter serial Number"
                  class="p-column-filter form-control"
                  formControlName="serialNumber"
                />
              </th>
              <th style="width: 10%">
                <button
                  id="addAtt"
                  style="object-fit: cover; padding: 5px 8px"
                  class="btn btn-primary"
                  (click)="onAddAttribute()"
                >
                  <i class="fa fa-plus-square" aria-hidden="true"></i>
                  Add
                </button>
              </th>
            </tr>
            <tr>
              <th style="width: 45%" *ngIf="this.hasMac">MAC Address</th>
              <th style="width: 45%" *ngIf="this.hasSerial">Serial Number</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-product let-rowIndex="rowIndex">
            <tr>
              <td style="width: 45%" *ngIf="this.hasMac">
                <input
                  type="text"
                  name="mac"
                  class="form-control"
                  [value]="product.macAddress"
                  disabled
                />
              </td>
              <td style="width: 45%" *ngIf="this.hasSerial">
                <input
                  type="text"
                  name="mac"
                  class="form-control"
                  [value]="product.serialNumber"
                  disabled
                />
              </td>
              <td style="width: 10%">
                <a id="deleteMAC" (click)="deleteMACMapping(product.itemId)">
                  <img src="assets/img/ioc02.jpg" />
                </a>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <!-- <button
              type="submit"
              class="btn btn-primary btn-sm"
              data-dismiss="modal"
              (click)="this.addMacMapping()"
            >
              <i class="fa fa-check-circle"></i>
              Save
            </button> -->

          <button
            type="button"
            class="btn btn-danger btn-sm"
            data-dismiss="modal"
            (click)="clearMacMapping()"
          >
            Close
          </button>
        </div>
      </div>
    </p-dialog>
  </div>
  <p-dialog
    header="Change Approval Status"
    [(visible)]="approveChangeStatusModal"
    [style]="{ width: '40%' }"
    [modal]="true"
    [responsive]="true"
    [draggable]="false"
    [closable]="true"
    (onHide)="closeStatusModal()"
  >
    <div class="modal-body">
      <form [formGroup]="assignExternalItemForm">
        <div>
          <div class="dataGroup">
            <label class="datalbl">Remark*:</label>
          </div>
        </div>
        <div class="row" style="margin-top: 1rem">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <textarea class="form-control" formControlName="remark" name="remark"></textarea>
            <div
              class="errorWrap text-danger"
              *ngIf="assignExternalItemSubmitted && assignExternalItemForm.controls.remark.errors"
            >
              <div
                class="error text-danger"
                *ngIf="
                  assignExternalItemSubmitted &&
                  assignExternalItemForm.controls.remark.errors.required
                "
              >
                Remark is required.
              </div>
            </div>
            <br />
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button
        (click)="approveExternalItemGroup()"
        class="btn btn-primary"
        id="submit"
        type="submit"
      >
        <i class="fa fa-check-circle"></i>
        Approve
      </button>
      <button
        class="btn btn-default"
        (click)="closeStatusModal()"
        data-dismiss="modal"
        type="button"
      >
        Close
      </button>
    </div>
  </p-dialog>
  <p-dialog
    header="Change Approval Status"
    [(visible)]="rejectChangeStatusModal"
    [style]="{ width: '40%' }"
    [modal]="true"
    [responsive]="true"
    [draggable]="false"
    [closable]="true"
    (onHIde)="closeStatusModal()"
  >
    <div class="modal-body">
      <form [formGroup]="assignExternalItemForm">
        <div>
          <div class="dataGroup">
            <label class="datalbl">Remark*:</label>
          </div>
        </div>
        <div class="row" style="margin-top: 1rem">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <textarea class="form-control" formControlName="remark" name="remark"></textarea>
            <div
              class="errorWrap text-danger"
              *ngIf="assignExternalItemSubmitted && assignExternalItemForm.controls.remark.errors"
            >
              <div
                class="error text-danger"
                *ngIf="
                  assignExternalItemSubmitted &&
                  assignExternalItemForm.controls.remark.errors.required
                "
              >
                Remark is required.
              </div>
            </div>
            <br />
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button (click)="rejectExternalItemGroup()" class="btn btn-primary" id="submit" type="submit">
        <i class="fa fa-check-circle"></i>
        Reject
      </button>
      <button
        class="btn btn-default"
        (click)="closeStatusModal()"
        data-dismiss="modal"
        type="button"
      >
        Close
      </button>
    </div>
  </p-dialog>
  <div *ngIf="this.addMACaddress">
    <p-dialog
      header="External Item MAC Mapping"
      [(visible)]="MACShowModal"
      [style]="{ width: '60%' }"
      [modal]="true"
      [responsive]="true"
      [draggable]="false"
      [closable]="true"
      (onHide)="onclosed()"
    >
      <div class="modal-body" [formGroup]="macForm">
        <p-table
          #dt1
          [value]="this.externalItemMacList"
          responsiveLayout="scroll"
          [scrollable]="true"
          scrollHeight="300px"
        >
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 45%" *ngIf="this.hasMac">MAC Address</th>
              <th style="width: 45%" *ngIf="this.hasSerial">Serial Number</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-product let-rowIndex="rowIndex">
            <tr>
              <td style="width: 45%" *ngIf="this.hasMac">
                <input
                  type="text"
                  name="mac"
                  class="form-control"
                  [value]="product.macAddress"
                  disabled
                />
              </td>
              <td style="width: 45%" *ngIf="this.hasSerial">
                <input
                  type="text"
                  name="mac"
                  class="form-control"
                  [value]="product.serialNumber"
                  disabled
                />
              </td>
              <!-- <td style="width: 10%">
                  <a id="deleteMAC" (click)="deleteMACMapping(product)">
                    <img src="assets/img/ioc02.jpg" />
                  </a>
                </td> -->
            </tr>
          </ng-template>
        </p-table>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <!-- <button
            type="submit"
            class="btn btn-primary btn-sm"
            data-dismiss="modal"
            (click)="this.addMacMapping()"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button> -->

          <button
            type="button"
            class="btn btn-danger btn-sm"
            data-dismiss="modal"
            (click)="onclosed()"
          >
            Close
          </button>
        </div>
      </div>
    </p-dialog>
  </div>
  <div class="row" *ngIf="detailView">
    <div class="col-md-12 col-sm-12">
      <div class="panel">
        <div class="panel-heading">
          <div class="displayflex">
            <button
              type="button"
              class="btn btn-secondary backbtn"
              data-toggle="tooltip"
              data-placement="bottom"
              title="Go to Tax Details"
              (click)="externalItemList()"
            >
              <i
                class="fa fa-arrow-circle-left"
                style="color: #f7b206 !important; font-size: 28px"
              ></i>
            </button>
            <h3 class="panel-title">
              {{ viewExternalItemData.externalItemGroupNumber }} External Item
            </h3>
          </div>
          <div class="right">
            <button
              type="button"
              class="btn-toggle-collapse"
              data-toggle="collapse"
              data-target="#externalItemDetail"
              aria-expanded="false"
              aria-controls="taxalldea"
            >
              <i class="fa fa-minus-circle"></i>
            </button>
          </div>
        </div>

        <div id="externalItemDetail" class="panel-collapse collapse in">
          <div class="panel-body table-responsive">
            <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
              <legend>Basic Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Product Name :</label>
                    <span>{{ viewExternalItemData.productId.name }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl"> Service Area :</label>
                    <span>{{ viewExternalItemData.serviceAreaId.name }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Quantity :</label>
                    <span>{{ viewExternalItemData.qty }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Transit Quantity :</label>
                    <span>{{ viewExternalItemData.inTransitQty }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Ownership Type :</label>
                    <span>{{ viewExternalItemData.ownershipType }}</span>
                  </div>
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Owner Name :</label>
                    <span>{{ viewExternalItemData.ownerName }}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-4 col-md-4 dataGroup">
                    <label class="datalbl">Status :</label>
                    <span>{{ viewExternalItemData.status }}</span>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="selectPartnerOwner" role="dialog" data-backdrop="static">
    <div class="modal-dialog" style="width: 80%">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Select Partner</h3>
        </div>
        <div class="modal-body">
          <h5 style="margin-top: 15px">Select Partner</h5>
          <div class="row">
            <div class="col-lg-3 col-md-3 m-b-10">
              <input
                [(ngModel)]="searchPartnerValue"
                class="form-control"
                id="name"
                placeholder="Enter Search Detail"
                type="text"
                (keydown.enter)="searchPartner()"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <button
                (click)="searchPartner()"
                class="btn btn-primary"
                id="partnersearchbtn"
                type="button"
                [disabled]="!searchPartnerValue"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                (click)="clearSearchPartner()"
                class="btn btn-default"
                id="partnerclearbtn"
                type="reset"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
          <h5 style="margin-top: 15px">Select Partner</h5>
          <p-table
            #dt
            [(selection)]="selectedPartner"
            [value]="partnerListData"
            responsiveLayout="scroll"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 5rem"></th>
                <th>Name</th>
              </tr>
            </ng-template>
            <ng-template let-selectOwner let-rowIndex="rowIndex" pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="selectOwner"></p-tableRadioButton>
                </td>
                <td>
                  {{ selectOwner.name }}
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="summary">
              <p-paginator
                (onPageChange)="paginatePartner($event)"
                [first]="newFirst"
                [rows]="partnerListdataitemsPerPage"
                [totalRecords]="partnerListdatatotalRecords"
              ></p-paginator>
            </ng-template>
          </p-table>
        </div>
        <div class="modal-footer">
          <div class="addUpdateBtn">
            <button
              (click)="saveSelOwner('Partner Owned')"
              class="btn btn-primary"
              style="object-fit: cover; padding: 5px 8px"
              id="partnersavebtn"
              [disabled]="this.selectedPartner.length == 0"
            >
              <i class="fa fa-check-circle"></i>
              Save
            </button>
            <button
              (click)="modalCloseOwner('Partner Owned')"
              class="btn btn-danger btn-sm"
              type="button"
              id="partnerclosebtn"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
  <!-- <div *ngIf="customerOwnerModelFlag"> -->
  <div class="modal fade" id="selectCustomerOwner" role="dialog" data-backdrop="static">
    <div class="modal-dialog" style="width: 80%">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="panel-title">Select Customer</h3>
        </div>
        <div class="modal-body">
          <h5 style="margin-top: 15px">Select Customer</h5>
          <div class="row">
            <div class="col-lg-3 col-md-3 m-b-10">
              <p-dropdown
                (onChange)="selParentSearchOption($event)"
                [(ngModel)]="searchParentCustOption"
                [options]="searchOptionSelect"
                [filter]="true"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select a Search Option"
              ></p-dropdown>
            </div>
            <div
              *ngIf="
                parentFieldEnable &&
                searchParentCustOption != 'UserName' &&
                searchParentCustOption !== 'Name'
              "
              class="col-lg-3 col-md-3 m-b-10"
            >
              <input
                [(ngModel)]="searchParentCustValue"
                class="form-control"
                id="username"
                placeholder="Enter Search Detail"
                type="text"
                (keydown.enter)="searchParentCustomer()"
              />
            </div>
            <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
              <button
                (click)="searchParentCustomer()"
                class="btn btn-primary"
                id="customersearchbtn"
                type="button"
                [disabled]="!searchParentCustValue"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                (click)="clearSearchParentCustomer()"
                class="btn btn-default"
                id="customerclearbtn"
                type="reset"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
          <h5 style="margin-top: 15px">Select Customer</h5>
          <p-table
            #dt
            [(selection)]="selectedCustomer"
            [value]="customerListData"
            responsiveLayout="scroll"
          >
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 5rem"></th>
                <th>Name</th>
                <th>UserName</th>
              </tr>
            </ng-template>
            <ng-template let-selectOwner let-rowIndex="rowIndex" pTemplate="body">
              <tr>
                <td>
                  <p-tableRadioButton [value]="selectOwner"></p-tableRadioButton>
                </td>
                <td>
                  {{ selectOwner.fullName }}
                </td>
                <td>
                  {{ selectOwner.username }}
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="summary">
              <p-paginator
                (onPageChange)="paginateCustomer($event)"
                [first]="newFirst"
                [rows]="customerListdataitemsPerPage"
                [totalRecords]="customerListdatatotalRecords"
              ></p-paginator>
            </ng-template>
          </p-table>
        </div>
        <div class="modal-footer">
          <div class="addUpdateBtn">
            <button
              (click)="saveSelOwner('Customer Owned')"
              class="btn btn-primary"
              style="object-fit: cover; padding: 5px 8px"
              [disabled]="this.selectedCustomer.length == 0"
              id="customersavebtn"
            >
              <i class="fa fa-check-circle"></i>
              Save
            </button>
            <button
              (click)="modalCloseOwner('Customer Owned')"
              class="btn btn-danger btn-sm"
              type="button"
              id="customerclearbtn"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
