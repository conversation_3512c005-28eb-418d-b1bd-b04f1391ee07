<!-- Modal -->
<p-dialog
  header="Inventory Detail"
  [(visible)]="inventorySpecificationParamModal"
  [style]="{ width: '75%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div id="inwardDetail" class="panel-collapse collapse in">
    <div class="panel-body table-responsive">
      <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
        <legend>Inventory Specification Parameter Details</legend>
        <div class="boxWhite">
          <div class="row table-responsive">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th id="parameter">Parameter</th>
                    <th id="mandatory">Mandatory</th>
                    <th id="value">Value</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let param of inventorySpecificationDetails
                        | paginate
                          : {
                              id: 'productDetailPageData',
                              itemsPerPage: productDeatilItemPerPage,
                              currentPage: productPageChargeDeatilList,
                              totalItems: productDeatiltotalRecords
                            };
                      let i = index
                    "
                  >
                    <td>{{ param.paramName }}</td>
                    <td>{{ param.isMandatory }}</td>
                    <td *ngIf="!param.isMultiValueParam">
                      <input
                        type="text"
                        name="value"
                        class="form-control"
                        [(ngModel)]="param.paramValue"
                        [disabled]="!isEditing(i)"
                      />
                    </td>
                    <td *ngIf="param.isMultiValueParam">
                      <p-dropdown
                        [options]="param.multiValue"
                        [(ngModel)]="param.paramValue"
                        optionLabel="label"
                        optionValue="value"
                        filter="true"
                        filterBy="label"
                        placeholder="Select Parameter"
                        [disabled]="!isEditing(i)"
                      >
                      </p-dropdown>
                    </td>

                    <td *ngIf="!isEditing(i)">
                      <div class="btnAction">
                        <button
                          type="button"
                          class="approve-btn"
                          title="Edit"
                          (click)="editValue(i)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </button>
                      </div>
                    </td>
                    <td *ngIf="isEditing(i)">
                      <button
                        type="submit"
                        id="delete-button"
                        class="btn btn-primary btn-sm"
                        (click)="addOrEditValue(i, param.id, param.paramValue, param)"
                      >
                        Save
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </fieldset>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="button"
        (click)="saveInventorySpecificationParams()"
        class="btn btn-primary btn-sm"
        data-dismiss="modal"
      >
        Save
      </button>

      <button
        type="button"
        (click)="closeInventorySpecificationDetailModal()"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
