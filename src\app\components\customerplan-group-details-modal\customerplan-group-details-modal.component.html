<!-- Modal -->
<div
  class="modal fade"
  id="{{ dialogId }}"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Plan Details</h4>
      </div>

      <div class="modal-body">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12" *ngIf="planMappingList.length !== 0">
              <table class="table">
                <thead>
                  <tr>
                    <!-- <th>Service</th> -->
                    <th>Plan</th>
                    <th>Invoice Type</th>
                    <th>Validity</th>
                    <th>
                      <span *ngIf="this.customerBill == 'ORGANIZATION'">Old offerPrice</span>
                      <span *ngIf="this.customerBill !== 'ORGANIZATION'">Offer Price</span>
                    </th>
                    <th *ngIf="this.customerBill == 'ORGANIZATION'">New offerPrice</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let plan of planMappingList
                        | paginate
                          : {
                              id: 'custPlanpageData',
                              itemsPerPage: MasteritemsPerPage,
                              currentPage: currentPageMasterSlab,
                              totalItems: MastertotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      {{ plan.planName }}
                    </td>
                    <td>{{ plan.invoiceType !== null ? plan.invoiceType : "-" }}</td>
                    <td>
                      <span *ngFor="let list of dataPlan; index as j">
                        <span *ngIf="i === j">
                          {{ list.validity }} {{ list.unitsOfValidity }}
                        </span>
                      </span>
                    </td>
                    <td>
                      {{ plan.offerPrice | number : "1.2-2" }}
                    </td>
                    <td *ngIf="this.customerBill == 'ORGANIZATION'">
                      {{ plan.newAmount }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <br />
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="custPlanpageData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedMasterList($event)"
                ></pagination-controls>
              </div>
            </div>
            <div class="col-lg-12 col-md-12" *ngIf="planMappingList.length === 0">
              Details are not available
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
