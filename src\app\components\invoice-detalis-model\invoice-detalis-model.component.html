<p-dialog
  header="Invoice Master Details"
  [(visible)]="displayInvoiceMasterDetails"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="close()"
>
  <div class="modal-body">
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Basic Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 dataGroup">
            <label class="datalbl">Customer: </label>
            <span>{{ viewbillInvoiceListData.customerName }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8 col-xs-12 dataGroup">
            <label class="datalbl">Document No: </label>
            <span>{{ viewbillInvoiceListData.docnumber }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Bill Run Id : </label>
            <span>{{ viewbillInvoiceListData.billrunid }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Bill Date : </label>
            <span>{{ viewbillInvoiceListData.billdate | date: "yyyy-MM-dd" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Status : </label>
            <span class="badge badge-success">{{ viewbillInvoiceListData.billrunstatus }}</span>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Amount Details</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Total Amount: </label>
            <span>{{ viewbillInvoiceListData.totalamount | number: "1.2-2" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Discount : </label>
            <span>{{ viewbillInvoiceListData.discount }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Tax : </label>
            <a
              class="curson_pointer"
              style="color: #f7b206"
              (click)="openTotalTaxModal(viewbillInvoiceListData.id, 'amount')"
              >{{ viewbillInvoiceListData.tax | number: "1.2-2" }}</a
            >
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 dataGroup">
            <label class="datalbl">Amount In Words : </label>
            <span>{{ viewbillInvoiceListData.amountinwords }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Due Date : </label>
            <span>{{ viewbillInvoiceListData.duedate | date: "yyyy-MM-dd" }}</span>
          </div>
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12 dataGroup"
            *ngIf="sourceType == 'customer'"
          >
            <label class="datalbl">Due Date with Grace Day: </label>
            <span>{{ viewbillInvoiceListData.dueDateWithGrace | date: "yyyy-MM-dd" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Start Date : </label>
            <span>{{ viewbillInvoiceListData.startdate | date: "yyyy-MM-dd" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">End Date : </label>
            <span>{{ viewbillInvoiceListData.endate | date: "yyyy-MM-dd" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Create Date : </label>
            <span>{{ viewbillInvoiceListData.createdate | date: "yyyy-MM-dd" }}</span>
          </div>
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup" 
            *ngIf="debitDocDetails?.length && debitDocDetails[0]?.currentInstallmentNo">
            <label class="datalbl">Current Installment No : </label>
            <span>{{ debitDocDetails[0].currentInstallmentNo }}</span>
            </div>
        </div>
      </div>
    </fieldset>
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem" *ngIf="promiseToPay">
      <legend>Promise To Pay</legend>
      <div class="boxWhite">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Promise To Pay HoldDays: </label>
            <span>{{ viewbillInvoiceListData.promiseToPayHoldDays }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Promise To Pay Start Date : </label>
            <span>{{ viewbillInvoiceListData.promiseStartDate | date: "yyyy-MM-dd" }}</span>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
            <label class="datalbl">Promise To Pay End Date : </label>
            <span>{{ viewbillInvoiceListData.promiseEndDate | date: "yyyy-MM-dd" }}</span>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
      <legend>Charge Details</legend>
      <div class="boxWhite">
        <p-table #dt [value]="debitDocDetails" responsiveLayout="scroll">
          <ng-template pTemplate="header">
            <tr>
              <th>Charge Name</th>
              <!--                  <th>Type</th>-->
              <th>Sub Total</th>
              <th>Discount</th>
              <th>Tax</th>
              <th *ngIf="installmentInterestExists">Installment Interest</th>
              <th>Total Amount</th>
              <th>Description</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
            <tr>
              <td width="30%">{{ prepaidParentCustomerList.chargename }}</td>

              <!--                  <td>{{ prepaidParentCustomerList.chargetype }}</td>-->
              <td>
                {{ prepaidParentCustomerList.subtotal | number: "1.2-2" }}
              </td>
              <td>
                {{ prepaidParentCustomerList.discount | number: "1.2-2" }}
              </td>
              <td>
                <a
                  class="curson_pointer"
                  style="color: #f7b206"
                  (click)="openTaxModal(prepaidParentCustomerList.debitdocdetailid, 'charge')"
                >
                  {{ prepaidParentCustomerList.tax | number: "1.2-2" }}
                </a>
              </td>
                <td *ngIf="installmentInterestExists">
                {{ prepaidParentCustomerList.installmentInterest | number: "1.2-2"}}
              </td>
              <td>
                {{ prepaidParentCustomerList.totalamount | number: "1.2-2" }}
              </td>
              <td>{{ prepaidParentCustomerList.description }}</td>
            </tr>

          </ng-template>
          <!-- <ng-template pTemplate="summary">
                <p-paginator
                  [rows]="parentCustomerListdataitemsPerPage"
                  [first]="newFirst"
                  [totalRecords]="parentCustomerListdatatotalRecords"
                  (onPageChange)="paginate($event)"
                ></p-paginator>
              </ng-template> -->
        </p-table>
      </div>
    </fieldset>
    <fieldset style="margin-top: 0rem; margin-bottom: 2rem" *ngIf="showInventory">
      <legend>Inventory Details</legend>
      <div class="boxWhite">
        <p-table
          #dt
          [value]="viewbillInvoiceListData.debitDocumentInventoryRels"
          responsiveLayout="scroll"
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Product Name</th>
              <th>Product Type</th>
              <!--                  <th>Item Name</th>-->
              <th>Serial No</th>
              <th>Mac</th>
              <th>Assigned Date</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-prepaidParentCustomerList let-rowIndex="rowIndex">
            <tr>
              <td width="30%">{{ prepaidParentCustomerList.productName }}</td>
              <td>{{ prepaidParentCustomerList.productType }}</td>
              <!--                  <td> {{ prepaidParentCustomerList.itemName}} </td>-->
              <td>{{ prepaidParentCustomerList.itemSerialNumber }}</td>
              <td>{{ prepaidParentCustomerList.itemMac }}</td>
              <td>
                {{ prepaidParentCustomerList.assignedDate | date: "dd-MM-yyyy hh:mm:ss" }}
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </fieldset>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" (click)="close()">Close</button>
  </div>
</p-dialog>
<p-dialog
  header="Tax Details"
  [(visible)]="displayTaxDetails"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <p-table #dt [value]="taxData" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th style="text-align: center">Tax Name</th>
          <th style="text-align: center" *ngIf="taxtype === 'charge'">Tax Level</th>
          <th style="text-align: center">Percentage%</th>
          <th style="text-align: center">Amount</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-data let-rowIndex="rowIndex">
        <tr>
          <td style="text-align: center">{{ data?.taxname }}</td>
          <td style="text-align: center" *ngIf="taxtype === 'charge'">{{ data?.taxlevel }}</td>
          <td style="text-align: center">{{ data?.percentage }}%</td>
          <td style="text-align: center">{{ data?.amount | number: "1.2-2" }}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <!-- <button
            style="object-fit: cover; padding: 5px 8px" 
            class="btn btn-primary"
            (click)="saveSelCustomer()"
          >
            <i class="fa fa-check-circle"></i>
            Save
          </button> -->
      <button
        type="button"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeDisplayTaxDetails()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>
