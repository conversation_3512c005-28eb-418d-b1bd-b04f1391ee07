<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Business Vertical Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataState"
            aria-expanded="false"
            aria-controls="searchDataState"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchDataState" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchbusVertical()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchbusVertical()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button type="reset" class="btn btn-default" id="searchbtn" (click)="clearData()">
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Business Vertical</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataState"
            aria-expanded="false"
            aria-controls="allDataState"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="allDataState" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <!-- <th>Branch</th> -->
                    <th>Status</th>
                    <th *ngIf="deleteAccess || editAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let bVertical of busVerticalListData
                        | paginate
                          : {
                              id: 'regionListDataID',
                              itemsPerPage: itemsPerPage,
                              currentPage: currentPage,
                              totalItems: totalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ bVertical.vname }}</td>
                    <!-- <td>{{ bVertical.rname }}</td> -->
                    <td>
                      <span
                        *ngIf="
                          bVertical.status == 'ACTIVE' ||
                          bVertical.status == 'Active' ||
                          bVertical.status == 'active'
                        "
                      >
                        <span class="badge badge-success">Active</span>
                      </span>
                      <span
                        *ngIf="bVertical.status == 'INACTIVE' || bVertical.status == 'Inactive'"
                      >
                        <span class="badge badge-danger">Inactive</span>
                      </span>
                    </td>
                    <td class="btnAction" *ngIf="deleteAccess || editAccess">
                      <a
                        *ngIf="editAccess"
                        id="edit-button"
                        type="button"
                        href="javascript:void(0)"
                        (click)="editBVertical(bVertical.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="deleteAccess"
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmon(bVertical)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="pagination_Dropdown">
                <pagination-controls
                  id="regionListDataID"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isEditData ? "Update" : "Create" }} Business Vertical</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataSate"
            aria-expanded="false"
            aria-controls="createDataSate"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createDataSate" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !isEditData">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (isEditData && editAccess)">
            <form [formGroup]="busVerticalForm">
              <label>Name*</label>
              <input
                type="text"
                class="form-control"
                placeholder="Enter a Name"
                formControlName="vname"
                [ngClass]="{
                  'is-invalid': submitted && busVerticalForm.controls.vname.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && busVerticalForm.controls.vname.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && busVerticalForm.controls.vname.errors.required"
                >
                  Name is required.
                </div>
              </div>
              <br />
              <label>Region</label>
              <div>
                <p-multiSelect
                  [options]="commondropdownService.regionDataList"
                  formControlName="region_id"
                  optionValue="id"
                  optionLabel="rname"
                  filter="true"
                  filterBy="rname"
                  placeholder="Select a Region"
                  resetFilterOnHide="true"
                  [ngClass]="{
                    'is-invalid': submitted && busVerticalForm.controls.region_id.errors
                  }"
                >
                </p-multiSelect>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && busVerticalForm.controls.region_id.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && busVerticalForm.controls.region_id.errors.required"
                >
                  Region is required.
                </div>
              </div>
              <br />
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && busVerticalForm.controls.status.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && busVerticalForm.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && busVerticalForm.controls.status.errors.required"
                >
                  Status is required.
                </div>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  *ngIf="!isEditData"
                  (click)="addEditBVertical('')"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Business Vertical
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  id="submit"
                  *ngIf="isEditData"
                  (click)="addEditBVertical(viewbusVerticalListData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Business Vertical
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
