<!-- MVNO Menu -->
<div class="row" *ngIf="isShowMenu">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ mvnoTitle }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchMvno"
            aria-expanded="false"
            aria-controls="searchMvno"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchPreCust">
        <div class="panel-body no-padding panel-udata">
          <div [ngClass]="'col-md-6'" class="pcol" *ngIf="createAccess">
            <div>
              <a
                [routerLink]="['/home/<USER>/create']"
                class="curson_pointer"
                href="javascript:void(0)"
                (click)="createClick()"
                class="dbox"
              >
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create {{ mvnoTitle }}</h5>
              </a>
              <div
                *ngIf="isShowCreateView"
                [ngClass]="{
                  activeSubMenu: true
                }"
              ></div>
            </div>
          </div>
          <div [ngClass]="'col-md-6'" class="pcol" *ngIf="listAccess">
            <div>
              <a
                [routerLink]="['/home/<USER>/list/']"
                class="curson_pointer"
                href="javascript:void(0)"
                class="dbox"
                (click)="searchClick()"
              >
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search {{ mvnoTitle }}</h5>
              </a>
              <div
                [ngClass]="{
                  activeSubMenu: true
                }"
                *ngIf="isShowListView"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<router-outlet> </router-outlet>
