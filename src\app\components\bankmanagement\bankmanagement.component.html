<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Bank Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataBank"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchDataBank" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchBankName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchBank()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchBank()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchBank()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Bank</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataBank"
            aria-expanded="false"
            aria-controls="allDataBank"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataBank" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div>
            <div class="row">
              <div class="col-lg-12 col-md-12">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Bank Name</th>
                      <th>Bank type</th>
                      <th>Account No.</th>
                      <th>Status</th>
                      <th *ngIf="deleteAccess || editAccess">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let data of BankListData
                          | paginate
                            : {
                                id: 'bankPaginationId',
                                itemsPerPage: itemPerpageBank,
                                currentPage: currentpage,
                                totalItems: BanktotalRecords
                              };
                        index as i
                      "
                    >
                      <td>{{ data.bankname }}</td>
                      <td>{{ data.banktype }}</td>
                      <td>{{ data.accountnum }}</td>
                      <td *ngIf="data.status == 'ACTIVE' || data.status == 'Active'">
                        <span class="badge badge-success">Active</span>
                      </td>
                      <td *ngIf="data.status == 'INACTIVE' || data.status == 'Inactive'">
                        <span class="badge badge-danger">Inactive</span>
                      </td>
                      <td class="btnAction" *ngIf="deleteAccess || editAccess">
                        <a
                          *ngIf="editAccess"
                          id="edit-button"
                          href="javascript:void(0)"
                          type="button"
                          (click)="editBank(data.id)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </a>
                        <a
                          *ngIf="deleteAccess"
                          id="delete-button"
                          href="javascript:void(0)"
                          (click)="deleteConfirmonBank(data)"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="pagination_Dropdown">
                  <pagination-controls
                    id="bankPaginationId"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedBankList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isBankEdit ? "Update" : "Create" }} Bank</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataBank"
            aria-expanded="false"
            aria-controls="createDataBank"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataBank" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !isBankEdit">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (isBankEdit && editAccess)">
            <form [formGroup]="BankFormGroup">
              <label>Bank Name*</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Bank Name"
                formControlName="bankname"
                [ngClass]="{
                  'is-invalid': submitted && BankFormGroup.controls.bankname.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.bankname.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.bankname.errors.required"
                >
                  Bank Name is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.bankname.errors?.cannotContainSpace"
                >
                  <p class="error">White space are not allowed.</p>
                </div>
              </div>
              <br />
              <label>Bank Code</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Bank Code"
                formControlName="bankcode"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.bankcode.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.bankcode.errors.required"
                >
                  Bank Code is required.
                </div>
              </div>
              <br />
              <div>
                <label>Bank Type* </label>
                <p-dropdown
                  [options]="bankTypeData"
                  optionValue="text"
                  optionLabel="text"
                  filter="true"
                  filterBy="text"
                  placeholder="Select a bank Type"
                  formControlName="banktype"
                  (onChange)="selectAllbankTypeEvent($event)"
                  [ngClass]="{
                    'is-invalid': submitted && BankFormGroup.controls.banktype.errors
                  }"
                  [disabled]="isBankEdit"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && BankFormGroup.controls.banktype.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="submitted && BankFormGroup.controls.banktype.errors.required"
                  >
                    Bank Type is required.
                  </div>
                </div>
              </div>
              <br />

              <label>Account Holder Name</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Account Holder Name"
                formControlName="bankholdername"
                [ngClass]="{
                  'is-invalid': submitted && BankFormGroup.controls.bankholdername.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.bankholdername.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.bankholdername.errors.required"
                >
                  Account Holder Name is required.
                </div>
              </div>
              <br />
              <label>{{ account }}</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter Account Number"
                formControlName="accountnum"
                [readOnly]="isBankEdit"
                [ngClass]="{
                  'is-invalid': submitted && BankFormGroup.controls.accountnum.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.accountnum.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.accountnum.errors.required"
                >
                  Account Number is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.accountnum.errors.pattern"
                >
                  Only alphanumeric characters are allowed.
                </div>
              </div>
              <br />
              <label>IFSC Code</label>
              <input
                id="name"
                type="text"
                class="form-control"
                placeholder="Enter  IFSC Code"
                formControlName="ifsccode"
                [ngClass]="{
                  'is-invalid': submitted && BankFormGroup.controls.ifsccode.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.ifsccode.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.ifsccode.errors.required"
                >
                  IFSC Code is required.
                </div>
              </div>
              <br />
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && BankFormGroup.controls.status.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && BankFormGroup.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && BankFormGroup.controls.status.errors.required"
                >
                  Bank Status is required.
                </div>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="!isBankEdit"
                  id="submit"
                  (click)="addEditBank('')"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Bank
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isBankEdit"
                  id="submit"
                  (click)="addEditBank(viewBankListData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Bank
                </button>
                <button
                  type="submit"
                  class="btn btn-danger"
                  style="margin-left: 10px"
                  *ngIf="isBankEdit"
                  (click)="cancelEditBank(viewBankListData.id)"
                >
                  <i class="fa fa-times"></i>
                  Cancel
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
