// Dashboard

import { BuildingConfig } from "../RadiusUtils/RadiusConstants";

export const DASHBOARDS = {
  DASHBOARD: "dashboard",
  DASHBOARD_APPROVAL: "dashboard_approval",
  CUSTOMER_PENDING_FOR_APPROVALS: "customer_pending_for_approvals",
  CUSTOMER_PENDING_FOR_TERMINATION_APPROVALS: "customer_pending_for_termination_approvals",
  PLANS_PENDING_FOR_APPROVALS: "plans_pending_for_approvals",
  PLAN_GROUP_PENDING_FOR_APPROVALS: "plan_group_pending_for_approvals",
  PAYMENT_PENDING_FOR_APPROVALS: "payment_pending_for_approvals",
  TICKET_PENDING_FOR_APPROVALS: "ticket_pending_for_approvals",
  CHANGE_DISCOUNT_PENDING_FOR_APPROVALS: "change_discount_pending_for_approvals",
  INVOICES_PENDING_FOR_APPROVALS: "invoices_pending_for_approvals",
  PARTNER_PAYMENT_FOR_APPROVALS: "partner_payment_for_approvals",
  CUSTOMER_DOCUMENT_FOR_APPROVALS: "customer_document_for_approvals",
  INVENTORY_PENDING_FOR_APPROVALS: "inventory_pending_for_approvals",
  SPECIAL_PLAN_FOR_APPROVALS: "special_plan_for_approvals",
  DASHBOARD_SALES_CRM: "dashboard_sales_crm",
  PRODUCT_QUANTITY_OF_STAFF: "product_quantity_of_staff",
  PRODUCT_QUANTITY_BY_WAREHOUSE: "product_quantity_by_warhouse",
  DASHBOARD_INVENTORY: "dashboard_inventory",
  ASSIGNED_LEAD_LIST: "assigned_lead_list",
  TEAM_LEAD_APPROVAL_LIST: "team_lead_approval_list",
  LEAD_FOLLOWUP_LIST: "lead_followup_list",
  TEAM_LEAD_FOLLOWUP_LIST: "team_lead_followup_list",
  SALSE_DARSHBOARD: "salse_dashbaord"
};

// Master
export const MASTERS = {
  MASTER: "master",
  COUNTRY: "country",
  COUNTRY_CREATE: "country_create",
  COUNTRY_EDIT: "country_edit",
  COUNTRY_DELETE: "country_delete",
  STATE: "state",
  STATE_CREATE: "state_create",
  STATE_EDIT: "state_edit",
  STATE_DELETE: "state_delete",
  CITY: "city",
  CITY_CREATE: "city_create",
  CITY_EDIT: "city_edit",
  CITY_DELETE: "city_delete",
  PINCODE: "pincode",
  PINCODE_CREATE: "pincode_create",
  PINCODE_EDIT: "pincode_edit",
  PINCODE_DELETE: "pincode_delete",
  AREA: "area",
  AREA_CREATE: "area_create",
  AREA_EDIT: "area_edit",
  AREA_DELETE: "area_delete",
  SERVICE_AREA: "service_area",
  SERVICE_AREA_CREATE: "service_area_create",
  SERVICE_AREA_EDIT: "service_area_edit",
  SERVICE_AREA_DELETE: "service_area_delete",
  SERVICE_AREA_INVENTORY: "sa_inventory_list",
  INVESTMENT_CODE: "investment_code",
  INVESTMENT_CODE_CREATE: "investment_code_create",
  INVESTMENT_CODE_EDIT: "investment_code_edit",
  INVESTMENT_CODE_DELETE: "investment_code_delete",
  SA_INVENTORY_LIST: "sa_inventory_list",
  SA_INVENTORY_EDIT: "sa_inventory_edit",
  SA_INVENTORY_APPROVE: "sa_inventory_approve",
  SA_INVENTORY_REJECT: "sa_inventory_reject",
  SA_INVENTORY_PROGRESS: "sa_inventory_progress",
  SA_INVENTORY_ASSIGN: "sa_inventory_assign",
  SA_INVENTORY_DELETE: "sa_inventory_delete",
  BUSINESS_UNIT: "business_unit",
  BUSINESS_UNIT_CREATE: "business_unit_create",
  BUSINESS_UNIT_EDIT: "business_unit_edit",
  BUSINESS_UNIT_DELETE: "business_unit_delete",
  SUB_BUSINESS_UNIT: "sub_business_unit",
  SUB_BUSINESS_UNIT_CREATE: "sub_business_unit_create",
  SUB_BUSINESS_UNIT_EDIT: "sub_business_unit_edit",
  SUB_BUSINESS_UNIT_DELETE: "sub_business_unit_delete",
  BANK: "bank",
  BANK_CREATE: "bank_create",
  BANK_EDIT: "bank_edit",
  BANK_DELETE: "bank_delete",
  BRANCH: "branch",
  BRANCH_CREATE: "branch_create",
  BRANCH_EDIT: "branch_edit",
  BRANCH_DELETE: "branch_delete",
  REGION: "region",
  REGION_CREATE: "region_create",
  REGION_EDIT: "region_edit",
  REGION_DELETE: "region_delete",
  BUSINESS_VERTICALS: "business_verticals",
  BUSINESS_VERTICALS_CREATE: "business_verticals_create",
  BUSINESS_VERTICALS_EDIT: "business_verticals_edit",
  BUSINESS_VERTICALS_DELETE: "business_verticals_delete",
  SUB_BUSINESS_VERTICAL: "sub_business_vertical",
  SUB_BUSINESS_VERTICALS_CREATE: "sub_business_verticals_create",
  SUB_BUSINESS_VERTICALS_EDIT: "sub_business_verticals_edit",
  SUB_BUSINESS_VERTICALS_DELETE: "sub_business_verticals_delete",
  DEPARTMENT: "department",
  DEPARTMENT_CREATE: "department_create",
  DEPARTMENT_EDIT: "department_edit",
  DEPARTMENT_DELETE: "department_delete",
  POLYGON: "sarea_polygon",
  POLYGON_CREATE: "sarea_polygon_create",
  POLYGON_EDIT: "sarea_polygon_edit",
  POLYGON_DELETE: "sarea_polygon_delete",
  SUBAREA: "sub_area_management",
  BUILDINGMGMT: "building_mgmt",
  SUBAREA_CREATE: "sub_area_create",
  SUBAREA_EDIT: "sub_area_update",
  SUBAREA_DELETE: "sub_area_delete",
  BUILDING_MGMT_CREATE: "building_mgmt_create",
  BUILDING_MGMT_UPDATE: "building_mgmt_update",
  BUILDING_MGMT_DELETE: "building_mgmt_delete",
  BUILDING_CONFIG: "building_config",
  BUILDING_CONFIG_CREATE: "building_config_create"
};

// Product
export const PRODUCTS = {
  PRODUCT: "product",
  LOCATION_MASTER_CREATE: "location_master_create",
  //   LOCATION_MASTER_DELETE: "location_master_delete",
  LOCATION_MASTER_EDIT: "location_master_edit",
  LOCATION_MASTER: "location_master",
  SERVICE: "service",
  SERVICE_CREATE: "service_create",
  SERVICE_EDIT: "service_edit",
  SERVICE_DELETE: "service_delete",
  TAX: "tax",
  TAX_CREATE: "tax_create",
  TAX_EDIT: "tax_edit",
  TAX_DELETE: "tax_delete",
  CHARGE: "charge",
  CHARGE_CREATE: "charge_create",
  CHARGE_EDIT: "charge_edit",
  CHARGE_DELETE: "charge_delete",
  QOS_POLICY: "qos_policy",
  QOS_POLICY_CREATE: "qos_policy_create",
  QOS_POLICY_EDIT: "qos_policy_edit",
  QOS_POLICY_DELETE: "qos_policy_delete",
  TIME_POLICY: "time_policy",
  TIME_POLICY_CREATE: "time_policy_create",
  TIME_POLICY_EDIT: "time_policy_edit",
  TIME_POLICY_DELETE: "time_policy_delete",
  PLAN: "plan",
  PLAN_CREATE: "plan_create",
  PLAN_EDIT: "plan_edit",
  PLAN_DELETE: "plan_delete",
  PLAN_CHANGE_STATUS: "plan_change_status",
  DISCOUNT: "discount",
  DISCOUNT_CREATE: "discount_create",
  DISCOUNT_EDIT: "discount_edit",
  DISCOUNT_DELETE: "discount_delete",
  PLAN_GROUP: "plan_group",
  PLAN_GROUP_CREATE: "plan_group_create",
  PLAN_GROUP_EDIT: "plan_group_edit",
  PLAN_GROUP_DELETE: "plan_group_delete",
  SPECIAL_PLAN_MAPPING: "special_plan_mapping",
  SPECIAL_PLAN_MAPPING_CREATE: "special_plan_mapping_create",
  SPECIAL_PLAN_MAPPING_EDIT: "special_plan_mapping_edit",
  SPECIAL_PLAN_MAPPING_DELETE: "special_plan_mapping_delete",
  VOUCHER_MANAGEMENT: "voucher",
  VOUCHER_CREATE: "voucher_create",
  SHOW_VOUCHER_PROFILE: "show_voucher_profile",
  VOUCHER_EDIT: "voucher_edit",
  VOUCHER_DELETE: "voucher_delete",
  VOUCHER_GENERATE: "voucher_generate",
  SHOW_VOUCHER_BATCH: "show_voucher_batch",
  EXTEND_EXPIRY_VOUCHER_BATCH: "extend_expiry_voucher_batch",
  SHOW_MANAGE_VOUCHERS: "show_manage_vouchers",
  VOUCHER_ACTIVE: "voucher_active",
  VOUCHER_BLOCK: "voucher_block",
  VOUCHER_UNBLOCK: "voucher_unblock",
  VOUCHER_SCRAP: "voucher_scrap",
  SEND_SMS_MANAGE_VOUCHERS: "send_sms_manage_vouchers",
  DOWNLOAD_VOUCHER: "download_voucher"
};

// Partner
export const PARTNERS = {
  PARTNER_UPLOAD: "partner_upload",
  PARTNER: "partner",
  PARTNER_PLAN_BUNDLE: "partner_plan_bundle",
  PARTNER_BUNDLE_CREATE: "partner_bundle_create",
  PARTNER_BUNDLE_EDIT: "partner_bundle_edit",
  PARTNER_BUNDLE_DELETE: "partner_bundle_delete",
  PARTNER_LIST: "partner_list",
  PARTNER_CREATE: "partner_create",
  PARTNER_EDIT: "partner_edit",
  PARTNER_DELETE: "partner_delete",
  PARTNER_MANAGE_BALANCE: "partner_manage_balance",
  PARTNER_UPLOAD_DELETE: "partner_upload_delete",
  PARTNER_SHIFT_PARTNER: "partner_shift_partner",
  MANAGE_BALANCE: "manage_balance",
  MANAGE_BALANCE_CREATE: "manage_balance_create",
  PARTNER_DETAILS: "partner_details",
  PARTNER_BALANCE_DATA: "partner_balance_data",
  PARTNER_INVOICE: "partner_invoice",
  PARTNER_PAYMENT_DATA: "partner_payment_data",
  PARTNER_DOCS_EDIT: "partner_docs_edit",
  PARTNER_DOCS_DELETE: "partner_docs_delete",
  PARTNER_DOCS_CREATE: "partner_docs_create",
  PARTNER_VIEW_DOCS: "partner_view_docs"
};

// DTV
export const DTVS = {
  DTV: "dtv",
  CAS_MGMT: "cas_mgmt",
  CAS_CREATE: "cas_create",
  CAS_EDIT: "cas_edit",
  CAS_DELETE: "cas_delete",
  SECTOR_MGMT: "sector_mgmt",
  SECTOR_CREATE: "sector_create",
  SECTOR_EDIT: "sector_edit",
  SECTOR_DELETE: "sector_delete"
};

// Sales Crm
export const SALES_CRMS = {
  SALES_CRM: "sales_crm",
  LEAD_SOURCE_MASTER: "lead_source_master",
  CREATE_LEAD_SOURCE: "create_lead_source",
  EDIT_LEAD_SOURCE: "edit_lead_source",
  DELETE_LEAD_SOURCE: "delete_lead_source",
  LEAD: "lead",
  LEAD_DETAILS: "lead_details",
  LEAD_AUDIT_TRAIL: "lead_audit_trail",
  LEAD_LEAD_STATUS: "lead_lead_status",
  LEAD_LEAD_NOTES: "lead_lead_notes",
  LEAD_FOLLOW_UP: "lead_follow_up",
  LEAD_SCHEDULE: "lead_schedule",
  LEAD_RESCHEDULE: "lead_reschedule",
  LEAD_CLOSE: "lead_close",
  LEAD_REMARK: "lead_remark",
  LEAD_CALL: "lead_call",
  CREATE_LEAD: "create_lead",
  EDIT_LEAD: "edit_lead",
  CLOSE_LEAD: "close_lead",
  LEAD_REOPEN: "lead_reopen",
  ENTERPRISE_LEAD_REOPEN: "enterprise_lead_reopen",
  UPLOAD_DOCUMENT_LEAD: "upload_document_lead",
  REASSIGN_LEAD: "reassign_lead",
  REJECTED_REASON_MASTER: "rejected_reason_master",
  CREATE_REJECTED_REASON: "create_rejected_reason",
  EDIT_REJECTED_REASON: "edit_rejected_reason",
  DELETE_REJECTED_REASON: "delete_rejected_reason",
  ENTERPRISE_LEAD: "enterprise_lead",
  CREATE_ENTERPRISE_LEAD: "create_enterprise_lead",
  EDIT_ENTERPRISE_LEAD: "edit_enterprise_lead",
  CLOSE_ENTERPRISE_LEAD: "close_enterprise_lead",
  UPLOAD_ENTERPRISE_LEAD: "upload_enterprise_lead",
  REASSIGN_ENTERPRISE_LEAD: "reassign_enterprise_lead",
  ENTERPRISE_LEAD_AUDIT_TRAIL: "enterprise_lead_audit_trail",
  ENTERPRISE_LEAD_STATUS: "enterprise_lead_status",
  ENTERPRISE_LEAD_NOTES: "enterprise_lead_notes",
  ENTERPRISE_CIRCUIT: "enterprise_circuit",
  ENTERPRISE_CIRCUIT_CREATE: "enterprise_circuit_create",
  ENTERPRISE_CIRCUIT_EDIT: "enterprise_circuit_edit",
  ENTERPRISE_CIRCUIT_START: "enterprise_circuit_start",
  QUOTATION_MGMT: "quotation_mgmt",
  QM_GENERATE: "qm_generate",
  QM_DOWNLOAD_PDF: "qm_download_pdf",
  QM_SHOW: "qm_show",
  QM_SEND_MAIL: "qm_send_mail",
  QM_ASSIGN_PO: "qm_assign_po",
  QM_DOWNLOAD_PO: "qm_download_po",
  ENTERPRISE_LEAD_FOLLOW_UP: "enterprise_lead_follow_up",
  ENTERPRISE_LEAD_SCHEDULE: "enterprise_lead_schedule",
  ENTERPRISE_LEAD_RESCHEDULE: "enterprise_lead_reschedule",
  ENTERPRISE_LEAD_CLOSE: "enterprise_lead_close",
  ENTERPRISE_LEAD_REMARK: "enterprise_lead_remark",
  ENTERPRISE_LEAD_CALL: "enterprise_lead_call",
  UPLOAD_DOCUMETN_LEAD: "upload_document_lead",
  LEAD_VIEW_DOCS: "lead_view_docs",
  LEAD_DOCS_EDIT: "lead_docs_edit",
  LEAD_DOCS_DELETE: "lead_docs_delete",
  LEAD_DOCS_CREATE: "lead_docs_create",
  ENTERPRISE_DOCS_VIEW: "enterprise_docs_view",
  ENTERPRISE_DOCS_EDIT: "enterprise_docs_edit",
  ENTERPRISE_DOCS_DELETE: "enterprise_docs_delete",
  ENTERPRISE_DOCS_CREATE: "enterprise_docs_create",
  ENTERPRISE_DOCS_DOWNLOAD: "enterprise_docs_download",
  LEAD_DOCS_DOWNLOAD: "lead_docs_download"
};

// Sales Full Fillment
export const SALES_FULFILLMENTS = {
  SALES_FULFILLMENT: "sales_fulfillment",
  MY_ACHIEVEMENT: "my_achievement",
  KPI: "kpi",
  CREATE_KPI: "create_kpi",
  EDIT_KPI: "edit_kpi",
  DELETE_KPI: "delete_kpi",
  TARGET: "target",
  CREATE_TARGET: "create_target",
  EDIT_TARGET: "edit_target",
  DELETE_TARGET: "delete_target"
};

// Prepaid Customer
export const PRE_CUST_CONSTANTS = {
  PRE_CUST_DOC_DOWNLOAD: "pre_cust_doc_download",
  PRE_CUST: "pre_cust",
  PRE_CUSTS_LIST: "pre_custs_list",
  PRE_CUST_DETAILS: "pre_cust_details",
  PRE_CUST_PLANS: "pre_cust_plans",
  PRE_CUST_PLANS_DELETE_TRIAL: "pre_cust_plans_delete_trial",
  PRE_CUST_PLANS_EXTEND_TRIAL: "pre_cust_plans_extend_trial",
  PRE_CUST_PLANS_SUBSCRIBE_TRIAL: "pre_cust_plans_subscribe_trial",
  PRE_CUST_PLANS_PTP: "pre_cust_plans_ptp",
  PRE_CUST_PLANS_EXTEND_VALIDITY: "pre_cust_plans_extend_validity",
  PRE_CUST_INVOICES: "pre_cust_invoices",
  PRE_CUST_INVOICES_GENERATE: "pre_cust_invoices_generate",
  PRE_CUST_INVOICES_LIST: "pre_cust_invoices_list",
  PRE_CUST_INVOICES_VOID: "pre_cust_invoices_void",
  PRE_CUST_INVOICES_REPRINT: "pre_cust_invoices_reprint",
  PRE_CUST_INVOICES_CANCEL_REGENERATE: "pre_cust_invoices_cancel_regenerate",
  PRE_CUST_INVOICES_VIEW: "pre_cust_invoices_view",
  PRE_CUST_LEDGER: "pre_cust_ledger",
  PRE_CUST_PAYMENT: "pre_cust_payment",
  PRE_CUST_PAYMENT_RECORD: "pre_cust_payment_record",
  PRE_CUST_SESSION_HISTORY: "pre_cust_session_history",
  PRE_CUST_SESSION_HISTORY_EXPORT: "pre_cust_session_history_export",
  PRE_CUST_TICKETS: "pre_cust_tickets",
  PRE_CUST_TICKETS_CREATE_TICKET: "pre_cust_tickets_create_ticket",
  PRE_CUST_INVENTORY: "pre_cust_inventory",
  PRE_CUST_INVEN_PLAN: "pre_cust_inven_plan",
  PRE_CUST_INVEN_OTHER: "pre_cust_inven_other",
  PRE_CUST_INVEN_EXTERNAL: "pre_cust_inven_external",
  PRE_CUST_INVEN_HISTORY: "pre_cust_inven_history",
  PRE_CUST_INVEN_SWAP: "pre_cust_inven_swap",
  PRE_CUST_INVEN_REMOVE: "pre_cust_inven_remove",
  PRE_CUST_INVEN_EDIT: "pre_cust_inven_edit",
  PRE_CUST_INVEN_REPLACE: "pre_cust_inven_replace",
  PRE_CUST_INVEN_DTV: "pre_cust_inven_dtv",
  PRE_CUST_CHANGE_PLAN: "pre_cust_change_plan",
  PRE_CUST_CHANGE_DISCOUNT: "pre_cust_change_discount",
  PRE_CUST_CHANGE_DISCOUNT_AUDIT: "pre_cust_change_discount_audit",
  PRE_CUST_CHANGE_PASSWORD: "pre_cust_change_password",
  PRE_CUST_CHANGE_STATUS: "pre_cust_change_status",
  PRE_CUST_WALLET: "pre_cust_wallet",
  PRE_CUST_WALLET_WITHDRAWAL: "pre_cust_wallet_withdrawal",
  PRE_CUST_CHARGE: "pre_cust_charge",
  PRE_CUST_CHARGE_CREATE: "pre_cust_charge_create",
  PRE_CUST_CREDIT_NOTE: "pre_cust_credit_note",
  PRE_CUST_CREDIT_NOTE_CREATE: "pre_cust_credit_note_create",
  PRE_CUST_SHIFT_LOCATION: "pre_cust_shift_location",
  PRE_CUST_SHIFT_LOCATION_ADD: "pre_cust_shift_location_add",
  PRE_CUST_SERVICE: "pre_cust_service",
  PRE_CUST_SERVICE_CREATE: "pre_cust_service_create",
  PRE_CUST_SERVICE_TERMINATION: "pre_cust_service_termination",
  PRE_CUST_SERVICE_HOLD_RESUME: "pre_cust_service_hold_resume",
  PRE_CUST_SERVICE_STOP: "pre_cust_service_stop",
  PRE_CUST_REVENUE_REPORT: "pre_cust_revenue_report",
  PRE_CUST_WORKFLOW_DETAILS: "pre_cust_workflow_details",
  PRE_CUST_AUDIT_DETAILS: "pre_cust_audit_details",
  PRE_CUST_DUNNING: "pre_cust_dunning",
  PRE_CUST_DUNNING_STATUS: "pre_cust_dunning_status",
  PRE_CUST_NOTIFICATION: "pre_cust_notification",
  PRE_CUST_NOTIFICATION_STATUS: "pre_cust_notification_status",
  PRE_CUST_CHILD_CUSTS: "pre_cust_child_custs",
  PRE_CUST_CHILD_CUSTS_REMOVE: "pre_cust_child_custs_remove",
  PRE_CUST_CHILD_CUSTS_MAKE_PARENT: "pre_cust_child_custs_make_parent",
  CREATE_PRE_CUST: "create_pre_cust",
  EDIT_PRE_CUST: "edit_pre_cust",
  UPLOAD_DOCS_PRE_CUST: "upload_docs_pre_cust",
  DOCS_PRE_CUST_EDIT: "docs_pre_cust_edit",
  DOCS_PRE_CUST_DELETE: "docs_pre_cust_delete",
  PRE_CUST_NEAR_BY_DEVICE: "pre_cust_near_by_device",
  SEND_PAYMENT_LINK_PRE_CUST: "send_payment_link_pre_cust",
  CHANGE_STATUS_PRE_CUST: "change_status_pre_cust",
  PRE_CUST_CAF_LIST: "pre_cust_caf_list",
  PRE_CUST_CAF_DETAILS: "pre_cust_caf_details",
  PRE_CUST_CAF_CUST_STATUS: "pre_cust_caf_cust_status",
  PRE_CUST_CAF_CUST_PLANS: "pre_cust_caf_cust_plans",
  PRE_CUST_CAF_INVOICES: "pre_cust_caf_invoices",
  PRE_CUST_CAF_INVOICES_VIEW: "pre_cust_caf_invoices_view",
  PRE_CUST_CAF_INVOICES_REPRINT: "pre_cust_caf_invoices_reprint",
  PRE_CUST_CAF_INVOICES_GENERATE: "pre_cust_caf_invoices_generate",
  PRE_CUST_CAF_LEDGER: "pre_cust_caf_ledger",
  PRE_CUST_CAF_PAYMENT: "pre_cust_caf_payment",
  PRE_CUST_CAF_INVENTORY: "pre_cust_caf_Inventory",
  PRE_CUST_CAF_INVEN_PLAN: "pre_cust_caf_inven_plan",
  PRE_CUST_CAF_INVEN_OTHER: "pre_cust_caf_inven_other",
  PRE_CUST_CAF_INVEN_EXTERNAL: "pre_cust_caf_inven_external",
  PRE_CUST_CAF_INVEN_INVEN: "pre_cust_caf_inven_inven",
  PRE_CUST_CAF_INVEN_SWAP: "pre_cust_caf_inven_swap",
  PRE_CUST_CAF_INVEN_REMOVE: "pre_cust_caf_inven_remove",
  PRE_CUST_CAF_INVEN_EDIT: "pre_cust_caf_inven_edit",
  PRE_CUST_CAF_INVEN_REPLACE: "pre_cust_caf_inven_replace",
  PRE_CUST_CAF_INVEN_DTV: "pre_cust_caf_inven_dtv",
  PRE_CUST_CAF_CHANGE_DISCOUNT: "pre_cust_caf_change_discount",
  PRE_CUST_CAF_CHANGE_DISCOUNT_AUDIT_DETAILS: "pre_cust_caf_change_discount_audit_details",
  PRE_CUST_CAF_WALLET: "pre_cust_caf_wallet",
  PRE_CUST_CAF_CHARGE: "pre_cust_caf_charge",
  PRE_CUST_CAF_CHARGE_CREATE_CHARGE: "pre_cust_caf_charge_create_charge",
  PRE_CUST_CAF_SHIFT_LOCATION: "pre_cust_caf_shift_location",
  PRE_CUST_CAF_SHIFT_LOCATION_ADD: "pre_cust_caf_shift_location_add",
  PRE_CUST_CAF_FOLLOW_UP: "pre_cust_caf_follow_up",
  PRE_CUST_CAF_FOLLOW_UP_SCHEDULE: "pre_cust_caf_follow_up_schedule",
  PRE_CUST_CAF_FOLLOW_UP_RESCHEDULE: "pre_cust_caf_follow_up_reschedule",
  PRE_CUST_CAF_FOLLOW_UP_CLOSE: "pre_cust_caf_follow_up_close",
  PRE_CUST_CAF_FOLLOW_UP_REMARK: "pre_cust_caf_follow_up_remark",
  PRE_CUST_CAF_FOLLOW_UP_CALL: "pre_cust_caf_follow_up_call",
  PRE_CUST_CAF_SERVICE: "pre_cust_caf_service",
  PRE_CUST_CAF_SERVICE_CREATE: "pre_cust_caf_service_create",
  PRE_CUST_CAF_SERVICE_TERMINATION: "pre_cust_caf_service_termination",
  PRE_CUST_CAF_SERVICE_HOLD_RESUME: "pre_cust_caf_service_hold_resume",
  PRE_CUST_CAF_SERVICE_STOP: "pre_cust_caf_service_stop",
  PRE_CUST_CAF_SERVICE_CHANGE_PLAN: "pre_cust_caf_service_change_plan",
  CREATE_PRE_CUST_CAF_LIST: "create_pre_cust_caf_list",
  EDIT_PRE_CUST_CAF_LIST: "edit_pre_cust_caf_list",
  CLOSE_PRE_CUST_CAF_LIST: "close_pre_cust_caf_list",
  UPLOAD_PRE_CUST_CAF_LIST: "upload_pre_cust_caf_list",
  PRE_CUST_CAF_DOWNLOAD_DOCS: "pre_cust_caf_download_docs",
  PRE_CUST_CAF_DOCS_EDIT: "pre_cust_caf_docs_edit",
  PRE_CUST_CAF_DOCS_DELETE: "pre_cust_caf_docs_delete",
  PAYMENT_LINK_PRE_CUST_CAF: "payment_link_pre_cust_caf",
  PRE_CUST_CAF_NEAR_BY_DEVICE: "pre_cust_caf_near_by_device",
  PRE_CUST_LEASED_LINE_CUST: "pre_cust_leased_line_cust",
  PRE_CUST_LEASED_LINE_CREATE: "pre_cust_leased_line_create",
  PRE_CUST_LEASED_LINE_EDIT: "pre_cust_leased_line_edit",
  PRE_CUST_LEASED_LINE_DELETE: "pre_cust_leased_line_delete",
  CUST_BETA: "cust_beta",
  PRE_CUST_REJECTED_REASON_MASTER: "pre_cust_rejected_reason_master",
  CREATE_PRE_CUST_REJECTED_REASON: "create_pre_cust_rejected_reason",
  EDIT_PRE_CUST_REJECTED_REASON: "edit_pre_cust_rejected_reason",
  DELETE_PRE_CUST_REJECTED_REASON: "delete_pre_cust_rejected_reason",
  PRE_CUST_TASK_AUDIT: "pre_cust_task_audit",
  DOCS_POST_CUST_CAF_CREATE: "docs_post_cust_caf_create",
  PRE_CUST_DOWNLOAD_DOCS: "pre_cust_download_docs",
  DOCS_PRE_CUST_CREATE: "docs_pre_cust_create",
  PRE_CUST_CAF_INVENTORY_DELETE_DOCUMENT: "pre_cust_caf_inventory_delete_document",
  PRE_CUST_CAF_INVENTORY_VIEW_DOCUMENT: "pre_cust_caf_inventory_view_document",
  PRE_CUST_CAF_INVENTORY_DOWNLOAD_DOCUMENT: "pre_cust_caf_inventory_download_document",
  PRE_CUST_CAF_INVENTORY_DOWNLOAD_DOCUMENTS: "pre_cust_caf_inventory_download_documents",
  PRE_CUST_CAF_INVENTORY_UPLOAD_DOCUMENT: "pre_cust_caf_inventory_upload_document",
  PRE_CUST_INVENTORY_DELETE_DOCUMENT: "pre_cust_inventory_delete_document",
  PRE_CUST_INVENTORY_VIEW_DOCUMENT: "pre_cust_inventory_view_document",
  PRE_CUST_INVENTORY_DOWNLOAD_DOCUMENT: "pre_cust_inventory_download_document",
  PRE_CUST_INVENTORY_DOWNLOAD_DOCUMENTS: "pre_cust_inventory_download_documents",
  PRE_CUST_INVENTORY_UPLOAD_DOCUMENT: "pre_cust_inventory_upload_document",
  PRE_CUST_CHANGE_UPDATE_DISCOUNT: "pre_cust_change_update_discount",
  PRE_CUST_CAF_CHANGE_UPDATE_DISCOUNT: "pre_cust_caf_change_update_discount",
  PRE_CUST_CAF_INVOICE_PAYMENT: "pre_cust_caf_invoice_payment",
  PRE_CUST_INVOICES_SEND_TRA_INVOICE: "pre_cust_invoices_send_tra_invoice",
  PRE_CUST_PLANS_NOTES: "pre_cust_plans_notes",
  ADD_NOTES_PRE_CUST_CAF: "add_notes_pre_cust_caf",
  ADD_NOTES_PRE_CUST: "add_notes_pre_cust",
  RENEW_PAYMENT_PRE_CUST: "renew_payment_pre_cust",
  REASSIGN_PRE_CUST_CAF: "reassign_pre_cust_caf",
  PRE_CUST_CAF_INVOICES_PAYMENT: "pre_cust_caf_invoices_payment",
  CUSTOMER_CAF_NOTES: "customer_caf_notes",
  CUSTOMER_NOTES: "customer_notes",
  PRE_CALL_DETAILS: "pre_call_details",
  RETRY_PAYMENTSTATUS: "Retry_PaymentStatus",
  MANUALLY_SETTLEMENT: "Manually_Settlement",
  RETRY_CAF_PAYMENTSTATUS: "Retry_CAF_PaymentStatus",
  MANUALLY_CAF_SETTLEMENT: "Manually_Caf_Settlement",
  PRE_CUST_FEEDBACK: "pre_cust_feedback",
  PRE_CUST_IP_MANAGEMENT: "pre_cust_ip_management",
  PRE_CUST_MAC_MANAGEMENT: "pre_cust_mac_management",
  PRE_CUST_PASSWORD_VISIBILITY: "pre_cust_password_visiblity",
  PRE_CUST_CAF_PASSWORD_VISIBILITY: "pre_cust_caf_password_visiblity",
  PRE_CUST_AAA_PASSWORD: "pre_cust_aaa_password",
  PRE_CUST_CAF_AAA_PASSWORD: "pre_cust_caf_aaa_password",
  PRE_CUST_CWSC_PASSWORD: "pre_cust_cwsc_password",
  PRE_CUST_CAF_CWSC_PASSWORD: "pre_cust_caf_cwsc_password"
};

export const POST_CUST_CONSTANTS = {
  DOCS_POST_CUST_DOC_DOWNLOAD: "docs_post_cust_list_download",
  POST_CUST: "post_cust",
  POST_CUST_LIST: "post_cust_list",
  POST_CUST_DETAILS: "post_cust_details",
  POST_CUST_PLANS: "post_cust_plans",
  POST_CUST_PLANS_DELETE_TRIAL: "postapid_cust_plans_delete_trial",
  POST_CUST_PLANS_EXTEND_TRIAL: "postapid_cust_plans_extend_trial",
  POST_CUST_PLANS_SUBSCRIBE_TRIAL: "postapid_cust_plans_subscribe_trial",
  POST_CUST_PLANS_EXTEND_VALIDITY: "post_cust_plans_extend_validity",
  POST_CUST_PLANS_PTP: "post_cust_plans_ptp",
  POST_CUST_INVOICES: "post_cust_invoices",
  POST_CUST_INVOICES_GENERATE: "post_cust_invoices_generate",
  POST_CUST_INVOICES_PAYMENT_LIST: "post_cust_invoices_payment_list",
  POST_CUST_INVOICES_VOID: "post_cust_invoices_void",
  POST_CUST_INVOICES_REPRINT: "post_cust_invoices_reprint",
  POST_CUST_INVOICES_CANCEL_REGENERATE: "post_cust_invoices_cancel_regenerate",
  POST_CUST_INVOICES_VIEW: "post_cust_invoices_view",
  POST_CUST_LEDGER: "post_cust_ledger",
  POST_CUST_PAYMENT: "post_cust_payment",
  POST_CUST_PAYMENT_RECORD: "post_cust_payment_record",
  POST_CUST_SESSION_HISTORY: "post_cust_session_history",
  POST_CUST_SESSION_HISTORY_EXPORT: "post_cust_session_history_export",
  POST_CUST_TICKETS: "post_cust_tickets",
  POST_CUST_TICKETS_CREATE_TICKETS: "post_cust_tickets_create",
  POST_CUST_INVENTORY: "post_cust_inventory",
  POST_CUST_INVEN_PLAN: "post_cust_inven_plan",
  POST_CUST_INVEN_OTHER: "post_cust_inven_other",
  POST_CUST_INVEN_EXTERNAL: "post_cust_inven_external",
  POST_CUST_INVEN: "post_cust_inven",
  POST_CUST_INVEN_SWAP: "post_cust_inven_swap",
  POST_CUST_INVEN_DTV: "post_cust_inven_dtv",
  POST_CUST_INVEN_REMOVE: "post_cust_inven_remove",
  POST_CUST_INVEN_EDIT: "post_cust_inven_edit",
  POST_CUST_INVEN_REPLACE: "post_cust_inven_replace",
  POST_CUST_CHANGE_PLAN: "post_cust_change_plan",
  POST_CUST_CHANGE_DISCOUNT: "post_cust_change_discount",
  POST_CUST_AUDIT_DISCOUNT: "post_cust_audit_discount",
  POST_CUST_CHANGE_PASSWORD: "post_cust_change_password",
  POST_CUST_CHANGE_STATUS: "post_cust_change_status",
  POST_CUST_WALLET: "post_cust_wallet",
  POST_CUST_WALLET_WITHDRAWAL: "post_cust_wallet_withdrawal",
  POST_CUST_CHARGE: "post_cust_charge",
  POST_CUST_CHARGE_CREATE: "post_cust_charge_create",
  POST_CUST_CREDIT_NOTE: "post_cust_credit_note",
  POST_CUST_CREDIT_NOTE_CREATE: "post_cust_credit_note_create",
  POST_CUST_SHIFT_LOCATION: "post_cust_shift_location",
  POST_CUST_SHIFT_LOCATION_ADD: "post_cust_shift_location_add",
  POST_CUST_SERVICE: "post_cust_service",
  POST_CUST_SERVICE_CREATE: "post_cust_service_create",
  POST_CUST_SERVICE_TERMINATION: "post_cust_service_termination",
  POST_CUST_SERVICE_HOLD_RESUME: "post_cust_service_hold_resume",
  POST_CUST_SERVICE_STOP: "post_cust_service_stop",
  POST_CUST_REVENUE_REPORT: "post_cust_revenue_report",
  POST_CUST_WORKFLOW_DETAILS: "post_cust_workflow_details",
  POST_CUST_AUDIT_DETAILS: "post_cust_audit_details",
  POST_CUST_DUNNING: "post_cust_dunning",
  POST_CUST_DUNNING_STATUS: "post_cust_dunning_status",
  POST_CUST_NOTIFICATION: "post_cust_notification",
  POST_CUST_NOTIFICATION_STATUS: "post_cust_notification_status",
  POST_CUST_CHILD_CUST: "post_cust_child_cust",
  POST_CUST_CHILD_CUST_REMOVE: "post_cust_child_cust_remove",
  POST_CUST_CHILD_CUST_MAKE: "post_cust_child_cust_make",
  CREATE_POST_CUST_LIST: "create_post_cust_list",
  EDIT_POST_CUST_LIST: "edit_post_cust_list",
  UPLOAD_DOCUMENTS_POST_CUST_LIST: "upload_documents_post_cust_list",
  DOCS_POST_CUST_LIST_DOWNLOAD: "docs_post_cust_list_download",
  DOCS_POST_CUST_LIST_EDIT: "docs_post_cust_list_edit",
  DOCS_POST_CUST_LIST_DELETE: "docs_post_cust_list_delete",
  POST_CUST_NEAR_BY_DEVICE: "post_cust_near_by_device",
  SEND_POST_CUST: "send_post_cust",
  CHANGE_STATUS_POST_CUST: "change_status_post_cust",
  SEND_PAYMENT_LINK_POST_CUST: "send_payment_link_post_cust",
  POST_CUST_CAF: "post_cust_caf",
  POST_CUST_CAF_DETAILS: "post_cust_caf_details",
  POST_CUST_CAF_STATUS: "post_cust_caf_status",
  POST_CUST_CAF_PLANS: "post_cust_caf_plans",
  POST_CUST_CAF_INVOICES: "post_cust_caf_invoices",
  POST_CUST_CAF_INVOICES_GENERATE: "post_cust_caf_invoices_generate",
  POST_CUST_CAF_INVOICES_VIEW: "post_cust_caf_invoices_view",
  POST_CUST_CAF_INVOICES_REPRINT: "post_cust_caf_invoices_reprint",
  POST_CUST_CAF_LEDGER: "post_cust_caf_ledger",
  POST_CUST_CAF_PAYMENT: "post_cust_caf_payment",
  POST_CUST_CAF_INVENTORY: "post_cust_caf_Inventory",
  POST_CUST_CAF_INVEN_PLAN: "post_cust_caf_inven_plan",
  POST_CUST_CAF_INVEN_OTHER: "post_cust_caf_inven_other",
  POST_CUST_CAF_INVEN_EXTERNAL: "post_cust_caf_inven_external",
  POST_CUST_CAF_INVEN_HISTORY: "post_cust_caf_inven_history",
  POST_CUST_CAF_INVEN_SWAP: "post_cust_caf_inven_swap",
  POST_CUST_CAF_INVEN_DTV: "post_cust_caf_inven_dtv",
  POST_CUST_CAF_INVEN_REMOVE: "post_cust_caf_inven_remove",
  POST_CUST_CAF_INVEN_EDIT: "post_cust_caf_inven_edit",
  POST_CUST_CAF_INVEN_REPLACE: "post_cust_caf_inven_replace",
  POST_CUST_CAF_CHANGE_DISCOUNT: "post_cust_caf_change_discount",
  POST_CUST_CAF_CHANGE_DISCOUNT_AUDIT: "post_cust_caf_change_discount_audit",
  POST_CUST_CAF_WALLET: "post_cust_caf_wallet",
  POST_CUST_CAF_CHARGE: "post_cust_caf_charge",
  POST_CUST_CAF_CHARGE_CREATE: "post_cust_caf_charge_create",
  POST_CUST_CAF_SHIFT_LOCATION: "post_cust_caf_shift_location",
  POST_CUST_CAF_FOLLOW_UP: "post_cust_caf_follow_up",
  POST_CUST_CAF_SCHEDULE: "post_cust_caf_schedule",
  POST_CUST_CAF_RESCHEDULE: "post_cust_caf_reschedule",
  POST_CUST_CAF_FOLLOW_UP_CLOSE: "post_cust_caf_follow_up_close",
  POST_CUST_CAF_REMARK: "post_cust_caf_remark",
  POST_CUST_CAF_CALL: "post_cust_caf_call",
  POST_CUST_CAF_SERVICE: "post_cust_caf_service",
  POST_CUST_CAF_SERVICE_CREATE: "post_cust_caf_service_create",
  POST_CUST_CAF_SERVICE_TERMINATION: "post_cust_caf_service_termination",
  POST_CUST_CAF_SERVICE_HOLD_RESUME: "post_cust_caf_service_hold_resume",
  POST_CUST_CAF_SERVICE_STOP: "post_cust_caf_service_stop",
  POST_CUST_CAF_SERVICE_CHANGE_PLAN: "post_cust_caf_service_change_plan",
  CREATE_POST_CUST_CAF: "create_post_cust_caf",
  EDIT_POST_CUST_CAF: "edit_post_cust_caf",
  CLOSE_POST_CUST_CAF: "close_post_cust_caf",
  UPLOAD_DOCUMENTS_POST_CUST_CAF: "upload_documents_post_cust_caf",
  DOCS_POST_CUST_CAF_DOWNLOAD: "docs_post_cust_caf_download",
  DOCS_POST_CUST_CAF_EDIT: "docs_post_cust_caf_edit",
  DOCS_POST_CUST_CAF_DELETE: "docs_post_cust_caf_delete",
  POST_CUST_CAF_NEARBY_DEVICE: "post_cust_caf_nearby_device",
  PAYMENT_LINK_POST_CUST_CAF: "payment_link_post_cust_caf",
  REASSIGN_POST_CUST_CAF: "reassign_post_cust_caf",
  DOCS_POST_CUST_DELETE: "docs_post_cust_delete",
  DOCS_POST_CUST_EDIT: "docs_post_cust_edit",
  POST_CUST_TASK_AUDIT: "post_cust_task_audit",
  POST_RETRY_PAYMENTSTATUS: "post_retry_paymentstatus",
  POST_MANUALLY_SETTLEMENT: "post_manually_settlement",
  POST_RETRY_CAF_PAYMENTSTATUS: "post_caf_retry_paymentstatus",
  POST_MANUALLY_CAF_SETTLEMENT: "post_caf_manually_settlement",
  POST_CALL_DETAILS: "post_call_details",
  POST_CUST_FEEDBACK: "post_cust_feedback",
  POST_CUST_IP_MANAGEMENT: "post_cust_ip_management",
  POST_CUST_MAC_MANAGEMENT: "post_cust_mac_management",
  POST_CUST_PASSWORD_VISIBILITY: "post_cust_password_visiblity",
  POST_CUST_CAF_PASSWORD_VISIBILITY: "post_cust_caf_password_visiblity",
  POST_CUST_AAA_PASSWORD: "post_cust_aaa_password",
  POST_CUST_CAF_AAA_PASSWORD: "post_cust_caf_aaa_password",
  POST_CUST_CWSC_PASSWORD: "post_cust_cwsc_password",
  POST_CUST_CAF_CWSC_PASSWORD: "post_cust_caf_cwsc_password"
};

export const INVOICE_SYSTEMS = {
  INVOICE_SYSTEM: "invoice_system",
  BILL_TEMPLATE: "bill_template",
  CREATE_BILL_TEMPLATE: "create_bill_template",
  EDIT_BILL_TEMPLATE: "edit_bill_template",
  DELETE_BILL_TEMPLATE: "delete_bill_template",
  QUICK_INVOICE: "quick_invoice",
  CREATE_QUICK_INVOICE: "create_quick_invoice",
  POSTPAID_BILL_RUNS: "postpaid_bill_runs",
  POSTPAID_GENE_BILL_RUN: "postpaid_gene_bill_run",
  POSTPAID_BILL_RUN_MASTER: "postpaid_bill_run_master",
  POSTPAID_INVOICE_MASTER: "postpaid_invoice_master",
  PREPAID_BILL_RUNS: "prepaid_bill_runs",
  PREPAID_INVOICE_MASTER: "prepaid_invoice_master",
  PREPAID_INVOICE_MASTER_RECORD: "prepaid_invoice_master_record",
  POSTPAID_TRAIL_BILL_RUN: "postpaid_trail_bill_run",
  POSTPAID_GENE_TRIAL_BILL_RUN: "postpaid_gene_trial_bill_run",
  POSTPAID_TRIAL_BILL_RUN_MASTER: "postpaid_trial_bill_run_master",
  POSTPAID_TRIAL_BILL_RUN_INVOICE: "postpaid_trial_bill_run_invoice",
  INVOICE_REVENUE_REPORT: "invoice_revenue_report",
  POST_BILL_RUN_INVOICE_GENERATE: "post_bill_run_invoice_generate",
  POST_BILL_RUN_INVOICE_PAY_LIST: "post_bill_run_invoice_pay_list",
  POST_BILL_RUN_INVOICE_VOID: "post_bill_run_invoice_void",
  POST_BILL_RUN_INVOICE_REPRINT: "post_bill_run_invoice_reprint",
  POST_BILL_RUN_INVOICE_CR: "post_bill_run_invoice_cr",
  POST_BILL_RUN_INVOICE_VIEW: "post_bill_run_invoice_view",
  PRE_BILL_INVOICE_DOWNLOAD: "pre_bill_invoice_download",
  PRE_BILL_INVOICE_PAY_LIST: "pre_bill_invoice_pay_list",
  PRE_BILL_INVOICE_VOID: "pre_bill_invoice_void",
  PRE_BILL_INVOICE_REPRINT: "pre_bill_invoice_reprint",
  PRE_BILL_INVOICE_CR: "pre_bill_invoice_cr",
  PRE_BILL_INVOICE_VIEW: "pre_bill_invoice_view",
  PRE_BILL_INVOICE_MILESTONE: "pre_bill_invoice_milestone"
};

export const PAYMENT_SYSTEMS = {
  PAYMENT_SYSTEM: "payment_system",
  RECORD_PAYMENT: "record_payment",
  SEARCH_PAYMENT: "search_payment",
  PAYMENT_APPROVE: "payment_approve",
  PAYMENT_REJECT: "payment_reject",
  PAYMENT_BATCH_AUDIT: "payment_batch_audit",
  PAYMENT_CREATE_BATCH: "payment_create_batch",
  PAY_BATCH_PAYMENT: "pay_batch_payment",
  PAY_BATCH_PAY_DELETE: "pay_batch_pay_delete",
  PAY_BATCH_PAY_ASSIGN: "pay_batch_pay_assign",
  PAY_BATCH_PAY_CREATE: "pay_batch_pay_create",
  PAYMENT_DOWNLOAD: "payment_download",
  PAYMENT_REASSIGN: "payment_reassign"
};

export const CREDIT_NOTES = {
  CREDIT_NOTE: "credit_notes",
  GENERATE_CREDIT_NOTE: "generate_credit_note",
  SEARCH_CREDIT_NOTE: "search_credit_note",
  CREDIT_NOTE_DOWNLOAD: "credit_note_download",
  CREDIT_NOTE_REPRINT: "credit_note_reprint",
  CREDIT_NOTE_REASSIGN: "credit_note_reassign"
};

export const TICKETING_SYSTEMS = {
  TICKETING_SYSTEM: "ticketing_system",
  TAT_TICKET: "tat_ticket",
  TAT_TICKET_CREATE: "tat_ticket_create",
  TAT_TICKET_EDIT: "tat_ticket_edit",
  TAT_TICKET_DELETE: "tat_ticket_delete",
  PROBLEM_DOMAIN: "problem_domain",
  PROBLEM_DOMAIN_CREATE: "problem_domain_create",
  PROBLEM_DOMAIN_EDIT: "problem_domain_edit",
  PROBLEM_DOMAIN_DELETE: "problem_domain_delete",
  SUB_PB_DOMAIN: "sub_pb_domain",
  SUB_PB_DOMAIN_CREATE: "sub_pb_domain_create",
  SUB_PB_DOMAIN_EDIT: "sub_pb_domain_edit",
  SUB_PB_DOMAIN_DELETE: "sub_pb_domain_delete",
  ROOT_CAUSE_MASTER: "root_cause_master",
  ROOT_CAUSE_CREATE: "root_cause_create",
  ROOT_CAUSE_EDIT: "root_cause_edit",
  ROOT_CAUSE_DELETE: "root_cause_delete",
  TICKET: "ticket",
  TICKET_CREATE: "ticket_create",
  TICKET_PICK: "ticket_pick",
  TICKET_FOLLOW_UP: "ticket_follow_up",
  TICKET_SLA_COUNTER: "ticket_sla_counter",
  TICKET_DETAILS: "ticket_details",
  TICKET_ETR: "ticket_etr",
  TICKET_REMARKS: "ticket_remarks",
  TICKET_CONVERSATION: "ticket_conversation",
  TICKET_ATTACHMENT: "ticket_attachment",
  TICKET_ATTACHMENT_DOWNLOAD: "ticket_attachment_download",
  TICKET_ETR_EXCEL_DOWNLOAD: "ticket_etr_excel_download",
  TICKET_ASSIGN: "ticket_assign",
  TICKET_CHANGE_PRIORITY: "ticket_change_priority",
  TICKET_LINE_TICKET: "ticket_line_ticket",
  TICKET_UPLOAD_DOC: "ticket_upload_doc",
  TICKET_CHANGE_PB_DOMAIN: "ticket_change_pb_domain",
  TICKET_EDIT: "ticket_edit",
  TICKET_CHANGE_STATUS: "ticket_change_status",
  TICKET_BULK_REASSIGN: "ticket_bulk_reassign",
  TICKET_OPEN_OPPORTUNITY: "ticket_open_opportunity",
  OPEN_OPPORTUNITY_CREATE: "open_opportunity_create",
  OPEN_OPPORTUNITY_DELETE: "open_opportunity_delete"
};

export const DUNNINGS = {
  DUNNING: "dunning",
  DUNNING_RULES: "dunning_rules",
  DUNNING_RULES_CREATE: "dunning_rules_create",
  DUNNING_RULES_EDIT: "dunning_rules_edit",
  DUNNING_RULES_DELETE: "dunning_rules_delete"
};

export const RADIUS_CONSTANTS = {
  RADIUS: "radius",
  RADIUS_GROUP: "radius_group",
  RADIUS_GROUP_CREATE: "radius_group_create",
  RADIUS_GROUP_EDIT: "radius_group_edit",
  RADIUS_GROUP_DELETE: "radius_group_delete",
  RADIUS_CLIENT: "radius_client",
  RADIUS_CLIENT_CREATE: "radius_client_create",
  RADIUS_CLIENT_EDIT: "radius_client_edit",
  RADIUS_CLIENT_DELETE: "radius_client_delete",
  RADIUS_CUST: "radius_cust",
  RADIUS_CUST_DETAILS: "radius_cust_details",
  RADIUS_CUST_DETAILS_CUST_PLAN: "radius_cust_details_cust_plan",
  RADIUS_CUST_DETAILS_CDR_SESSION: "radius_cust_details_cdr_session",
  RADIUS_CUST_DETAILS_EXPORT_EXCEL: "radius_cust_details_export_excel",
  RADIUS_CDR: "radius_cdr",
  RADIUS_CDR_CSV: "radius_cdr_csv",
  RADIUS_CDR_XLS: "radius_cdr_xls",
  RADIUS_CDR_PDF: "radius_cdr_pdf",
  RADIUS_LIVE_USERS: "radius_live_users",
  RADIUS_LIVE_USERS_CSV: "radius_live_users_csv",
  RADIUS_LIVE_USERS_XLS: "radius_live_users_xls",
  RADIUS_LIVE_USERS_PDF: "radius_live_users_pdf",
  RADIUS_LIVE_USERS_DELETE: "radius_live_users_delete",
  RADIUS_LIVE_USERS_DISCONNECT: "radius_live_users_disconnect",
  RADIUS_PROXY_CONFIG: "radius_proxy_config",
  RADIUS_PROXY_CONFIG_CREATE: "radius_proxy_config_create",
  RADIUS_PROXY_CONFIG_EDIT: "radius_proxy_config_edit",
  RADIUS_PROXY_CONFIG_DELETE: "radius_proxy_config_delete",
  RADIUS_DICT: "radius_dict",
  RADIUS_DICT_ATTRIBUTES: "radius_dict_attributes",
  RADIUS_DICT_VALUE: "radius_dict_value",
  RADIUS_DICT_VALUE_CREATE: "radius_dict_value_create",
  RADIUS_DICT_VALUE_EDIT: "radius_dict_value_edit",
  RADIUS_DICT_VALUE_DELETE: "radius_dict_value_delete",
  RADIUS_DICT_ATTRIBUTES_CREATE: "radius_dict_attributes_create",
  RADIUS_DICT_ATTRIBUTES_EDIT: "radius_dict_attributes_edit",
  RADIUS_DICT_ATTRIBUTES_DELETE: "radius_dict_attributes_delete",
  RADIUS_DICT_CREATE: "radius_dict_create",
  RADIUS_DICT_EDIT: "radius_dict_edit",
  RADIUS_DICT_DELETE: "radius_dict_delete",
  RADIUS_AUTHEN_AUDIT: "radius_authen_audit",
  RADIUS_AUTHEN_AUDIT_DELETE: "radius_authen_audit_delete",
  RADIUS_COA_DM: "radius_coa_dm",
  RADIUS_COA_DM_CREATE: "radius_coa_dm_create",
  RADIUS_COA_DM_EDIT: "radius_coa_dm_edit",
  RADIUS_COA_DM_DELETE: "radius_coa_dm_delete",
  RADIUS_PROFILES: "radius_profiles",
  RADIUS_PROFILES_CREATE: "radius_profiles_create",
  RADIUS_PROFILES_EDIT: "radius_profiles_edit",
  RADIUS_PROFILES_DELETE: "radius_profiles_delete",
  RADIUS_DEVICE: "radius_device",
  RADIUS_DEVICE_CREATE: "radius_device_create",
  RADIUS_DEVICE_EDIT: "radius_device_edit",
  RADIUS_DEVICE_DELETE: "radius_device_delete",
  RADIUS_DEVICE_STATUS: "radius_device_status",
  RADIUS_DB_MAPPING: "radius_db_mapping",
  RADIUS_DB_MAPPING_CREATE: "radius_db_mapping_create",
  RADIUS_DB_MAPPING_EDIT: "radius_db_mapping_edit",
  RADIUS_DB_MAPPING_DELETE: "radius_db_mapping_delete",
  RADIUS_DRIVER_MANAGEMENT: "driver_management",
  RADIUS_DRIVER_CREATE: "driver_management_create",
  RADIUS_DRIVER_EDIT: "driver_management_edit",
  RADIUS_DRIVER_DELETE: "driver_management_delete",
  NETCONF_CLIENT: "netConf_client",
  NETCONF_CLIENT_CREATE: "netConf_client_create",
  NETCONF_CLIENT_EDIT: "netConf_client_edit",
  NETCONF_CLIENT_DELETE: "netConf_client_delete",
  NETCONF_CUST: "netConf_cust",
  NETCONF_CUST_DETAILS: "netConf_cust_details",
  NETCONF_CUST_DETAILS_CUST_PLAN: "netConf_cust_details_cust_plan",
  NETCONF_CUST_DETAILS_CDR_SESSION: "netConf_cust_details_cdr_session",
  NETCONF_CUST_DETAILS_EXPORT_EXCEL: "netConf_cust_details_export_excel",
  NETCONF_CUST_MANAGEMENT: "netconf_customers_management",
  RADIUS_CUST_CREATE: "radius_cust_create",
  RADIUS_CUST_EDIT: "radius_cust_edit",
  RADIUS_CUST_CHANGE_STATUS: "radius_cust_change_status",

  RADIUS_ACCESS_RESPONSE: "access_Response",
  RADIUS_ACCESS_RESPONSE_CREATE: "access_Response_create",
  RADIUS_ACCESS_RESPONSE_EDIT: "access_Response_edit",
  RADIUS_ACCESS_RESPONSE_DELETE: "access_Response_delete",

  RADIUS_IP_MANAGEMENT: "IP_managment",
  RADIUS_IP_MANAGEMENT_CREATE: "IP_managment_create",
  RADIUS_IP_MANAGEMENT_EDIT: "IP_managment_edit",
  RADIUS_IP_MANAGEMENT_DELETE: "IP_managment_delete",

  RADIUS_FAULTY_MAC: "Faulty_mac_managment",
  RADIUS_FAULTY_MAC_CREATE: "Faulty_mac_managment_create",
  RADIUS_FAULTY_MAC_EDIT: "Faulty_mac_managment_edit",
  RADIUS_FAULTY_MAC_DELETE: "Faulty_mac_managment_delete",

  RADIUS_VLAN_MANAGMENT: "VLAN_managment",
  RADIUS_VLAN_MANAGMENT_CREATE: "VLAN_managment_create",
  RADIUS_VLAN_MANAGMENT_EDIT: "VLAN_managment_edit",
  RADIUS_VLAN_MANAGMENT_DELETE: "VLAN_managment_delete"
};

export const TACACS = {
  TACACS: "tacacs",
  TACACS_CONFIG: "tacacs_config",
  TACACS_CONFIG_START: "tacacs_config_start",
  TACACS_CONFIG_STOP: "tacacs_config_stop",
  TACACS_CONFIG_EDIT: "tacacs_config_edit",
  TACACS_STAFF: "tacacs_staff",
  TACACS_DEVICE: "tacacs_device",
  TACACS_DEVICE_CREATE: "tacacs_device_create",
  TACACS_DEVICE_EDIT: "tacacs_device_edit",
  TACACS_DEVICE_DELETE: "tacacs_device_delete",
  TACACS_DEVICE_GROUP: "tacacs_device_group",
  TACACS_DEVICE_GROUP_CREATE: "tacacs_device_group_create",
  TACACS_DEVICE_GROUP_EDIT: "tacacs_device_group_edit",
  TACACS_DEVICE_GROUP_DELETE: "tacacs_device_group_delete",
  TACACS_AUDIT: "tacacs_audit",
  TACACS_CMD_SET: "tacacs_cmd_set",
  TACACS_CMD_SET_CREATE: "tacacs_cmd_set_create",
  TACACS_CMD_SET_EDIT: "tacacs_cmd_set_edit",
  TACACS_CMD_SET_DELETE: "tacacs_cmd_set_delete",
  TACACS_ACCESS_LEVEL_GROUP: "tacacs_access_level_group",
  TACACS_ACCESS_LEVEL_GROUP_CREATE: "tacacs_access_level_group_create",
  TACACS_ACCESS_LEVEL_GROUP_EDIT: "tacacs_access_level_group_edit",
  TACACS_ACCESS_LEVEL_GROUP_DELETE: "tacacs_access_level_group_delete"
};

export const NETWORKS = {
  NETWORK: "network",
  NETWORK_DEVICE: "network_device",
  NETWORK_DEVICE_CREATE: "network_device_create",
  NETWORK_DEVICE_EDIT: "network_device_edit",
  NETWORK_DEVICE_DELETE: "network_device_delete",
  NETWORK_DEVICE_PARENT_MAPPING: "network_device_parent_mapping",
  NETWORK_MAP: "network_map",
  IP: "ip",
  IP_CREATE: "ip_create",
  IP_EDIT: "ip_edit",
  IP_DELETE: "ip_delete"
};

export const INVENTORYS = {
  INVENTORY: "inventory",
  MANUFACTURER: "manufacturer",
  MANUFACTURER_CREATE: "manufacturer_create",
  MANUFACTURER_EDIT: "manufacturer_edit",
  MANUFACTURER_DELETE: "manufacturer_delete",
  PRODUCT_CATEGORY: "product_category",
  PRODUCT_CATEGORY_CREATE: "product_category_create",
  PRODUCT_CATEGORY_EDIT: "product_category_edit",
  PRODUCT_CATEGORY_DELETE: "product_category_delete",
  INVEN_PRODUCT: "inven_product",
  INVEN_PRODUCT_CREATE: "inven_product_create",
  INVEN_PRODUCT_EDIT: "inven_product_edit",
  INVEN_PRODUCT_DELETE: "inven_product_delete",
  POP: "pop",
  POP_CREATE: "pop_create",
  POP_EDIT: "pop_edit",
  POP_DELETE: "pop_delete",
  POP_INVEN_LIST: "pop_inven_list",
  POP_INVEN_LIST_ASSIGN_INVENTORY: "pop_inven_list_assign_inventory",
  WAREHOUSE: "warehouse",
  WAREHOUSE_CREATE: "warehouse_create",
  WAREHOUSE_EDIT: "warehouse_edit",
  WAREHOUSE_DELETE: "warehouse_delete",
  INVEN_INWARDS: "inven_inwards",
  INVEN_INWARDS_CREATE: "inven_inwards_create",
  INVEN_INWARDS_EDIT: "inven_inwards_edit",
  INVEN_INWARDS_DELETE: "inven_inwards_delete",
  INVEN_INWARDS_SHOW_MAC: "inven_inwards_show_mac",
  INVEN_INWARDS_APPROVE: "inven_inwards_approve",
  INVEN_OUTWARDS: "inven_outwards",
  INVEN_OUTWARDS_CREATE: "inven_outwards_create",
  INVEN_OUTWARDS_EDIT: "inven_outwards_edit",
  INVEN_OUTWARDS_DELETE: "inven_outwards_delete",
  INVEN_OUTWARDS_SHOW_MAC: "inven_outwards_show_mac",
  INVEN_OUTWARDS_ADD_MAC: "inven_outwards_add_mac",
  EXT_ITEM: "ext_item",
  EXT_ITEM_CREATE: "ext_item_create",
  EXT_ITEM_EDIT: "ext_item_edit",
  EXT_ITEM_DELETE: "ext_item_delete",
  EXT_ITEM_ADD_MAC_ADDRESS: "ext_item_add_mac_address",
  EXT_ITEM_SHOW_MAC_ADDRESS: "ext_item_show_mac_address",
  EXT_ITEM_APPROVE: "ext_item_approve",
  EXT_ITEM_REJECT: "ext_item_reject",
  EXT_ITEM_BULK_CONSUMPTION: "ext_item_bulk_consumption",
  CREATE_BULK_CONSUMPTION: "create_bulk_consumption",
  EDIT_BULK_CONSUMPTION: "edit_bulk_consumption",
  DELETE_BULK_CONSUMPTION: "delete_bulk_consumption",
  VIEW_INWARD_MAC_MAPPING: "view_inward_mac_mapping",
  INVEN_REQUEST: "inven_request",
  RAISED_INVEN_REQUEST: "raised_inven_request",
  INVEN_REQUEST_DELETE: "inven_request_delete",
  ASSIGNED_INVEN_REQUEST: "assigned_inven_request",
  ASSIGNED_INVEN_REQUEST_FORWARD: "assigned_inven_request_forward",
  ASSIGNED_INVEN_REQUEST_FULLFILLMENT: "assigned_inven_request_fullfillment",
  INVEN_DETAILS: "inven_details",
  INVEN_DETAILS_CHANGE_TYPE: "inven_details_change_type",
  INVEN_DETAILS_WARRANTY: "inven_details_warranty",
  INVEN_DETAILS_STATUS: "inven_details_status",
  INVEN_DETAILS_OWNERSHIP_STATUS: "inven_details_ownership_status",
  INVEN_DETAILS_ASSIGNED_INVENTORY: "inven_details_assigned_inventory",
  INVEN_DETAILS_INVEN_ASSIGNED_TO_CUST: "inven_details_inven_assigned_to_cust",
  INVEN_DETAILS_INVEN_ASSIGNED_TO_POP: "inven_details_inven_assigned_to_pop",
  INVEN_DETAILS_INVEN_ASSIGNED_TO_SA: "inven_details_inven_assigned_to_sa",
  INVEN_DETAILS_INVEN_ASSIGNED_SERIALIZED: "inven_details_inven_assigned_serialized",
  INVEN_DETAILS_INVEN_ASSIGNED_NONSERIALIZED: "inven_details_inven_assigned_nonserialized",
  INVEN_LIST_EDIT: "inven_list_edit",
  INVEN_LIST_DELETE: "inven_list_delete",
  INVEN_LIST_APPROVE: "inven_list_approve",
  INVEN_LIST_REJECT: "inven_list_reject",
  INVEN_LIST_PROGRESS: "inven_list_progress",
  INWARD_REJECT: "inward_reject",
  BULK_CONSUMPTION_APPROVE: "bulk_consumption_approve",
  BULK_CONSUMPTION_REJECT: "bulk_consumption_reject",
  ASSIGNED_INVEN_REQUEST_APPROVE: "assigned_inven_request_approve",
  ASSIGNED_INVEN_REQUEST_REJECT: "assigned_inven_request_reject"
};

export const NOTIFICATIONS = {
  NOTIFICATION: "notifications",
  EMAIL_CONFIG: "email_config",
  EMAIL_CONFIG_CREATE: "email_config_create",
  SMS_CONFIG_CREATE: "sms_config_create",
  EMAIL_CONFIG_EDIT: "email_config_edit",
  SMS_CONFIG: "sms_config",
  SMS_CONFIG_EDIT: "sms_config_edit",
  NOTIFICATION_EMAIL: "notification_email",
  NOTIFICATION_EMAIL_CREATE: "notification_email_create",
  NOTIFICATION_EMAIL_EDIT: "notification_email_edit",
  NOTIFICATION_EMAIL_DELETE: "notification_email_delete",
  NOTIFICATION_EMAIL_SEND: "notification_email_send",
  NOTIFICATION_SMS: "notification_sms",
  NOTIFICATION_SMS_CREATE: "notification_sms_create",
  NOTIFICATION_SMS_EDIT: "notification_sms_edit",
  NOTIFICATION_SMS_DELETE: "notification_sms_delete",
  NOTIFICATION_SMS_SEND: "notification_sms_send",
  NOTIFICATION_OTP: "notification_otp",
  NOTIFICATION_OTP_CREATE: "notification_otp_create",
  NOTIFICATION_OTP_EDIT: "notification_otp_edit",
  NOTIFICATION_OTP_DELETE: "notification_otp_delete"
};

export const WORKFLOWS = {
  WORKFLOW: "workflow",
  TEAMS: "teams",
  TEAMS_CREATE: "teams_create",
  TEAMS_EDIT: "teams_edit",
  TEAMS_DELETE: "teams_delete",
  TEAMS_HIERARCHY: "teams_hierarchy",
  TAT_METRICS: "tat_metrics",
  TAT_METRICS__CREATE: "tat_metrics__create",
  TAT_METRICS__EDIT: "tat_metrics__edit",
  TAT_METRICS__DELETE: "tat_metrics__delete",
  WORKFLOW_LIST: "workflow_list",
  WORKFLOW_CREATE: "workflow_create",
  WORKFLOW_EDIT: "workflow_edit",
  WORKFLOW_DELETE: "workflow_delete"
};

export const INTEGRATION_SYSTEMS = {
  INTEGRATION_SYSTEM: "integration_system",
  FINANCE_INTEGRATION: "finance_integration",
  MICROSOFT_DYNAMICS: "microsoft_dynamics",
  MICROSOFT_DYNAMICS_CREATE: "microsoft_dynamics_create",
  MICROSOFT_DYNAMICS_EDIT: "microsoft_dynamics_edit",
  MICROSOFT_NAV_DETAILS: "microsoft_nav_details",
  NAV_REPORT_SUBMIT: "nav_report_submit",
  NAV_REPORT_PUSH: "nav_report_push",
  ACS_MASTER: "acs_master",
  ACS_MASTER_CREATE: "acs_master_create",
  ACS_MASTER_EDIT: "acs_master_edit",
  ACS_MASTER_DELETE: "acs_master_delete",
  GOV_INTEGRATION: "gov_integration",
  GOV_INTEGRATION_CREATE: "gov_integration_create",
  GOV_INTEGRATION_EDIT: "gov_integration_edit",
  GOV_INTEGRATION_DELETE: "gov_integration_delete",
  INTEGRATION_CONFIG_CREATE: "integration_configuration_create",
  INTEGRATION_CONFIG_EDIT: "integration_configuration_Edit",
  INTEGRATION_CONFIG_DELETE: "integration_configuration_Delete",
  INTEGRATION_CONFIG: "integration_configuration",
  THIRD_PARTY_MENU: "thirdparty_menu",
  THIRD_PARTY_MENU_CREATE: "thirdparty_menu_create",
  THIRD_PARTY_MENU_EDIT: "thirdparty_menu_edit",
  THIRD_PARTY_MENU_DELETE: "thirdparty_menu_delete"
};

export const SETTINGS = {
  SETTING: "setting",
  ROLE: "role",
  ROLE_CREATE: "role_create",
  ROLE_EDIT: "role_edit",
  ROLE_DELETE: "role_delete",
  STAFF: "staff",
  STAFF_CREATE: "staff_create",
  STAFF_EDIT: "staff_edit",
  STAFF_DETAILS: "staff_details",
  STAFF_DETAILS_RECEIPT: "staff_details_receipt",
  STAFF_CHANGE_PASSWORD: "staff_change_password",
  STAFF_CREATE_RECEIPT: "staff_create_receipt",
  STAFF_DETAILS_WALLET: "staff_details_wallet",
  MY_PROFILE: "my_profile",
  STAFF_RECEIPT: "staff_receipt",
  STAFF_RECEIPT_MGMT: "staff_receipt_mgmt",
  MY_PROFILE_WALLET: "my_profile_wallet",
  MY_PROFILE_CHANGE_PASSWORD: "my_profile_change_password",
  ORGANIZATION: "organization",
  ORGANIZATION__INVOICES: "organization__invoices",
  ORGANIZATION__LEDGER: "organization__ledger",
  ORGANIZATION__WALLET: "organization__wallet",
  SYSTEM_CONFIGURATION: "system_configuration",
  SYSTEM_CONFIGURATION_CREATE: "system_configuration_create",
  SYSTEM_CONFIGURATION_EDIT: "system_configuration_edit",
  TEMPLATE: "template",
  TEMPLATE_SAVE: "template_save",
  FIELD_TEMPLATE_MAPPING: "field_template_mapping",
  FIELD_TMPLT_CUST: "field_tmplt_cust",
  FIELD_TMPLT_CUST_SAVE: "field_tmplt_cust_save",
  FIELD_TMPLT_LEAD: "field_tmplt_lead",
  FIELD_TMPLT_LEAD_SAVE: "field_tmplt_lead_save",
  PAYMENT_GATEWAY_CONFIGURATION: "payment_gateway_configuration",
  PAYMENT_GATEWAY_CONFIGURATION_CREATE: "payment_gateway_configuration_create",
  PAYMENT_GATEWAY_CONFIGURATION_EDIT: "payment_gateway_configuration_edit",
  PAYMENT_GATEWAY_CONFIGURATION_DELETE: "payment_gateway_configuration_delete",
  PROFILE_MANAGEMENT: "profile_management",
  PROFILE_MANAGEMENT_CREATE: "profile_management_create",
  PROFILE_MANAGEMENT_EDIT: "profile_management_edit",
  PROFILE_MANAGEMENT_DELETE: "profile_management_delete",
  MIGRATION: "migration",
  ISP_MANAGEMENT: "isp_management",
  ISP_DATA_TRANSFER: "isp_data_transfer",
  ISP_MANAGEMENT_EDIT: "isp_management_edit",
  ISP_MANAGEMENT_CREATE: "isp_management_create",
  ISP_MANAGEMENT_LIST: "isp_management_list",
  ISP_UPLOAD_DOCUMENT: "isp_upload_document",
  ISP_REJECT_DOCUMENT: "isp_reject_document",
  ISP_APPROVE_DOCUMENT: "isp_approve_document",
  ISP_DETAILS: "isp_details",
  ISP_INVOICE_DETAILS: "isp_invoice_details",
  ISP_PAYMENT_DETAILS: "isp_payment_details",
  ISP_DUNNING_AUDIT: "isp_dunning_audit",
  ISP_LEDGER_DETAILS: "isp_ledger_details",
  ISP_INVOICE_SEARCH: "isp_invoice_search",
  ISP_RECORD_PAYMENT: "isp_record_payment",
  ISP_APPROVE_PAYMENT: "isp_approve_payment",
  ISP_LEDGER_SEARCH: "isp_ledger_search",
  ISP_INVOICE_GENERATE: "isp_invoice_generate",
  ISP_INVOICE_CANCEL_AND_REGENERATE: "isp_invoice_cancel_and_regenerate",
  ISP_INVOICE_SEND_PAYLOAD: "isp_invoice_send_payload",
  ISP_INVOICE_CUSTOMER_LIST: "isp_invoice_customer_list",
  ISP_INVOICE_CUSTOMER_EXPORT: "isp_invoice_customer_export",
  ISP_DOWNLOAD_DOCUMENT: "isp_download_document",
  ISP_INVOICE_DELETE_CHARGE: "isp_invoice_delete_charge",
  ISP_GENERATE_INVOICE: "isp_generate_invoice",
  FEEDBACK: "feedback",
  FEEDBACK_CONFIGURATION_CREATE: "feedback_configuration_create",
  FEEDBACK_CONFIGURATION_EDIT: "feedback_configuration_edit",
  FEEDBACK_CONFIGURATION_DELETE: "feedback_configuration_delete",
  KNOWLEDGE_BASE: "knowledge_base",
  KNOWLEDGE_BASE_CREATE: "knowledge_base_create",
  KNOWLEDGE_BASE_DELETE: "knowledge_base_delete",
  KNOWLEDGE_BASE_EDIT: "knowledge_base_edit",
  SETTLE_AMOUNT: "settle_amount"
};

export const AUDITS = {
  AUDIT: "audit",
  AUDIT_LOG: "audit_log",
  REPORTED_PROBLEM: "reported_problem"
};
export const TASK_SYSTEMS = {
  TAT_TASK: "tat_task",
  TAT_TASK_CREATE: "tat_task_create",
  TAT_TASK_EDIT: "tat_task_edit",
  TAT_TASK_DELETE: "tat_task_delete",

  TASK_CATEGORY_DOMAIN: "task_category",
  TASK_CATEGORY_DOMAIN_CREATE: "task_category_create",
  TASK_CATEGORY_DOMAIN_EDIT: "task_category_edit",
  TASK_CATEGORY_DOMAIN_DELETE: "task_category_delete",

  TASK_SUB_CATEGORY_DOMAIN: "task_sub_category",
  TASK_SUB_CATEGORY_DOMAIN_CREATE: "task_sub_category_create",
  TASK_SUB_CATEGORY_DOMAIN_EDIT: "task_sub_category_edit",
  TASK_SUB_CATEGORY_DOMAIN_DELETE: "task_sub_category_delete",

  TASK_DOMAIN: "task",
  TASK_DOMAIN_CREATE: "task_create",
  TASK_DOMAIN_EDIT: "task_edit",
  TASK_DOMAIN_DELETE: "task_delete",
  TASK_DOMAIN_ETR: "task_etr",
  TASK_DOMAIN_REMARKS: "task_remarks",

  TASK_DOMAIN_CONERSATION: "task_conversation",
  TASK_DOMAIN_CONERSATION_EDIT: "task_conversation_edit",
  TASK_DOMAIN_CONERSATION_DELETE: "task_conversation_delete",

  TASK_DOMAIN_CHANGE_STATUS: "task_change_status",
  TASK_DOMAIN_BULK_REASSIGN: "task_bulk_reassign",
  TASK_DOMAIN_CHANGE_PRIORITY: "task_change_priority",
  TASK_DOMAIN_CHANGE_CATEGORY: "task_change_category",
  TASK_UPLOAD_DOC: "task_upload_document",
  TASK_ATTACHMENT: "task_attachment",
  TASK_ATTACHMENT_DOWNLOAD: "task_attachment_download",
  TASK_ETR_EXCEL_DOWNLOAD: "task_etr_excel_download",
  TASK_ASSIGN: "task_assign",
  TASK_LINK_TICKET: "task_link_ticket",
  ROOT_CAUSE_MASTER: "root_cause_master",
  ROOT_CAUSE_CREATE: "root_cause_create",
  ROOT_CAUSE_EDIT: "root_cause_edit",
  ROOT_CAUSE_DELETE: "root_cause_delete"
};
export const TECHNICIAN_DIARY_SYSTEMS = {
  TECHNNICIAN_DIARY: "technician_diary",
  ADD_EVENT: "add_event",
  VIEW_EVENT: "view_event",
  DELETE_EVENT: "delete_event",
  DATE_CLICK: "date_click",
  TASK_DIARY_CHANGE_STATUS: "task_diary_change_status",
  TASK_DIARY_UPLOAD_DOCUMENTS: "task_diary_upload_documents",
  TASK_DIARY_RESCHEDULE: "task_diary_reschedule",
  TASK_DIARY_REMARKS: "task_diary_remarks",
  TASK_DIARY_ATTACHMENT_DOWNLOAD: "task_diary_document_download",
  TASK_DIARY_ATTACHMENT_VIEW: "task_diary_view_documents",
  TASK_DIARY_LINK_TICKET: "task_diary_link_ticket"
};
