<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltmac"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }} {{ custData.lastname }} MAC Details
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="custStatus"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#custStatus"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="custStatuss">
        <div class="panel-body table-responsive">
          <div style="margin: 20px">
            <div class="row" style="margin-bottom: 20px">
              <button type="submit" class="btn btn-primary" id="submit" (click)="addMac()">
                <i class="fa fa-check-circle"></i>
                Add MAC
              </button>
            </div>
            <div class="table-responsive">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>MAC Address</th>
                        <th>Service</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let data of macListData; let index = index">
                        <td>
                          <div *ngIf="displaymode || currentEditRecord?.id !== data?.id">
                            {{ data.macAddress }}
                          </div>
                          <div *ngIf="!displaymode && currentEditRecord?.id === data?.id">
                            <input
                              type="text"
                              class="form-control"
                              placeholder="Enter Ip Address"
                              [(ngModel)]="editingRecord.macAddress"
                            />
                          </div>
                        </td>
                        <td>
                          <div *ngIf="displaymode || currentEditRecord?.id !== data?.id">
                            {{ data.service }}
                          </div>
                          <div *ngIf="!displaymode && currentEditRecord?.id === data?.id">
                            <input
                              type="text"
                              class="form-control"
                              placeholder="Enter Ip Address"
                              [(ngModel)]="editingRecord.service"
                              readonly
                            />
                          </div>
                        </td>
                        <td>
                          <div *ngIf="displaymode" style="display: flex; gap: 10px">
                            <a
                              type="button"
                              data-title="Edit"
                              data-toggle="tooltmac"
                              class="curson_pointer"
                              (click)="editMacById(data, index)"
                            >
                              <img src="assets/img/ioc01.jpg" />
                            </a>

                            <a
                              type="button"
                              data-title="Delete"
                              data-toggle="tooltmac"
                              class="curson_pointer"
                              (click)="deleteConfirm(data.id)"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </a>
                          </div>
                          <div
                            *ngIf="!displaymode && editingIndex === index"
                            style="display: flex; gap: 10px"
                          >
                            <a
                              type="button"
                              data-title="Edit"
                              data-toggle="tooltmac"
                              class="curson_pointer"
                              (click)="saveChanges()"
                            >
                              <img src="assets/img/assign.jpg" />
                            </a>

                            <a
                              type="button"
                              data-title="Delete"
                              data-toggle="tooltmac"
                              class="curson_pointer"
                              (click)="cancelChanges()"
                            >
                              <img src="assets/img/reject.jpg" />
                            </a>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <br />
                  <div class="pagination_Dropdown">
                    <pagination-controls
                      (pageChange)="pageChangedMasterNotificationList($event)"
                      directionLinks="true"
                      id="searchNotificationPageData"
                      maxSize="10"
                      nextLabel=""
                      previousLabel=""
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        (onChange)="TotalItemPerPageNotificationHistory($event)"
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="MAC details"
  [(visible)]="createMac"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <div class="row" [formGroup]="macManagementGroup">
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <input
          type="text"
          class="form-control"
          placeholder="Enter Mac Address"
          formControlName="macAddress"
          [ngClass]="{
            'is-invalid': macSubmitted && macManagementGroup.controls.macAddress.errors
          }"
        />
        <div
          class="errorWrap text-danger"
          *ngIf="macSubmitted && macManagementGroup.controls.macAddress.errors"
        >
          <div class="error text-danger">Mac Address is required.</div>
        </div>
        <div
          class="errorWrap text-danger"
          *ngIf="
            macManagementGroup.controls.macAddress.errors &&
            (macManagementGroup.controls.macAddress.touched ||
              macManagementGroup.controls.macAddress.dirty)
          "
        >
          <div class="error text-danger">Mac Address is invalid</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <p-dropdown
          [options]="dropdownOptions"
          placeholder="Select Service"
          optionLabel="label"
          optionValue="value"
          formControlName="custid"
          [ngClass]="{
            'is-invalid': macSubmitted && macManagementGroup.controls.custid.errors
          }"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <button
          style="object-fit: cover; padding: 5px 8px"
          class="btn btn-primary"
          (click)="onAddmacList()"
        >
          <i class="fa fa-plus-square" aria-hidden="true"></i>
          Add
        </button>
      </div>
    </div>
    <table class="table coa-table" style="margin-top: 10px">
      <thead>
        <tr>
          <th style="text-align: center; width: 10%">Mac Address</th>
          <th style="text-align: center; width: 10%; padding: 8px">Service</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of macMapppingListFromArray.controls; let index = index">
          <td style="text-align: center">
            <input
              class="form-control"
              type="text"
              readonly
              [formControl]="row.get('macAddress')"
            />
          </td>
          <td style="text-align: center">
            <input class="form-control" type="text" readonly [formControl]="row.get('service')" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button (click)="saveMac()" class="btn btn-primary" data-title="Shift Location" type="button">
        Save
      </button>
      <button type="button" class="btn btn-default" (click)="closeaddMac()">Close</button>
    </div>
  </div>
</p-dialog>
