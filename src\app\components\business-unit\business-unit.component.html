<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Business Unit Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchData"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchData" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="search()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="search()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button type="reset" class="btn btn-default" id="searchbtn" (click)="clearSearch()">
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Business Unit</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allData"
            aria-expanded="false"
            aria-controls="allData"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allData" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div>
            <div class="row">
              <div class="col-lg-12 col-md-12">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Code</th>
                      <th>Status</th>
                      <th *ngIf="deleteAccess || editAccess">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let businessUnit of businessUnitListData
                          | paginate
                            : {
                                id: 'ListData',
                                itemsPerPage: itemsPerPage,
                                currentPage: currentPageSlab,
                                totalItems: totalRecords
                              };
                        index as i
                      "
                    >
                      <td>
                        <span
                          class="HoverEffect"
                          data-backdrop="static"
                          data-keyboard="false"
                          title="BU Details"
                          (click)="IcCodeOpenModel(businessUnit.id)"
                          >{{ businessUnit.buname }}</span
                        >
                      </td>
                      <td>{{ businessUnit.bucode }}</td>
                      <td
                        *ngIf="businessUnit.status == 'ACTIVE' || businessUnit.status == 'Active'"
                      >
                        <span class="badge badge-success">Active</span>
                      </td>
                      <td
                        *ngIf="
                          businessUnit.status == 'INACTIVE' || businessUnit.status == 'Inactive'
                        "
                      >
                        <span class="badge badge-danger">Inactive</span>
                      </td>
                      <td class="btnAction" *ngIf="deleteAccess || editAccess">
                        <a
                          *ngIf="editAccess"
                          id="edit-button"
                          href="javascript:void(0)"
                          type="button"
                          (click)="edit(businessUnit.id)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </a>
                        <a
                          *ngIf="deleteAccess"
                          id="delete-button"
                          href="javascript:void(0)"
                          (click)="deleteConfirmon(businessUnit.id)"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="row">
                  <div class="col-md-12" style="display: flex">
                    <pagination-controls
                      id="ListData"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedList($event)"
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                        (onChange)="TotalItemPerPage($event)"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isEdit ? "Update" : "Create" }} Business Unit</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createData"
            aria-expanded="false"
            aria-controls="createData"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createData" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body table-responsive" *ngIf="!createAccess && !isEdit">
            Sorry you have not privilege to create operation!
          </div>
          <div class="panel-body" *ngIf="createAccess || (isEdit && editAccess)">
            <form [formGroup]="businessUnitFormGroup">
              <label>Business Unit Name*</label>
              <input
                id="buname"
                type="text"
                class="form-control"
                placeholder="Enter Business Unit Name"
                formControlName="buname"
                [ngClass]="{
                  'is-invalid': submitted && businessUnitFormGroup.controls.buname.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && businessUnitFormGroup.controls.buname.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && businessUnitFormGroup.controls.buname.errors.required"
                >
                  Business Unit Name is required.
                </div>
                <div
                  class="error text-danger"
                  *ngIf="
                    submitted && businessUnitFormGroup.controls.buname.errors?.cannotContainSpace
                  "
                >
                  <p class="error">White space are not allowed.</p>
                </div>
              </div>
              <br />
              <label>Business Unit Code*</label>
              <input
                id="bucode"
                type="text"
                class="form-control"
                placeholder="Enter Business Unit Code"
                formControlName="bucode"
                [ngClass]="{
                  'is-invalid': submitted && businessUnitFormGroup.controls.bucode.errors
                }"
              />
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && businessUnitFormGroup.controls.bucode.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && businessUnitFormGroup.controls.bucode.errors.required"
                >
                  Business Unit Code is required.
                </div>
              </div>
              <br />
              <label>Ic Code Name</label>
              <div>
                <p-multiSelect
                  [options]="IcListData"
                  formControlName="investmentCodeid"
                  optionValue="id"
                  optionLabel="icname"
                  filter="true"
                  filterBy="icname"
                  placeholder="Select IC Code"
                  resetFilterOnHide="true"
                  [ngClass]="{
                    'is-invalid':
                      submitted && businessUnitFormGroup.controls.investmentCodeid.errors
                  }"
                >
                </p-multiSelect>
              </div>
              <br />
              <label>Plan Binding Type*</label>
              <div>
                <p-dropdown
                  [options]="businessUnitTypeData"
                  formControlName="planBindingType"
                  optionLabel="text"
                  optionValue="text"
                  filter="true"
                  filterBy="text"
                  placeholder="Select Plan Binding Type"
                  [ngClass]="{
                    'is-invalid': submitted && businessUnitFormGroup.controls.planBindingType.errors
                  }"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="submitted && businessUnitFormGroup.controls.planBindingType.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="
                      submitted && businessUnitFormGroup.controls.planBindingType.errors.required
                    "
                  >
                    Plan Binding Type is required.
                  </div>
                </div>
              </div>

              <!-- <p-dropdown
                  [options]="businessUnitTypeData"
                  formControlName="planBindingType"
                  optionLabel="label"
                  optionValue="label"
                  filter="true"
                  filterBy="label"
                                
                  [disabled]="true"
                  [(ngModel)]="defaultPlanCreation.label"
                
                ></p-dropdown> -->

              <br />
              <label>Status*</label>
              <div>
                <p-dropdown
                  [options]="statusOptions"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Status"
                  formControlName="status"
                  [ngClass]="{
                    'is-invalid': submitted && businessUnitFormGroup.controls.status.errors
                  }"
                ></p-dropdown>
              </div>
              <div
                class="errorWrap text-danger"
                *ngIf="submitted && businessUnitFormGroup.controls.status.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="submitted && businessUnitFormGroup.controls.status.errors.required"
                >
                  Business Unit Status is required.
                </div>
              </div>
              <br />
              <div class="addUpdateBtn">
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="!isEdit"
                  id="submit"
                  (click)="addEdit('')"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Business Unit
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isEdit"
                  id="submit"
                  (click)="addEdit(viewListData.id)"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Business Unit
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="{{ buList.buname }} Details"
  [(visible)]="buNameDetailsModal"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModal()"
>
  <!-- <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">&times;</button>
    <h3 class="panel-title">{{ buList.buname }} Details</h3>
  </div> -->
  <div class="modal-body">
    <div class="panel-body table-responsive" id="networkDeviceTabel">
      <table class="table">
        <tbody>
          <tr>
            <td><label class="networkLabel">Name : </label></td>
            <td>{{ buList.buname }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">Code: </label>
            </td>
            <td>{{ buList.bucode }}</td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel">Ic Code Name : </label>
            </td>
            <td>{{ buList.icnames }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <!-- <div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
  </div> -->
</p-dialog>
