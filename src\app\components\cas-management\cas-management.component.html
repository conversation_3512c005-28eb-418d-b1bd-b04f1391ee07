<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCas"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchDataCas" class="panel-collapse collapse in">
        <div id="" class="panel-body" *ngIf="listView">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchCasName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchCas()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchCas()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchCas()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="createCas()" class="curson_pointer" *ngIf="createAccess">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create {{ title }}</h5>
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="lisCas()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search {{ title }}</h5>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12" *ngIf="listView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCas"
            aria-expanded="false"
            aria-controls="allDataCas"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataCas" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Status</th>
                    <th *ngIf="deleteAccess || editAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let Cas of casListData
                        | paginate
                          : {
                              id: 'casListDataID',
                              itemsPerPage: casitemsPerPage,
                              currentPage: currentPageCasSlab,
                              totalItems: castotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ Cas.casname }}</td>
                    <td *ngIf="Cas.status == 'ACTIVE' || Cas.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="Cas.status == 'INACTIVE' || Cas.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td class="btnAction" *ngIf="deleteAccess || editAccess">
                      <button
                        *ngIf="editAccess"
                        id="edit-button"
                        href="javascript:void(0)"
                        title="Edit"
                        type="button"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        (click)="editCas(Cas.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </button>
                      <button
                        *ngIf="deleteAccess"
                        id="delete-button"
                        href="javascript:void(0)"
                        type="button"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        (click)="deleteConfirmonCas(Cas)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    id="casListDataID"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedCasList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12" *ngIf="createView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isCasEdit ? "Update" : "Create" }} {{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCountry"
            aria-expanded="false"
            aria-controls="createDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCountry" class="panel-collapse collapse in">
        <div class="panel-body" *ngIf="createAccess || (isCasEdit && editAccess)">
          <form [formGroup]="casFormGroup">
            <fieldset style="margin-top: 0px">
              <legend>Basic Paramater</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-3 col-12">
                    <label>{{ title }} Name*</label>
                    <input
                      id="name"
                      type="text"
                      class="form-control"
                      placeholder="Enter {{ title }} Name"
                      formControlName="casname"
                      [ngClass]="{
                        'is-invalid': submitted && casFormGroup.controls.casname.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && casFormGroup.controls.casname.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && casFormGroup.controls.casname.errors.required"
                      >
                        {{ title }} Name is required.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-3 col-12">
                    <label>Endpoint Url</label>
                    <input
                      id="name"
                      type="text"
                      class="form-control"
                      placeholder="Enter Url"
                      formControlName="endpoint"
                      [ngClass]="{
                        'is-invalid': submitted && casFormGroup.controls.endpoint.errors
                      }"
                    />
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-3 col-12">
                    <label>Status*</label>
                    <div>
                      <p-dropdown
                        [options]="statusOptions"
                        optionValue="label"
                        optionLabel="label"
                        filter="true"
                        filterBy="label"
                        placeholder="Select a Status"
                        formControlName="status"
                        [ngClass]="{
                          'is-invalid': submitted && casFormGroup.controls.status.errors
                        }"
                      ></p-dropdown>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && casFormGroup.controls.status.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && casFormGroup.controls.status.errors.required"
                      >
                        {{ title }} Status is required.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>

            <fieldset>
              <legend>Parameter Mapping List</legend>
              <div class="boxWhite">
                <div class="row" [formGroup]="casParameterMappingsfromgroup">
                  <div class="col-lg-3 col-md-3 col-sm-3 col-12">
                    <div>
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Enter Parameter Name"
                        name="paramName"
                        id="paramName"
                        formControlName="paramName"
                        [ngClass]="{
                          'is-invalid':
                            casParamaterSubmitted &&
                            casParameterMappingsfromgroup.controls.paramName.errors
                        }"
                      />
                    </div>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        casParamaterSubmitted &&
                        casParameterMappingsfromgroup.controls.paramName.errors
                      "
                    >
                      <div class="error text-danger">Service required.</div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-3 col-12">
                    <input
                      class="form-control"
                      type="text"
                      placeholder="Enter Parameter Value"
                      name="paramValue"
                      id="paramValue"
                      formControlName="paramValue"
                      [ngClass]="{
                        'is-invalid':
                          casParamaterSubmitted &&
                          casParameterMappingsfromgroup.controls.paramValue.errors
                      }"
                    />

                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        casParamaterSubmitted &&
                        casParameterMappingsfromgroup.controls.paramValue.errors
                      "
                    >
                      <div class="error text-danger">Parameter Value is required.</div>
                    </div>
                  </div>
                  <div class="col-lg-2 col-md-2 col-sm-2 col-12">
                    <button
                      style="object-fit: cover; padding: 5px 8px"
                      class="btn btn-primary"
                      (click)="onAddParameterMappingList()"
                    >
                      <i class="fa fa-plus-square" aria-hidden="true"></i>
                      Add
                    </button>
                  </div>
                </div>
                <table class="table coa-table" style="margin-top: 10px">
                  <thead>
                    <tr>
                      <th style="text-align: center">Parameter Name</th>
                      <th style="text-align: center">Parameter Value</th>
                      <th style="text-align: center">Delete</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let row of casParameterMappingsFromArray.controls
                          | paginate
                            : {
                                id: 'parameterLISTID',
                                itemsPerPage: parameterItemPerPage,
                                currentPage: currentPageparameter,
                                totalItems: parametertotalRecords
                              };
                        let index = index
                      "
                    >
                      <td>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="Enter Parameter Name"
                          name="paramName"
                          id="paramName"
                          [formControl]="row.get('paramName')"
                          disabled
                        />
                      </td>
                      <td>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="Enter Parameter Value"
                          name="paramValue"
                          id="paramValue"
                          [formControl]="row.get('paramValue')"
                          disabled
                        />
                      </td>
                      <td style="text-align: center">
                        <a
                          *ngIf="casParameterMappingsFromArray.controls.length !== 1"
                          id="deleteAtt"
                          href="javascript:void(0)"
                          (click)="deleteConfirmonParameterMappingList(index)"
                        >
                          <img src="assets/img/ioc02.jpg" />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="row">
                  <div class="col-md-12">
                    <pagination-controls
                      id="parameterLISTID"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="pageChangedParameterMappingList($event)"
                    ></pagination-controls>
                  </div>
                </div>
                <br />
              </div>
            </fieldset>

            <fieldset style="margin-top: 1.5rem" *ngIf="isCasEdit">
              <legend>{{ title }} Mapping List</legend>
              <div class="boxWhite">
                <div class="panel-heading">
                  <div class="right" style="right: 0px !important">
                    <button type="button" (click)="referncecasPackage(this.viewCasListData.id)">
                      <i class="fa fa-refresh"></i>
                    </button>
                  </div>
                </div>

                <table class="table coa-table" style="margin-top: 25px">
                  <thead>
                    <tr>
                      <th style="padding-left: 2.5rem">Name</th>
                      <th>Package Id</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let mapping of this.viewCasListData.casPackageMappings
                          | paginate
                            : {
                                id: 'PackageLISTID',
                                itemsPerPage: PackageItemPerPage,
                                currentPage: currentPagePackage,
                                totalItems: PackagetotalRecords
                              };
                        let index = index
                      "
                    >
                      <td style="padding-left: 2.5rem">{{ mapping.packageName }}</td>
                      <td>{{ mapping.packageId }}</td>
                    </tr>
                  </tbody>
                </table>
                <div class="row">
                  <div class="col-md-12" style="display: flex">
                    <pagination-controls
                      id="PackageLISTID"
                      maxSize="10"
                      directionLinks="true"
                      previousLabel=""
                      nextLabel=""
                      (pageChange)="changepagePackage($event)"
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                        (onChange)="totalItemPerPageMapping($event)"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>

            <div class="addUpdateBtn" style="margin-top: 1.5rem">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditCas('')"
                *ngIf="!isCasEdit"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ title }}
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                *ngIf="isCasEdit"
                id="submit"
                (click)="addEditCas(viewCasListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update {{ title }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
