<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ cityTitle }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCity"
            aria-expanded="false"
            aria-controls="searchDataCity"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchDataCity" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                type="text"
                [(ngModel)]="searchCityName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchCity()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchCity()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchCity()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ cityTitle }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCity"
            aria-expanded="false"
            aria-controls="allDataCity"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="allDataCity" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>{{ stateTitle }}</th>
                    <th>{{ countryTitle }}</th>
                    <th>Status</th>
                    <th *ngIf="deleteAccess || editAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let city of cityListData
                        | paginate
                          : {
                              id: 'cityListData',
                              itemsPerPage: cityListdataitemsPerPage,
                              currentPage: currentPageCityListdata,
                              totalItems: cityListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ city.name }}</td>
                    <td>{{ city.stateName }}</td>
                    <td>{{ city.countryName }}</td>
                    <td *ngIf="city.status == 'ACTIVE' || city.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="city.status == 'INACTIVE' || city.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td class="btnAction" *ngIf="deleteAccess || editAccess">
                      <a
                        *ngIf="editAccess"
                        id="edit-button"
                        class="editBtn"
                        type="button"
                        href="javascript:void(0)"
                        (click)="editCity(city.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="deleteAccess"
                        id="delete-button"
                        class="deleteBtn"
                        href="javascript:void(0)"
                        (click)="deleteConfirmonCity(city.id)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="pagination_Dropdown">
                <pagination-controls
                  id="cityListData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedCityList((currentPageCityListdata = $event))"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isCityEdit ? "Update" : "Create" }} {{ cityTitle }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCity"
            aria-expanded="false"
            aria-controls="createDataCity"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCity" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isCityEdit">
          Sorry you have not privilege to create operation!
        </div>
        <div class="panel-body" *ngIf="createAccess || (isCityEdit && editAccess)">
          <form [formGroup]="cityFormGroup">
            <label>{{ cityTitle }} Name*</label>
            <input
              type="text"
              class="form-control"
              placeholder="Enter {{ cityTitle }} Name"
              formControlName="name"
              [ngClass]="{
                'is-invalid': submitted && cityFormGroup.controls.name.errors
              }"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && cityFormGroup.controls.name.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && cityFormGroup.controls.name.errors.required"
              >
                {{ cityTitle }} Name is required.
              </div>
              <div
                class="error text-danger"
                *ngIf="submitted && cityFormGroup.controls.name.errors?.cannotContainSpace"
              >
                <p class="error">White space are not allowed.</p>
              </div>
            </div>
            <br />
            <div>
              <label>{{ stateTitle }}*</label>
              <p-dropdown
                [options]="stateListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a state"
                formControlName="stateName"
                [ngClass]="{
                  'is-invalid': submitted && cityFormGroup.controls.stateName.errors
                }"
                (onChange)="selectStateChange($event)"
              >
                <ng-template let-data pTemplate="item">
                  <div class="item-drop1">
                    <span class="item-value1"> {{ data.name }} ( {{ data.countryName }} ) </span>
                  </div>
                </ng-template>
              </p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && cityFormGroup.controls.stateName.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && cityFormGroup.controls.stateName.errors.required"
              >
                {{ stateTitle }} is required.
              </div>
            </div>
            <br />
            <label>{{ countryTitle }}*</label>
            <div>
              <p-dropdown
                [options]="countryListData"
                optionValue="id"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a {{ countryTitle }}"
                formControlName="countryId"
                [disabled]="countryselectshow"
                [ngClass]="{
                  'is-invalid': submitted && cityFormGroup.controls.countryId.errors
                }"
              ></p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && cityFormGroup.controls.countryId.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && cityFormGroup.controls.countryId.errors.required"
              >
                {{ countryTitle }} is required.
              </div>
            </div>
            <br />
            <label>Status*</label>
            <div>
              <p-dropdown
                [options]="statusOptions"
                optionValue="label"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                formControlName="status"
                [ngClass]="{
                  'is-invalid': submitted && cityFormGroup.controls.status.errors
                }"
              ></p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && cityFormGroup.controls.status.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && cityFormGroup.controls.status.errors.required"
              >
                {{ cityTitle }} Status is required.
              </div>
            </div>
            <br />

            <div class="addUpdateBtn">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                *ngIf="!isCityEdit"
                (click)="addEditCity('')"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ cityTitle }}
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                *ngIf="isCityEdit"
                (click)="addEditCity(viewCityListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update {{ cityTitle }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
