<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }} {{ custData.lastname }} IP Details
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="custStatus"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#custStatus"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="custStatuss">
        <div class="panel-body table-responsive">
          <div style="margin: 20px">
            <div class="row" style="margin-bottom: 20px">
              <button type="submit" class="btn btn-primary" id="submit" (click)="addIp()">
                <i class="fa fa-check-circle"></i>
                Add IP
              </button>
            </div>
            <div class="table-responsive">
              <div class="row">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Ip Address</th>
                        <th>Ip Type</th>
                        <th>Service</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let data of ipListData; let index = index">
                        <td>
                          <div *ngIf="displaymode || currentEditRecord?.id !== data?.id">
                            {{ data.ipAddress }}
                          </div>
                          <div *ngIf="!displaymode && currentEditRecord?.id === data?.id">
                            <input
                              type="text"
                              class="form-control"
                              placeholder="Enter Ip Address"
                              [(ngModel)]="data.ipAddress"
                            />
                          </div>
                        </td>
                        <td>
                          <div *ngIf="displaymode || currentEditRecord?.id !== data?.id">
                            {{ data.ipType }}
                          </div>
                          <div *ngIf="!displaymode && currentEditRecord?.id === data?.id">
                            <p-dropdown
                              [options]="[
                                { label: 'Ipv4', value: 'Ipv4' },
                                { label: 'Ipv6', value: 'Ipv6' }
                              ]"
                              placeholder="Select a type"
                              [(ngModel)]="data.ipType"
                            ></p-dropdown>
                          </div>
                        </td>
                        <td>
                          <div *ngIf="displaymode || currentEditRecord?.id !== data?.id">
                            {{ data.service }}
                          </div>
                          <div *ngIf="!displaymode && currentEditRecord?.id === data?.id">
                            <input
                              type="text"
                              class="form-control"
                              placeholder="Enter Ip Address"
                              [(ngModel)]="data.service"
                              readonly
                            />
                          </div>
                        </td>
                        <td>
                          <div *ngIf="displaymode" style="display: flex; gap: 10px">
                            <a
                              type="button"
                              data-title="Edit"
                              data-toggle="tooltip"
                              class="curson_pointer"
                              (click)="editIpById(data, index)"
                            >
                              <img src="assets/img/ioc01.jpg" />
                            </a>

                            <a
                              type="button"
                              data-title="Delete"
                              data-toggle="tooltip"
                              class="curson_pointer"
                              (click)="deleteConfirm(data.id)"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </a>
                          </div>
                          <div
                            *ngIf="!displaymode && editingIndex === index"
                            style="display: flex; gap: 10px"
                          >
                            <a
                              type="button"
                              data-title="Edit"
                              data-toggle="tooltip"
                              class="curson_pointer"
                              (click)="saveChanges()"
                            >
                              <img src="assets/img/assign.jpg" />
                            </a>

                            <a
                              type="button"
                              data-title="Delete"
                              data-toggle="tooltip"
                              class="curson_pointer"
                              (click)="cancelChanges()"
                            >
                              <img src="assets/img/reject.jpg" />
                            </a>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <br />
                  <div class="pagination_Dropdown">
                    <pagination-controls
                      (pageChange)="pageChangedMasterNotificationList($event)"
                      directionLinks="true"
                      id="searchNotificationPageData"
                      maxSize="10"
                      nextLabel=""
                      previousLabel=""
                    ></pagination-controls>
                    <div id="itemPerPageDropdown">
                      <p-dropdown
                        (onChange)="TotalItemPerPageNotificationHistory($event)"
                        [options]="pageLimitOptions"
                        optionLabel="value"
                        optionValue="value"
                      ></p-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="Ip details"
  [(visible)]="createIp"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeaddIp()"
>
  <div class="modal-body">
    <div class="row" [formGroup]="ipManagementGroup">
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <p-dropdown
          [options]="[
            { label: 'Ipv4', value: 'Ipv4' },
            { label: 'Ipv6', value: 'Ipv6' }
          ]"
          #ddlIpType
          placeholder="Select a type"
          formControlName="ipType"
          (onChange)="ipTypeChange($event, ddlIpType)"
          [ngClass]="{
            'is-invalid': ipSubmitted && ipManagementGroup.controls.ipType.errors
          }"
        ></p-dropdown>
        <div
          class="errorWrap text-danger"
          *ngIf="ipSubmitted && ipManagementGroup.controls.ipType.errors"
        >
          <div class="error text-danger">Ip Type is required.</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <input
          type="text"
          class="form-control"
          placeholder="Enter Ip Address"
          formControlName="ipAddress"
          [ngClass]="{
            'is-invalid': ipSubmitted && ipManagementGroup.controls.ipAddress.errors
          }"
        />
        <div
          class="errorWrap text-danger"
          *ngIf="ipSubmitted && ipManagementGroup.controls.ipAddress.errors"
        >
          <div
            class="error text-danger"
            *ngIf="ipSubmitted && ipManagementGroup.controls.ipAddress.errors.required"
          >
            Ip Address is required.
          </div>
        </div>
        <div
          class="errorWrap text-danger"
          *ngIf="
            ipManagementGroup.controls.ipAddress.errors &&
            (ipManagementGroup.controls.ipAddress.touched ||
              ipManagementGroup.controls.ipAddress.dirty)
          "
        >
          <div class="error text-danger">Ip Address is invalid</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <p-dropdown
          [options]="dropdownOptions"
          placeholder="Select Service"
          optionLabel="label"
          optionValue="value"
          formControlName="custid"
          [ngClass]="{
            'is-invalid': ipSubmitted && ipManagementGroup.controls.custid.errors
          }"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <button
          style="object-fit: cover; padding: 5px 8px"
          class="btn btn-primary"
          (click)="onAddIPList()"
        >
          <i class="fa fa-plus-square" aria-hidden="true"></i>
          Add
        </button>
      </div>
    </div>
    <table class="table coa-table" style="margin-top: 10px">
      <thead>
        <tr>
          <th style="text-align: center; width: 10%">Ip Address</th>
          <th style="text-align: center; width: 10%">Ip Type</th>
          <th style="text-align: center; width: 10%; padding: 8px">Service</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of ipMapppingListFromArray.controls; let index = index">
          <td style="text-align: center">
            <input class="form-control" type="text" readonly [formControl]="row.get('ipAddress')" />
          </td>
          <td style="text-align: center">
            <input class="form-control" type="text" readonly [formControl]="row.get('ipType')" />
          </td>
          <td style="text-align: center">
            <input class="form-control" type="text" readonly [formControl]="row.get('service')" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveIp()"
        class="btn btn-primary"
        data-title="Shift Location"
        type="button"
        [disabled]="!ipMapppingListFromArray.length > 0"
      >
        Save
      </button>
      <button type="button" class="btn btn-default" (click)="closeaddIp()">Close</button>
    </div>
  </div>
</p-dialog>
