import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { InvoiceMasterComponent } from "./invoice-master.component";
import { DialogModule } from "primeng/dialog";
const routes = [{ path: "", component: InvoiceMasterComponent }];

@NgModule({
  declarations: [InvoiceMasterComponent],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule, DialogModule],
})
export class InvoiceMasterModule {}
