<p-dialog header="Select {{ deviceType }} Device" [(visible)]="isparentChildDeviceModelOpen" [style]="{ width: '70%' }"
  [modal]="true" [responsive]="true" [draggable]="false" [closable]="true" [baseZIndex]="1000000"
  (onHide)="modalCloseStaff()">
  <div class="modal-body">
    <h5>Search {{ deviceType }} Device</h5>
    <div class="row">
      <div class="col-md-3">
        <p-dropdown [options]="networkDeviceData" optionValue="text" optionLabel="text" filter="true" filterBy="text"
          placeholder="Select a Device Type" [(ngModel)]="searchDeviceType"></p-dropdown>
      </div>
      <div class="col-md-3" *ngIf="searchDeviceType">
        <input type="text" class="form-control" placeholder="Search by Display Name" [(ngModel)]="searchDeatil"
          (keyup.enter)="searchnetworkDevice()">
      </div>
      <div class="col-lg-6 col-md-6 col-sm-12">
        <button type="button" class="btn btn-primary" id="searchbtn" [disabled]="!searchDeatil"
          (click)="searchnetworkDevice()">
          <i class="fa fa-search"></i>
          Search
        </button>
        <button type="reset" class="btn btn-default" id="searchbtn" (click)="clearSearchnetworkDevice()">
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Device</h5>
    <p-table #dt [(selection)]="selectedDevice" [value]="networkDeviceListData" responsiveLayout="scroll" [rows]="5"
      [rowsPerPageOptions]="[5, 10, 25, 50]" [paginator]="false" [(first)]="newFirst">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>Device Type</th>
          <th>Display Name</th>
          <!-- <th>User Name</th>
          <th>Partner Name</th> -->
        </tr>
      </ng-template>
      <ng-template let-networkData let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [disabled]="networkData.id == selectedDeviceId"
              [ngClass]="{ disabled: networkData.id == selectedDeviceId }" [value]="networkData"></p-tableRadioButton>
          </td>
          <td>
            {{ networkData?.name }}
          </td>
          <td>
            {{ networkData?.devicetype || "-"}}
          </td>
          <td>
            {{ networkData?.displayname }}
          </td>
          <!-- <td>{{ networkData.devicetype }}</td>
          <td></td> -->
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator (onPageChange)="paginateStaff($event)" [first]="newFirst"
          [rows]="networkDeviceListdataitemsPerPage" [totalRecords]="networkDeviceListdatatotalRecords" [rows]="5"
          [rowsPerPageOptions]="[5, 10, 25, 50]"></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button (click)="saveSelstaff()" class="btn btn-primary" style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedDevice.length == 0">
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseStaff()" class="btn btn-danger btn-sm" type="button">Close</button>
    </div>
  </div>
</p-dialog>