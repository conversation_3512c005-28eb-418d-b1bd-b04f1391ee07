<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Generated Trial Bill Run</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#trialbill"
            aria-expanded="false"
            aria-controls="trialbill"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="trialbill" class="panel-collapse collapse in">
        <div class="panel-body">
          <form [formGroup]="searchGenerateTrialBillRunFormGroup">
            <div class="row">
              <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                <div class="form-group">
                  <input
                    type="date"
                    class="form-control"
                    placeholder="Enter Date"
                    formControlName="trialBillGenerateDate"
                    [ngClass]="{
                      'is-invalid':
                        submitted &&
                        searchGenerateTrialBillRunFormGroup.controls.trialBillGenerateDate.errors
                    }"
                  />
                  <div
                    class="errorWrap text-danger"
                    *ngIf="
                      submitted &&
                      searchGenerateTrialBillRunFormGroup.controls.trialBillGenerateDate.errors
                    "
                  >
                    <div
                      class="error text-danger"
                      *ngIf="
                        submitted &&
                        searchGenerateTrialBillRunFormGroup.controls.trialBillGenerateDate.errors
                          .required
                      "
                    >
                      Trial Bill Date is required.
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                <button type="submit" class="btn btn-primary" (click)="searchTrialBillRun()">
                  <i class="fa fa-search"></i>
                  Generate Trial Bill Run
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  id="searchbtn"
                  (click)="clearTrialBillRun()"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <div class="row">
    <div class="col-md-12" *ngIf="isGenerateBillSearch">
        <div class="panel">
            <div class="panel-heading">
                <h3 class="panel-title">Billing Detail</h3>
                <div class="right">
                    <button type="button" class="btn-toggle-collapse 
            data-toggle="collapse"
            data-target="#searchPlanB"
            aria-expanded="false"
            aria-controls="searchPlanB"">
                        <i class="fa fa-minus-circle"></i>
                    </button>
                </div>
            </div>
            <div class="panel-body table-responsive">
                <div class="row">
                    <div class="col-lg-12 col-md-12">
                        <span *ngIf="billRunData.status === 'In Progress' ">
                            Billing is <b>In Progress</b> your Trial bill run id is <b>{{billRunData}}</b>.
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->
