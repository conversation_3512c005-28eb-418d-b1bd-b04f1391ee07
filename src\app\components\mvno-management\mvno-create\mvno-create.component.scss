// :host ::ng-deep {

// }

.row {
  display: flex;
  flex-wrap: wrap;
}

.header {
  font-size: 20px;
  font-weight: bolder;
}

:host ::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border: 1px solid;
  padding: 10px;
  box-shadow: 2px 2px #f7b206 !important;
}

.modal-body-location {
  position: relative;
  height: 40vh;
}

:host ::ng-deep .p-dialog .p-dialog-header {
  background: #f7b206 !important;
}

.checkbox-align {
  padding-top: 8%;
  padding-right: 10%;
}

.borderuploadphoto {
  width: 150px;
  height: 150px;
  margin: 18px auto 10px;
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  border: 1px solid #cecdcd !important;
  background-position: center !important;
  background-color: whitesmoke !important;
}

.upload_btn-profileimage {
  border: none;
  background-color: #000000bf;
  font-size: 17px;
  color: white;
  border-radius: 15px;
}

.upload_btn-profileimage:hover {
  color: #b2d0f0;
}
