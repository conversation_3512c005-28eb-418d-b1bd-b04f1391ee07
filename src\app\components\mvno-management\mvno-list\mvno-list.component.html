<div class="row">
  <div class="col-md-12">
    <div class="panel mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">{{ mvnoTitle }} Management</h3>
        <div class="right">
          <button
            aria-controls="searchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchMvno" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchMvnoName"
                class="form-control"
                placeholder="Enter MVNO Name"
                (keydown.enter)="searchMvno()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchMvno()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button type="reset" class="btn btn-default" id="searchbtn" (click)="clearMvno()">
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row" *ngIf="generateInvoiceAccess">
  <div class="col-md-12">
    <div class="panel mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">{{ mvnoTitle }} Invoice Generate</h3>
        <div class="right">
          <button
            aria-controls="searchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchMvno" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row" style="margin-left: 15px">
            <!-- Move date pickers next to buttons -->
            <form class="form-auth-small" [formGroup]="generateIspInvoiceForm">
              <div class="row">
                <!-- <div class="col-md-4" style="padding-left: 0%; width: 40%"> -->
                <p-calendar
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [showTime]="false"
                  [hideOnDateTimeSelect]="true"
                  placeholder="Enter Bill Date"
                  [maxDate]="maxDisbleDate"
                  formControlName="startDate"
                  [style]="{ width: '80%' }"
                ></p-calendar>
                <!-- </div> -->

                <!-- <div class="col-md-2" style="padding-left: 0%; width: 40%">
                  <p-calendar
                    [showIcon]="true"
                    [showButtonBar]="true"
                    [showTime]="false"
                    [hideOnDateTimeSelect]="true"
                    placeholder="Enter To Date"
                    formControlName="endDate"
                    [style]="{ width: '100%' }"
                  ></p-calendar>
                </div> -->
              </div>
            </form>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="generateInvoice()"
                [disabled]="!generateIspInvoiceForm.valid"
              >
                Generate
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ mvnoTitle }} List</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#listmvno"
            aria-expanded="false"
            aria-controls="listmvno"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="listmvno" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Username</th>
                    <th>Status</th>
                    <th *ngIf="editAccess || uploadAccess || dataTransferAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let mvno of mvnoListData
                        | paginate
                          : {
                              id: 'mvnoListData',
                              itemsPerPage: mvnoitemsPerPage,
                              currentPage: currentPageMvno,
                              totalItems: mvnototalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <!-- {{ mvno.name }} -->
                      <a
                        [routerLink]="['/home/<USER>/details/', mvno.id]"
                        style="color: #f7b206"
                        href="javascript:void(0)"
                      >
                        {{ mvno.name }}
                      </a>
                    </td>
                    <td>{{ mvno.email }}</td>
                    <td>{{ mvno.username }}</td>
                    <td>
                      <span *ngIf="mvno.status == 'Active'" class="badge badge-success">
                        Active
                      </span>
                      <span *ngIf="mvno.status == 'Inactive'" class="badge badge-danger">
                        Inactive
                      </span>
                    </td>
                    <td class="btnAction" *ngIf="editAccess || uploadAccess || dataTransferAccess">
                      <a
                        *ngIf="editAccess"
                        id="edit-button"
                        href="javascript:void(0)"
                        type="button"
                        [routerLink]="['/home/<USER>/edit/', mvno.id]"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <!-- <a
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmonMvno(mvno.id)"
                        disabled
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a> -->
                      <a
                        *ngIf="uploadAccess"
                        [routerLink]="['/home/<USER>/', mvno.id]"
                        data-placement="mb-15"
                        data-toggle="tooltip"
                        id="editbutton"
                        title="Upload Documents"
                        type="button"
                      >
                        <img height="32" src="assets/img/up.jpg" width="32" />
                      </a>
                      <a
                        *ngIf="dataTransferAccess"
                        id="button"
                        title="mvno"
                        type="button"
                        (click)="sendMvnoId(mvno)"
                        data-placement="mb-15"
                        data-toggle="tooltip"
                      >
                        <img height="32" src="assets/img/icons-02.png" width="32" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="mvnoListData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedMvnoList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="{{ mvnoTitle }} Details"
  [style]="{ width: '50%' }"
  [(visible)]="migrationDialog"
  [responsive]="false"
  [draggable]="false"
  [modal]="true"
  [closable]="true"
  (onHide)="closeDialog()"
>
  <div class="modal-body">
    <div class="row">
      <div class="col-lg-6 col-md-6">
        <label for="oldMVNO">Old {{ mvnoTitle }}</label>
        <input id="oldMVNO" type="text" class="form-control" [(ngModel)]="oldMvnoName" readonly />
      </div>
      <div class="col-lg-6 col-md-6">
        <label for="newMVNO">New {{ mvnoTitle }}</label>
        <p-dropdown
          [options]="mvnoOptions"
          [(ngModel)]="newMvnoId"
          optionLabel="name"
          optionValue="id"
          filter="true"
          filterBy="name"
          placeholder="Select an {{ mvnoTitle }}"
          appendTo="body"
        >
        </p-dropdown>
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div>
      <button (click)="transferISP()" class="btn btn-primary">Send</button>
      <button (click)="closeDialog()" class="btn btn-danger" type="button">Close</button>
      <button (click)="downloadFile()" class="btn btn-danger" type="button" *ngIf="isDownloadView">
        Download
      </button>
    </div>
  </ng-template>
</p-dialog>
