<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Search Credit Note</h3>
        <div class="right">
          <!-- <button type="button" class="btn-toggle-collapse">
            <i class="fa fa-minus-circle"></i>
          </button> -->
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <!-- <h3 class="panel-title">Search Payment</h3> -->
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchCredit"
            aria-expanded="false"
            aria-controls="searchCredit"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchCredit" class="panel-collapse collapse in">
        <div class="panel-body">
          <!-- <form [formGroup]="searchPaymentFormGroup"> -->
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Customer</label>
              <div>
                <!-- <p-dropdown
                  [options]="customerData"
                  optionValue="id"
                  optionLabel="name"
                  [filter]="true"
                  filterBy="name"
                  placeholder="Select a Customer"
                  [(ngModel)]="customerid"
                  (keydown.enter)="searchPayment('')"
                ></p-dropdown> -->
                <p-dropdown
                  [disabled]="true"
                  [options]="parentCustList"
                  [showClear]="true"
                  [filter]="true"
                  filterBy="name"
                  [(ngModel)]="customerid"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select a Customer"
                  styleClass="disableDropdown"
                >
                  <ng-template let-data pTemplate="item">
                    <div class="item-drop1">
                      <span class="item-value1"> {{ data.title }} {{ data.name }} </span>
                    </div>
                  </ng-template>
                </p-dropdown>
                <button
                  type="button"
                  (click)="modalOpenParentCustomer()"
                  class="btn btn-primary"
                  style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
                >
                  <i class="fa fa-plus-square"></i>
                </button>
              </div>
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Credit From Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Credit From Date"
                [(ngModel)]="payfromdate"
                (keydown.enter)="searchPayment('')"
              />
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Credit To Date</label>
              <input
                type="date"
                class="form-control"
                placeholder="Enter Credit To Date"
                [(ngModel)]="paytodate"
                (keydown.enter)="searchPayment('')"
              />
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Credit Status</label>
              <div>
                <p-dropdown
                  [options]="payStatus"
                  optionValue="value"
                  optionLabel="label"
                  [filter]="true"
                  filterBy="label"
                  placeholder="Select a Credit Status"
                  [(ngModel)]="paystatus"
                  (keydown.enter)="searchPayment('')"
                ></p-dropdown>
              </div>
              <br />
            </div>
          </div>
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Reference Number</label>
              <div>
                <input
                  filterBy="label"
                  class="form-control"
                  placeholder="Select a Reference Number"
                  [(ngModel)]="referenceno"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Invoice Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Invoice Number"
                  [(ngModel)]="invoiceNumber"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Mobile Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Mobile Number"
                  [(ngModel)]="mobileNumber"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
              <label>Document Number</label>
              <div>
                <input
                  class="form-control"
                  filterBy="label"
                  placeholder="Select a Document Number"
                  [(ngModel)]="creditDocumentNumber"
                  (keydown.enter)="searchPayment('')"
                />
              </div>
              <br />
            </div>
          </div>
          <div class="addUpdateBtn">
            <button
              type="submit"
              class="btn btn-primary"
              id="searchbtn"
              (click)="searchPayment('')"
            >
              <i class="fa fa-search"></i>
              Search Credit Note
            </button>
            <button type="submit" class="btn btn-default" id="searchbtn" (click)="clearPayment()">
              <i class="fa fa-refresh"></i>
              Clear
            </button>
            <!-- <button type="submit" class="btn btn-default" id="searchbtn" (click)="openModal('myModal',63)">
                        <i class="fa fa-refresh"></i> Open modal
                    </button> -->
          </div>
          <!-- </form> -->
        </div>
      </div>
    </div>
  </div>

  <app-customer-details
    *ngIf="dialogId"
    (closeSelectStaff)="closeSelectStaff()"
    [custId]="custId"
  ></app-customer-details>

  <app-payment-amount-model
    *ngIf="displayInvoiceDetails"
    dialogId="PaymentDetailModal"
    [paymentId]="paymentId"
    (closeParentCustt)="closeParentCust()"
  ></app-payment-amount-model>

  <app-workflow-audit-details-modal
    *ngIf="ifModelIsShow"
    [auditcustid]="auditcustid"
    dialogId="custauditWorkflowModal"
    (closeParentCustt)="closeParentCustt()"
  ></app-workflow-audit-details-modal>

  <div class="col-md-12" *ngIf="isPaymentSearch">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Credit Note List</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#creditNoteList"
            aria-expanded="false"
            aria-controls="creditNoteList"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="creditNoteList" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length !== 0">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Customer</th>
                    <th style="width: 7%">Amount</th>
                    <th>Document No.</th>
                    <th>Reference No.</th>
                    <th>Credit Date</th>
                    <th>Credit By</th>
                    <th>Remark</th>
                    <th style="width: 7%">Status</th>
                    <th *ngIf="reassignAccess || reprintAccess || downloadAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let payment of searchPaymentData
                        | paginate
                          : {
                              id: 'searchPaymentPageData',
                              itemsPerPage: paymentitemsPerPage,
                              currentPage: currentPagePaymentSlab,
                              totalItems: paymenttotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <a
                        href="javascript:void(0)"
                        style="color: #f7b206"
                        (click)="openModal('custmerDetailModal', payment.custId)"
                      >
                        {{ payment.customerName }}
                      </a>
                    </td>
                    <!-- <td>{{ payment.type }}</td> -->
                    <td>
                      <span
                        class="curson_pointer"
                        style="color: #f7b206"
                        (click)="openPaymentInvoiceModal('PaymentDetailModal', payment.id)"
                      >
                        <!-- {{ currency }} {{ payment.amount | number: '1.2-2' }} -->
                        {{
                          payment.amount
                            | currency: payment?.currency || currency : "symbol" : "1.2-2"
                        }}
                      </span>
                    </td>
                    <td>{{ payment.documentno }}</td>
                    <td>{{ payment.referenceno }}</td>
                    <!-- <td>{{ payment.bankName }}</td>
                                    <td>{{ payment.branch }}</td>
                                    <td>{{ payment.chequedate }}</td> -->
                    <td>{{ payment.paymentdate }}</td>
                    <td>{{ payment.createbyname }}</td>
                    <td>{{ payment.remarks }}</td>
                    <td *ngIf="payment.status === 'pending'">
                      <span class="badge badge-info">
                        {{ "Generated" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'rejected'">
                      <span class="badge badge-danger">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'Fully Adjusted'">
                      <span class="badge badge-success">
                        {{ "Adjusted" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'Partialy Adjusted'">
                      <span class="badge badge-success">
                        {{ "Partialy Adjusted" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'Generated'">
                      <span class="badge badge-info">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="payment.status === 'approved'">
                      <span class="badge badge-success">
                        {{ payment.status | titlecase }}
                      </span>
                    </td>
                    <td class="btnAction" *ngIf="reassignAccess || reprintAccess || downloadAccess">
                      <button
                        *ngIf="downloadAccess"
                        id="download-button"
                        type="button"
                        class="approve-btn"
                        (click)="downloadreceipt(payment.id)"
                        title="Download"
                        [disabled]="payment.status === 'pending' || payment.status === 'rejected'"
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/pdf.png"
                        />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        disabled
                        *ngIf="payment.status !== 'Fully Adjusted'"
                        [disabled]="
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'advance' ||
                          payment.status === 'approved' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.status === 'rejected' ||
                          payment.approverid != null
                        "
                        (click)="pickModalOpen(payment)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        type="button"
                        class="approve-btn"
                        (click)="approveModalOpen(payment)"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Approve"
                        [disabled]="
                          payment.status === 'approved' ||
                          payment.status === 'advance' ||
                          payment.status === 'rejected' ||
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'Partially Adjusted' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.approverid != this.staffID
                        "
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>
                      <button
                        type="button"
                        class="approve-btn"
                        (click)="rejectModalOpen(payment)"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Reject"
                        [disabled]="
                          payment.status === 'approved' ||
                          payment.status === 'advance' ||
                          payment.status === 'rejected' ||
                          payment.status === 'Fully Adjusted' ||
                          payment.status === 'Partially Adjusted' ||
                          payment.status === 'Partialy Adjusted' ||
                          payment.approverid != this.staffID
                        "
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>
                      <a
                        class="detailOnAnchorClick"
                        title="Audit & Status Details"
                        (click)="openCreditNoteWorkFlow('custauditWorkflowModal', payment.id)"
                      >
                        <img
                          width="25"
                          height="25"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>
                      <a
                        *ngIf="reprintAccess"
                        (click)="creditNoteReprint(payment.id)"
                        href="javascript:void(0)"
                        title="Reprint CreditNote"
                      >
                        <img src="assets/img/22_Reprint-invoice_Y.png" />
                      </a>

                      <button
                        type="button"
                        class="approve-btn"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        id="assign-button"
                        title="Reassign CreditNote"
                        (click)="StaffReasignList1(payment)"
                        *ngIf="
                          !(
                            payment.status == 'Partialy Adjusted' ||
                            payment.status == 'Fully Adjusted' ||
                            payment.status === 'approved'
                          ) && reassignAccess
                        "
                        [disabled]="payment.approverid !== this.staffID"
                      >
                        <img
                          width="32"
                          height="32"
                          alt="Assign CreditNote"
                          src="assets/img/icons-02.png"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  id="searchPaymentPageData"
                  [maxSize]="10"
                  [directionLinks]="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedPaymentList($event)"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-body table-responsive" *ngIf="searchPaymentData.length === 0">
          Details are not available
        </div>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="assignCustomerCAFModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Assign Credit Note
        </h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="row">
              <div class="col-md-6">
                <input
                  id="searchStaffName"
                  type="text"
                  name="username"
                  class="form-control"
                  placeholder="Global Search Filter"
                  [(ngModel)]="searchStaffDeatil"
                  (keydown.enter)="searchStaffByName()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </div>
              <div class="col-lg-6 col-md-6 col-sm-12">
                <button
                  (click)="searchStaffByName()"
                  class="btn btn-primary"
                  id="searchbtn"
                  type="submit"
                  [disabled]="!searchStaffDeatil"
                >
                  <i class="fa fa-search"></i>
                  Search
                </button>
                <button
                  (click)="clearSearchForm()"
                  class="btn btn-default"
                  id="searchbtn"
                  type="reset"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </div>
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [value]="approveCAF"
                [(selection)]="selectStaff"
                responsiveLayout="scroll"
                [paginator]="true"
                [rows]="5"
                [rowsPerPageOptions]="[5, 10, 15, 20]"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <!-- <input type="file" formControlName="fileName" name="fileName"> -->
      </div>
      <div class="modal-footer">
        <button
          type="submit"
          class="btn btn-primary"
          id="submit"
          (click)="assignCreditnoteToStaff(true)"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="rejectCustomerCAFModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Assign Credit Note
        </h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <h5>Select Staff</h5>
              <p-table
                [value]="rejectCAF"
                [(selection)]="selectStaffReject"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th>Name</th>
                    <th>Username</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <!-- <input type="file" formControlName="fileName" name="fileName"> -->
      </div>
      <div class="modal-footer">
        <button
          type="submit"
          class="btn btn-primary"
          id="assignToStaff"
          (click)="assignCreditnoteToStaff(false)"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="ApproveRejectModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4
          class="modal-title"
          id="myModalLabel"
          style="color: #fff !important"
          *ngIf="ifApproveStatus"
        >
          Approve Credit
        </h4>
        <h4
          class="modal-title"
          id="myModalLabel"
          style="color: #fff !important"
          *ngIf="!ifApproveStatus"
        >
          Reject Credit
        </h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label>Remark*</label>
            <textarea
              [(ngModel)]="approveRejectRemark"
              placeholder="Remarks"
              class="form-control"
              name="remark"
            ></textarea>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          *ngIf="ifApproveStatus"
          (click)="statusApporeved()"
          [disabled]="!approveRejectRemark"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>
        <button
          *ngIf="!ifApproveStatus"
          (click)="statusRejected()"
          [disabled]="!approveRejectRemark"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Save
        </button>

        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>
<p-dialog
  header="Select Customer"
  [(visible)]="reAssignPLANModal"
  [modal]="true"
  [style]="{ width: '80%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <!-- <div class="modal fade" id="selectParentCustomer" role="dialog">
  <div class="modal-dialog" style="width: 80%"> -->
  <!-- Modal content-->
  <!-- <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Select Customer</h3>
      </div> -->
  <div class="modal-body">
    <h5>Search Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selParentSearchOption($event)"
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption != 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchParentCustomer()"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchParentCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="reset"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Customer</h5>
    <p-table
      #dt
      [(selection)]="selectedParentCust"
      [value]="customerList"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template let-customerList let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="customerList"></p-tableRadioButton>
          </td>
          <td>
            {{ customerList.name }}
            {{ customerList.lastname }}
          </td>
          <td>{{ customerList.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator
          (onPageChange)="paginate($event)"
          [first]="newFirst"
          [rows]="parentCustomerListdataitemsPerPage"
          [totalRecords]="parentCustomerListdatatotalRecords"
        ></p-paginator>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
        [disabled]="this.selectedParentCust.length == 0"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
  <!-- </div>
  </div>
</div> -->
</p-dialog>
<p-dialog
  header="Approve Staff"
  [(visible)]="reAssignPLANModalApprove"
  [modal]="true"
  [style]="{ width: '50%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="assignPLANForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <h5>Select Staff</h5>
            <p-table
              [value]="approvableStaff"
              [(selection)]="selectStaff"
              responsiveLayout="scroll"
            >
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 3rem"></th>
                  <th>Name</th>
                  <th>Username</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-product>
                <tr>
                  <td>
                    <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                  </td>
                  <td>{{ product.fullName }}</td>
                  <td>
                    {{ product.username }}
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Remark</label>
          <textarea
            class="form-control"
            name="remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': assignPLANsubmitted && assignPLANForm.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="assignPLANsubmitted && assignPLANForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="assignPLANsubmitted && assignPLANForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="reassignWorkflow()"
      [disabled]="!selectStaff"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>

    <button
      type="button"
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeStaffReasignListForTermination()"
    >
      Close
    </button>
  </div>
</p-dialog>
<div
  class="modal fade"
  id="reAssignPLANModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Approve Plan</h4>
      </div>
    </div>
  </div>
</div>

<app-customer-select
  *ngIf="showParentCustomerModel"
  [type]="custType"
  [selectedCust]="selectedParentCust"
  (selectedCustChange)="selectedCustChange($event)"
  (closeParentCust)="closeCust()"
></app-customer-select>
