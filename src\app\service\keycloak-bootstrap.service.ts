// Create a new file: keycloak-bootstrap.service.ts
import { Injectable } from "@angular/core";
import { KeycloakInstance } from "keycloak-js";
import * as Keycloak from "keycloak-js";

@Injectable({
  providedIn: "root"
})
export class KeycloakBootstrapService {
  private keycloakAuth!: KeycloakInstance;
  private isInitialized = false;

  async initializeKeycloak(): Promise<boolean> {
    if (this.isInitialized) {
      console.log(
        "🔄 Keycloak already initialized, authenticated:",
        this.keycloakAuth?.authenticated
      );
      return this.keycloakAuth?.authenticated || false;
    }

    try {
      console.log("🚀 Bootstrap: Initializing Keycloak");
      console.log("📍 Bootstrap: Current URL:", window.location.href);

      const response = await fetch("/assets/keycloak.json");
      const config = await response.json();
      console.log("📋 Keycloak config:", config);

      this.keycloakAuth = Keycloak(config);

      // ✅ Check if we're returning from Keycloak callback
      const isCallback = this.isKeycloakCallback();
      console.log("🔍 Is Keycloak callback?", isCallback);

      const authenticated = await this.keycloakAuth.init({
        onLoad: "login-required",
        checkLoginIframe: false,
        // ✅ Don't set redirectUri - let Keycloak use the current URL
        // This prevents redirect loops
        flow: "standard"
      });

      this.isInitialized = true;
      console.log("✅ Bootstrap: Keycloak initialized, authenticated:", authenticated);

      // ✅ If authenticated and we have callback parameters, clean the URL
      if (authenticated && isCallback) {
        console.log("🧹 Cleaning callback URL");
        this.cleanCallbackUrl();
      }

      return authenticated;
    } catch (error) {
      console.error("❌ Bootstrap: Keycloak init failed", error);
      this.isInitialized = true;
      return false;
    }
  }

  private isKeycloakCallback(): boolean {
    const url = window.location.href;
    return (
      url.includes("state=") ||
      url.includes("code=") ||
      url.includes("session_state=") ||
      url.includes("iss=")
    );
  }

  private cleanCallbackUrl(): void {
    // ✅ Clean URL after successful authentication
    const cleanUrl = window.location.origin + "/#/home/<USER>";
    console.log("🧹 Bootstrap: Cleaning callback URL to:", cleanUrl);

    // ✅ Use replaceState to avoid triggering navigation events
    window.history.replaceState({}, document.title, cleanUrl);
  }

  getKeycloakInstance(): KeycloakInstance {
    return this.keycloakAuth;
  }

  isLoggedIn(): boolean {
    return !!this.keycloakAuth?.authenticated;
  }

  getToken(): string | undefined {
    return this.keycloakAuth?.token;
  }

  getUsername(): string | undefined {
    return (this.keycloakAuth?.tokenParsed as any)?.preferred_username;
  }

  async updateToken(minValidity: number = 30): Promise<string | undefined> {
    try {
      await this.keycloakAuth.updateToken(minValidity);
      return this.keycloakAuth.token;
    } catch (err) {
      console.error("Failed to refresh token", err);
      return undefined;
    }
  }

  logout(): void {
    this.isInitialized = false;
    this.keycloakAuth?.logout();
  }
}
