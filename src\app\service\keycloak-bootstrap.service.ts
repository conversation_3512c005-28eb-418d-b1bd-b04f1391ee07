// Create a new file: keycloak-bootstrap.service.ts
import { Injectable } from "@angular/core";
import { KeycloakInstance } from "keycloak-js";
import * as Keycloak from "keycloak-js";

@Injectable({
  providedIn: "root"
})
export class KeycloakBootstrapService {
  private keycloakAuth!: KeycloakInstance;
  private isInitialized = false;

  async initializeKeycloak(): Promise<boolean> {
    if (this.isInitialized) {
      console.log(
        "🔄 Keycloak already initialized, authenticated:",
        this.keycloakAuth?.authenticated
      );
      return this.keycloakAuth?.authenticated || false;
    }

    try {
      console.log("🚀 Bootstrap: Initializing Keycloak");
      console.log("📍 Bootstrap: Current URL:", window.location.href);

      // ✅ Clean URL immediately if we detect callback parameters to prevent loops
      const isCallback = this.isKeycloakCallback();
      if (isCallback) {
        console.log("🧹 Pre-cleaning callback URL to prevent loops");
        const cleanUrl = window.location.origin + "/#/home/<USER>";
        window.history.replaceState({}, document.title, cleanUrl);
      }

      const response = await fetch("/assets/keycloak.json");
      const config = await response.json();
      console.log("📋 Keycloak config:", config);

      this.keycloakAuth = Keycloak(config);

      const authenticated = await this.keycloakAuth.init({
        onLoad: "login-required",
        checkLoginIframe: false,
        redirectUri: window.location.origin + "/#/home/<USER>",
        flow: "standard"
      });

      this.isInitialized = true;
      console.log("✅ Bootstrap: Keycloak initialized, authenticated:", authenticated);

      return authenticated;
    } catch (error) {
      console.error("❌ Bootstrap: Keycloak init failed", error);
      this.isInitialized = true;
      return false;
    }
  }

  private isKeycloakCallback(): boolean {
    const url = window.location.href;
    return (
      url.includes("state=") ||
      url.includes("code=") ||
      url.includes("session_state=") ||
      url.includes("iss=")
    );
  }

  getKeycloakInstance(): KeycloakInstance {
    return this.keycloakAuth;
  }

  isLoggedIn(): boolean {
    return !!this.keycloakAuth?.authenticated;
  }

  getToken(): string | undefined {
    return this.keycloakAuth?.token;
  }

  getUsername(): string | undefined {
    return (this.keycloakAuth?.tokenParsed as any)?.preferred_username;
  }

  async updateToken(minValidity: number = 30): Promise<string | undefined> {
    try {
      await this.keycloakAuth.updateToken(minValidity);
      return this.keycloakAuth.token;
    } catch (err) {
      console.error("Failed to refresh token", err);
      return undefined;
    }
  }

  logout(): void {
    this.isInitialized = false;
    this.keycloakAuth?.logout();
  }
}
